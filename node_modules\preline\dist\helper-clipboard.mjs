var t,e={d:(t,o)=>{for(var l in o)e.o(o,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:o[l]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},o={};e.d(o,{A:()=>s});const l=null!==(t=null===window||void 0===window?void 0:window.HS_CLIPBOARD_SELECTOR)&&void 0!==t?t:".js-clipboard";function n(t){document.querySelectorAll(t).forEach((t=>{new ClipboardJS(t,{text:t=>{const e=t.dataset.clipboardText;if(e)return e;const o=t.dataset.clipboardTarget,l=document.querySelector(o);return"SELECT"===l.tagName||"INPUT"===l.tagName||"TEXTAREA"===l.tagName?l.value:l.textContent}}).on("success",(()=>{const e=t.querySelector(".js-clipboard-default"),o=t.querySelector(".js-clipboard-success"),l=t.querySelector(".js-clipboard-success-text"),n=t.dataset.clipboardSuccessText||"",s=t.closest(".hs-tooltip");let a;l&&(a=l.textContent,l.textContent=n),e&&o&&(e.style.display="none",o.style.display="block"),s&&window.HSTooltip.show(s),setTimeout((function(){l&&a&&(l.textContent=a),s&&window.HSTooltip.hide(s),e&&o&&(o.style.display="",e.style.display="")}),800)}))}))}window.addEventListener("load",(()=>{n(l)}));const s=n;var a=o.A;export{a as default};