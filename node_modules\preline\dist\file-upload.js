!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var i=t();for(var o in i)("object"==typeof exports?exports:e)[o]=i[o]}}(self,(()=>(()=>{"use strict";var e={234:function(e,t,i){
/*
 * HSFileUpload
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const n=i(292),l=o(i(961));"undefined"!=typeof Dropzone&&(Dropzone.autoDiscover=!1);class s extends l.default{constructor(e,t,i){var o;super(e,t,i),this.extensions={},this.el="string"==typeof e?document.querySelector(e):e;const n=this.el.getAttribute("data-hs-file-upload"),l=n?JSON.parse(n):{};this.previewTemplate=(null===(o=this.el.querySelector("[data-hs-file-upload-preview]"))||void 0===o?void 0:o.innerHTML)||'<div class="p-3 bg-white border border-solid border-gray-300 rounded-xl dark:bg-neutral-800 dark:border-neutral-600">\n\t\t\t<div class="mb-2 flex justify-between items-center">\n\t\t\t\t<div class="flex items-center gap-x-3">\n\t\t\t\t\t<span class="size-8 flex justify-center items-center border border-gray-200 text-gray-500 rounded-lg dark:border-neutral-700 dark:text-neutral-500" data-hs-file-upload-file-icon></span>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<p class="text-sm font-medium text-gray-800 dark:text-white">\n\t\t\t\t\t\t\t<span class="truncate inline-block max-w-75 align-bottom" data-hs-file-upload-file-name></span>.<span data-hs-file-upload-file-ext></span>\n\t\t\t\t\t\t</p>\n\t\t\t\t\t\t<p class="text-xs text-gray-500 dark:text-neutral-500" data-hs-file-upload-file-size></p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="inline-flex items-center gap-x-2">\n\t\t\t\t\t<button type="button" class="text-gray-500 hover:text-gray-800 dark:text-neutral-500 dark:hover:text-neutral-200" data-hs-file-upload-remove>\n\t\t\t\t\t\t<svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" x2="10" y1="11" y2="17"></line><line x1="14" x2="14" y1="11" y2="17"></line></svg>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="flex items-center gap-x-3 whitespace-nowrap">\n\t\t\t\t<div class="flex w-full h-2 bg-gray-200 rounded-full overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" data-hs-file-upload-progress-bar>\n\t\t\t\t\t<div class="flex flex-col justify-center rounded-full overflow-hidden bg-blue-600 text-xs text-white text-center whitespace-nowrap transition-all duration-500 hs-file-upload-complete:bg-green-600 dark:bg-blue-500" style="width: 0" data-hs-file-upload-progress-bar-pane></div>\n\t\t\t\t</div>\n\t\t\t\t<div class="w-10 text-end">\n\t\t\t\t\t<span class="text-sm text-gray-800 dark:text-white">\n\t\t\t\t\t\t<span data-hs-file-upload-progress-bar-value>0</span>%\n\t\t\t\t\t</span>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>',this.extensions=_.merge({default:{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/></svg>',class:"size-5"},xls:{icon:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.0243 1.43996H7.08805C6.82501 1.43996 6.57277 1.54445 6.38677 1.73043C6.20077 1.91642 6.09631 2.16868 6.09631 2.43171V6.64796L15.0243 11.856L19.4883 13.7398L23.9523 11.856V6.64796L15.0243 1.43996Z" fill="#21A366"></path><path d="M6.09631 6.64796H15.0243V11.856H6.09631V6.64796Z" fill="#107C41"></path><path d="M22.9605 1.43996H15.0243V6.64796H23.9523V2.43171C23.9523 2.16868 23.8478 1.91642 23.6618 1.73043C23.4758 1.54445 23.2235 1.43996 22.9605 1.43996Z" fill="#33C481"></path><path d="M15.0243 11.856H6.09631V21.2802C6.09631 21.5433 6.20077 21.7955 6.38677 21.9815C6.57277 22.1675 6.82501 22.272 7.08805 22.272H22.9606C23.2236 22.272 23.4759 22.1675 23.6618 21.9815C23.8478 21.7955 23.9523 21.5433 23.9523 21.2802V17.064L15.0243 11.856Z" fill="#185C37"></path><path d="M15.0243 11.856H23.9523V17.064H15.0243V11.856Z" fill="#107C41"></path><path opacity="0.1" d="M12.5446 5.15996H6.09631V19.296H12.5446C12.8073 19.2952 13.0591 19.1904 13.245 19.0046C13.4308 18.8188 13.5355 18.567 13.5363 18.3042V6.1517C13.5355 5.88892 13.4308 5.63712 13.245 5.4513C13.0591 5.26548 12.8073 5.16074 12.5446 5.15996Z" fill="black"></path><path opacity="0.2" d="M11.8006 5.90396H6.09631V20.04H11.8006C12.0633 20.0392 12.3151 19.9344 12.501 19.7486C12.6868 19.5628 12.7915 19.311 12.7923 19.0482V6.8957C12.7915 6.6329 12.6868 6.38114 12.501 6.19532C12.3151 6.0095 12.0633 5.90475 11.8006 5.90396Z" fill="black"></path><path opacity="0.2" d="M11.8006 5.90396H6.09631V18.552H11.8006C12.0633 18.5512 12.3151 18.4464 12.501 18.2606C12.6868 18.0748 12.7915 17.823 12.7923 17.5602V6.8957C12.7915 6.6329 12.6868 6.38114 12.501 6.19532C12.3151 6.0095 12.0633 5.90475 11.8006 5.90396Z" fill="black"></path><path opacity="0.2" d="M11.0566 5.90396H6.09631V18.552H11.0566C11.3193 18.5512 11.5711 18.4464 11.757 18.2606C11.9428 18.0748 12.0475 17.823 12.0483 17.5602V6.8957C12.0475 6.6329 11.9428 6.38114 11.757 6.19532C11.5711 6.0095 11.3193 5.90475 11.0566 5.90396Z" fill="black"></path><path d="M1.13604 5.90396H11.0566C11.3195 5.90396 11.5718 6.00842 11.7578 6.19442C11.9438 6.38042 12.0483 6.63266 12.0483 6.8957V16.8162C12.0483 17.0793 11.9438 17.3315 11.7578 17.5175C11.5718 17.7035 11.3195 17.808 11.0566 17.808H1.13604C0.873012 17.808 0.620754 17.7035 0.434765 17.5175C0.248775 17.3315 0.144287 17.0793 0.144287 16.8162V6.8957C0.144287 6.63266 0.248775 6.38042 0.434765 6.19442C0.620754 6.00842 0.873012 5.90396 1.13604 5.90396Z" fill="#107C41"></path><path d="M2.77283 15.576L5.18041 11.8455L2.9752 8.13596H4.74964L5.95343 10.5071C6.06401 10.7318 6.14015 10.8994 6.18185 11.01H6.19745C6.27683 10.8305 6.35987 10.6559 6.44669 10.4863L7.73309 8.13596H9.36167L7.09991 11.8247L9.41897 15.576H7.68545L6.29489 12.972C6.22943 12.861 6.17387 12.7445 6.12899 12.6238H6.10817C6.06761 12.7419 6.01367 12.855 5.94748 12.9608L4.51676 15.576H2.77283Z" fill="white"></path></svg>',class:"size-5"},doc:{icon:'<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M30.6141 1.91994H9.45071C9.09999 1.91994 8.76367 2.05926 8.51567 2.30725C8.26767 2.55523 8.12839 2.89158 8.12839 3.24228V8.86395L20.0324 12.3359L31.9364 8.86395V3.24228C31.9364 2.89158 31.797 2.55523 31.549 2.30725C31.3011 2.05926 30.9647 1.91994 30.6141 1.91994Z" fill="#41A5EE"></path><path d="M31.9364 8.86395H8.12839V15.8079L20.0324 19.2799L31.9364 15.8079V8.86395Z" fill="#2B7CD3"></path><path d="M31.9364 15.8079H8.12839V22.7519L20.0324 26.2239L31.9364 22.7519V15.8079Z" fill="#185ABD"></path><path d="M31.9364 22.752H8.12839V28.3736C8.12839 28.7244 8.26767 29.0607 8.51567 29.3087C8.76367 29.5567 9.09999 29.696 9.45071 29.696H30.6141C30.9647 29.696 31.3011 29.5567 31.549 29.3087C31.797 29.0607 31.9364 28.7244 31.9364 28.3736V22.752Z" fill="#103F91"></path><path opacity="0.1" d="M16.7261 6.87994H8.12839V25.7279H16.7261C17.0764 25.7269 17.4121 25.5872 17.6599 25.3395C17.9077 25.0917 18.0473 24.756 18.0484 24.4056V8.20226C18.0473 7.8519 17.9077 7.51616 17.6599 7.2684C17.4121 7.02064 17.0764 6.88099 16.7261 6.87994Z" class="fill-black dark:fill-neutral-200" fill="currentColor"></path><path opacity="0.2" d="M15.7341 7.87194H8.12839V26.7199H15.7341C16.0844 26.7189 16.4201 26.5792 16.6679 26.3315C16.9157 26.0837 17.0553 25.748 17.0564 25.3976V9.19426C17.0553 8.84386 16.9157 8.50818 16.6679 8.26042C16.4201 8.01266 16.0844 7.87299 15.7341 7.87194Z" class="fill-black dark:fill-neutral-200" fill="currentColor"></path><path opacity="0.2" d="M15.7341 7.87194H8.12839V24.7359H15.7341C16.0844 24.7349 16.4201 24.5952 16.6679 24.3475C16.9157 24.0997 17.0553 23.764 17.0564 23.4136V9.19426C17.0553 8.84386 16.9157 8.50818 16.6679 8.26042C16.4201 8.01266 16.0844 7.87299 15.7341 7.87194Z" class="fill-black dark:fill-neutral-200" fill="currentColor"></path><path opacity="0.2" d="M14.7421 7.87194H8.12839V24.7359H14.7421C15.0924 24.7349 15.4281 24.5952 15.6759 24.3475C15.9237 24.0997 16.0633 23.764 16.0644 23.4136V9.19426C16.0633 8.84386 15.9237 8.50818 15.6759 8.26042C15.4281 8.01266 15.0924 7.87299 14.7421 7.87194Z" class="fill-black dark:fill-neutral-200" fill="currentColor"></path><path d="M1.51472 7.87194H14.7421C15.0927 7.87194 15.4291 8.01122 15.6771 8.25922C15.925 8.50722 16.0644 8.84354 16.0644 9.19426V22.4216C16.0644 22.7723 15.925 23.1087 15.6771 23.3567C15.4291 23.6047 15.0927 23.7439 14.7421 23.7439H1.51472C1.16401 23.7439 0.827669 23.6047 0.579687 23.3567C0.3317 23.1087 0.192383 22.7723 0.192383 22.4216V9.19426C0.192383 8.84354 0.3317 8.50722 0.579687 8.25922C0.827669 8.01122 1.16401 7.87194 1.51472 7.87194Z" fill="#185ABD"></path><path d="M12.0468 20.7679H10.2612L8.17801 13.9231L5.99558 20.7679H4.20998L2.22598 10.8479H4.01158L5.40038 17.7919L7.48358 11.0463H8.97161L10.9556 17.7919L12.3444 10.8479H14.0308L12.0468 20.7679Z" fill="white"></path></svg>',class:"size-5"},zip:{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 22h2a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v18"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/><circle cx="10" cy="20" r="2"/><path d="M10 7V6"/><path d="M10 12v-1"/><path d="M10 18v-2"/></svg>',class:"size-5"}},l.extensions),this.singleton=l.singleton,this.concatOptions=Object.assign(Object.assign({clickable:this.el.querySelector("[data-hs-file-upload-trigger]"),previewsContainer:this.el.querySelector("[data-hs-file-upload-previews]"),addRemoveLinks:!1,previewTemplate:this.previewTemplate,autoHideTrigger:!1},l),t),this.onReloadButtonClickListener=[],this.onTempFileInputChangeListener=[],this.init()}tempFileInputChange(e,t){var i;const o=null===(i=e.target.files)||void 0===i?void 0:i[0];if(o){const e=o;e.status=Dropzone.ADDED,e.accepted=!0,e.previewElement=t.previewElement,e.previewTemplate=t.previewTemplate,e.previewsContainer=t.previewsContainer,this.dropzone.removeFile(t),this.dropzone.addFile(e)}}reloadButtonClick(e,t){e.preventDefault(),e.stopPropagation();const i=document.createElement("input");i.type="file",this.onTempFileInputChangeListener.push({el:i,fn:e=>this.tempFileInputChange(e,t)}),i.click(),i.addEventListener("change",this.onTempFileInputChangeListener.find((e=>e.el===i)).fn)}init(){this.createCollection(window.$hsFileUploadCollection,this),this.initDropzone()}initDropzone(){const e=this.el.querySelector("[data-hs-file-upload-clear]"),t=Array.from(this.el.querySelectorAll("[data-hs-file-upload-pseudo-trigger]"));this.dropzone=new Dropzone(this.el,this.concatOptions),this.dropzone.on("addedfile",(e=>this.onAddFile(e))),this.dropzone.on("removedfile",(()=>this.onRemoveFile())),this.dropzone.on("uploadprogress",((e,t)=>this.onUploadProgress(e,t))),this.dropzone.on("complete",(e=>this.onComplete(e))),e&&(e.onclick=()=>{this.dropzone.files.length&&this.dropzone.removeAllFiles(!0)}),t.length&&t.forEach((e=>{e.onclick=()=>{var e,t;(null===(e=this.concatOptions)||void 0===e?void 0:e.clickable)&&(null===(t=this.concatOptions)||void 0===t?void 0:t.clickable).click()}}))}destroy(){this.onTempFileInputChangeListener.forEach((e=>{e.el.removeEventListener("change",e.fn)})),this.onTempFileInputChangeListener=null,this.onReloadButtonClickListener.forEach((e=>{e.el.removeEventListener("click",e.fn)})),this.onReloadButtonClickListener=null,this.dropzone.destroy(),window.$hsFileUploadCollection=window.$hsFileUploadCollection.filter((({element:e})=>e.el!==this.el))}onAddFile(e){const{previewElement:t}=e,i=e.previewElement.querySelector("[data-hs-file-upload-reload]");if(!t)return!1;this.singleton&&this.dropzone.files.length>1&&this.dropzone.removeFile(this.dropzone.files[0]),i&&(this.onReloadButtonClickListener.push({el:i,fn:t=>this.reloadButtonClick(t,e)}),i.addEventListener("click",this.onReloadButtonClickListener.find((e=>e.el===i)).fn)),this.previewAccepted(e)}previewAccepted(e){const{previewElement:t}=e,i=this.splitFileName(e.name),o=t.querySelector("[data-hs-file-upload-file-name]"),n=t.querySelector("[data-hs-file-upload-file-ext]"),l=t.querySelector("[data-hs-file-upload-file-size]"),s=t.querySelector("[data-hs-file-upload-file-icon]"),a=this.el.querySelector("[data-hs-file-upload-trigger]"),r=t.querySelector("[data-dz-thumbnail]"),d=t.querySelector("[data-hs-file-upload-remove]");o&&(o.textContent=i.name),n&&(n.textContent=i.extension),l&&(l.textContent=this.formatFileSize(e.size)),r&&(e.type.includes("image/")?r.classList.remove("hidden"):this.setIcon(i.extension,s)),this.dropzone.files.length>0&&this.concatOptions.autoHideTrigger&&(a.style.display="none"),d&&(d.onclick=()=>this.dropzone.removeFile(e))}onRemoveFile(){const e=this.el.querySelector("[data-hs-file-upload-trigger]");0===this.dropzone.files.length&&this.concatOptions.autoHideTrigger&&(e.style.display="")}onUploadProgress(e,t){const{previewElement:i}=e;if(!i)return!1;const o=i.querySelector("[data-hs-file-upload-progress-bar]"),n=i.querySelector("[data-hs-file-upload-progress-bar-pane]"),l=i.querySelector("[data-hs-file-upload-progress-bar-value]"),s=Math.floor(t);o&&o.setAttribute("aria-valuenow",`${s}`),n&&(n.style.width=`${s}%`),l&&(l.innerText=`${s}`)}onComplete(e){const{previewElement:t}=e;if(!t)return!1;t.classList.add("complete")}setIcon(e,t){const i=this.createIcon(e);t.append(i)}createIcon(e){var t,i;const o=(null===(t=this.extensions[e])||void 0===t?void 0:t.icon)?(0,n.htmlToElement)(this.extensions[e].icon):(0,n.htmlToElement)(this.extensions.default.icon);return(0,n.classToClassList)((null===(i=this.extensions[e])||void 0===i?void 0:i.class)?this.extensions[e].class:this.extensions.default.class,o),o}formatFileSize(e){return e<1024?e.toFixed(2)+" B":e<1048576?(e/1024).toFixed(2)+" KB":e<1073741824?(e/1048576).toFixed(2)+" MB":e<1099511627776?(e/1073741824).toFixed(2)+" GB":(e/1099511627776).toFixed(2)+" TB"}splitFileName(e){let t=e.lastIndexOf(".");return-1==t?{name:e,extension:""}:{name:e.substring(0,t),extension:e.substring(t+1)}}static getInstance(e,t){const i=window.$hsFileUploadCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return i?t?i:i.element.el:null}static autoInit(){window.$hsFileUploadCollection||(window.$hsFileUploadCollection=[]),window.$hsFileUploadCollection&&(window.$hsFileUploadCollection=window.$hsFileUploadCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll("[data-hs-file-upload]:not(.--prevent-on-load-init)").forEach((e=>{window.$hsFileUploadCollection.find((t=>{var i;return(null===(i=null==t?void 0:t.element)||void 0===i?void 0:i.el)===e}))||new s(e)}))}}window.addEventListener("load",(()=>{document.querySelectorAll("[data-hs-file-upload]:not(.--prevent-on-load-init)").length&&("undefined"==typeof _&&console.error("HSFileUpload: Lodash is not available, please add it to the page."),"undefined"==typeof Dropzone&&console.error("HSFileUpload: Dropzone is not available, please add it to the page.")),"undefined"!=typeof _&&"undefined"!=typeof Dropzone&&s.autoInit()})),"undefined"!=typeof window&&(window.HSFileUpload=s),t.default=s},292:function(e,t){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0;t.stringToBoolean=e=>"true"===e;t.getClassProperty=(e,t,i="")=>(window.getComputedStyle(e).getPropertyValue(t)||i).replace(" ","");t.getClassPropertyAlt=(e,t,i="")=>{let o="";return e.classList.forEach((e=>{e.includes(t)&&(o=e)})),o.match(/:(.*)]/)?o.match(/:(.*)]/)[1]:i};const i=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=i;t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let o=i(e);"auto"!==o&&(o=parseInt(o,10),o>t&&(t=o))})),t};t.isDirectChild=(e,t)=>{const i=e.children;for(let e=0;e<i.length;e++)if(i[e]===t)return!0;return!1};t.isEnoughSpace=(e,t,i="auto",o=10,n=null)=>{const l=t.getBoundingClientRect(),s=n?n.getBoundingClientRect():null,a=window.innerHeight,r=s?l.top-s.top:l.top,d=(n?s.bottom:a)-l.bottom,p=e.clientHeight+o;return"bottom"===i?d>=p:"top"===i?r>=p:r>=p||d>=p};t.isFocused=e=>document.activeElement===e;t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement;t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],i=e.trim().slice(-1);if("{"===t&&"}"===i||"["===t&&"]"===i)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const o=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||o(e.parentElement)};t.isParentOrElementHidden=o;t.isScrollable=e=>{const t=window.getComputedStyle(e),i=t.overflowY,o=t.overflowX,n=("scroll"===i||"auto"===i)&&e.scrollHeight>e.clientHeight,l=("scroll"===o||"auto"===o)&&e.scrollWidth>e.clientWidth;return n||l};t.debounce=(e,t=200)=>{let i;return(...o)=>{clearTimeout(i),i=setTimeout((()=>{e.apply(this,o)}),t)}};t.dispatch=(e,t,i=null)=>{const o=new CustomEvent(e,{detail:{payload:i},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(o)};t.afterTransition=(e,t)=>{const i=()=>{t(),e.removeEventListener("transitionend",i,!0)},o=window.getComputedStyle(e),n=o.getPropertyValue("transition-duration");"none"!==o.getPropertyValue("transition-property")&&parseFloat(n)>0?e.addEventListener("transitionend",i,!0):t()};t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild};t.classToClassList=(e,t,i=" ",o="add")=>{e.split(i).forEach((e=>"add"===o?t.classList.add(e):t.classList.remove(e)))};const n={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=n},961:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t,i){this.el=e,this.options=t,this.events=i,this.el=e,this.options=t,this.events={}}createCollection(e,t){var i;e.push({id:(null===(i=null==t?void 0:t.el)||void 0===i?void 0:i.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={};var i=function i(o){var n=t[o];if(void 0!==n)return n.exports;var l=t[o]={exports:{}};return e[o].call(l.exports,l,l.exports,i),l.exports}(234);return i})()));