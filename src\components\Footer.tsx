import Link from 'next/link'
import { BookOpen, Mail, ExternalLink, GraduationCap, Facebook, Instagram, Linkedin, Phone } from 'lucide-react'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const educationalTopics = [
    { name: 'Rad<PERSON>ie sobie po stracie', href: '/poradniki/radzenie-sobie-po-stracie' },
    { name: 'Procedury po pożarze', href: '/poradniki/procedury-po-pozarze' },
    { name: 'Działania po powodzi', href: '/poradniki/dzialania-po-powodzi' },
    { name: 'Bezpieczeństwo biologiczne', href: '/poradniki/bezpieczenstwo-biologiczne' },
    { name: 'Wsparcie psychologiczne', href: '/poradniki/wsparcie-psychologiczne' },
    { name: 'Aspekt<PERSON> prawne', href: '/poradniki/aspekty-prawne' },
  ]

  const quickLinks = [
    { name: '<PERSON> <PERSON>je<PERSON><PERSON>', href: '/o-projek<PERSON>' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/poradniki' },
    { name: 'Checklisty', href: '/checklisty' },
    { name: 'Case Studies', href: '/case-studies' },
    { name: 'FAQ', href: '/faq' },
    { name: 'Słownik pojęć', href: '/slownik' },
    { name: 'Linki pomocne', href: '/linki' },
  ]

  const legalLinks = [
    { name: 'Polityka prywatności', href: '/polityka-prywatnosci' },
    { name: 'Regulamin', href: '/regulamin' },
    { name: 'Cookies', href: '/cookies' },
  ]

  return (
    <footer className="bg-secondary-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center space-x-4">
              <div className="w-14 h-14 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center shadow-soft">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">VERICTUS</h3>
                <p className="text-secondary-300 font-medium">Edukacja i wsparcie</p>
              </div>
            </div>
            <p className="text-secondary-300 leading-relaxed max-w-md">
              Edukacyjne zaplecze treściowe oferujące poradniki, artykuły, checklisty i case studies
              dla osób dotkniętych trudnymi zdarzeniami. Praktyczna wiedza i wsparcie w trudnych chwilach.
            </p>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-primary-400" />
                <span className="text-secondary-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <GraduationCap className="w-5 h-5 text-primary-400" />
                <span className="text-secondary-300">Praktyczna wiedza i wsparcie</span>
              </div>
            </div>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-xl flex items-center justify-center transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-xl flex items-center justify-center transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-xl flex items-center justify-center transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Educational Topics */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Tematy edukacyjne</h4>
            <ul className="space-y-3">
              {educationalTopics.map((topic, index) => (
                <li key={index}>
                  <Link href={topic.href} className="text-secondary-300 hover:text-primary-400 transition-colors font-medium">
                    {topic.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Szybkie linki</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="text-secondary-300 hover:text-primary-400 transition-colors font-medium">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

        </div>

        {/* Professional Services CTA */}
        <div className="mt-16 pt-12 border-t border-secondary-700">
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-center">
            <h4 className="text-2xl font-bold mb-4 text-white">Potrzebujesz profesjonalnej pomocy?</h4>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto leading-relaxed">
              Jeśli potrzebujesz natychmiastowej, profesjonalnej pomocy w trudnej sytuacji,
              skorzystaj z usług naszego zaufanego partnera.
            </p>
            <a
              href="https://www.solvictus.pl"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-x-3 bg-white text-primary-700 px-8 py-4 rounded-xl font-bold hover:bg-primary-50 transition-colors shadow-soft hover:shadow-medium"
            >
              <span>SOLVICTUS</span>
              <ExternalLink className="w-5 h-5" />
            </a>
            <p className="text-primary-200 mt-4 text-sm">
              Profesjonalne sprzątanie i dezynfekcja po traumatycznych wydarzeniach
            </p>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-secondary-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-secondary-400">
              © {currentYear} VERICTUS. Wszystkie prawa zastrzeżone.
            </div>
            <div className="flex flex-wrap justify-center md:justify-end gap-6">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-secondary-400 hover:text-primary-400 transition-colors text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
