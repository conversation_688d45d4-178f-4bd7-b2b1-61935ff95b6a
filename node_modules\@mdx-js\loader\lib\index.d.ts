/**
 * A Webpack (5+) loader for MDX.
 * See `webpack.cjs`, which wraps this, because Webpack loaders must currently
 * be CommonJS.
 *
 * @this {LoaderContext<unknown>}
 *   Context.
 * @param {string} value
 *   Value.
 * @param {LoaderContext<unknown>['callback']} callback
 *   Callback.
 * @returns {undefined}
 *   Nothing.
 */
export function loader(this: LoaderContext<unknown>, value: string, callback: LoaderContext<unknown>["callback"]): undefined;
/**
 * Configuration (TypeScript type).
 *
 * Options are the same as `compile` from `@mdx-js/mdx` with the exception
 * that the `SourceMapGenerator` and `development` options are supported
 * based on how you configure webpack.
 * You cannot pass them manually.
 */
export type Options = Omit<CompileOptions, "SourceMapGenerator" | "development">;
/**
 * Process.
 */
export type Process = (vfileCompatible: Compatible) => Promise<VFile>;
import type { LoaderContext } from 'webpack';
import type { CompileOptions } from '@mdx-js/mdx';
import type { Compatible } from 'vfile';
import type { VFile } from 'vfile';
//# sourceMappingURL=index.d.ts.map