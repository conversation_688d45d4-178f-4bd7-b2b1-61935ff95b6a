/**
 * Create a file and options from a given `vfileCompatible` and options that
 * might contain `format: 'detect'`.
 *
 * @param {Readonly<Compatible>} vfileCompatible
 *   File.
 * @param {Readonly<CompileOptions> | null | undefined} [options]
 *   Configuration (optional).
 * @returns {{file: VFile, options: ProcessorOptions}}
 *   File and options.
 */
export function resolveFileAndOptions(vfileCompatible: Readonly<Compatible>, options?: Readonly<CompileOptions> | null | undefined): {
    file: VFile;
    options: ProcessorOptions;
};
import type { Compatible } from 'vfile';
import type { CompileOptions } from '../compile.js';
import { VFile } from 'vfile';
import type { ProcessorOptions } from '../core.js';
//# sourceMappingURL=resolve-file-and-options.d.ts.map