import Link from 'next/link'
import { Phone, Mail, MapPin, Shield, Clock, Facebook, Instagram, Linkedin } from 'lucide-react'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const services = [
    'Sprzątanie po zgonach',
    '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> po śmierci',
    'Sprzątanie po pożarach',
    'Usuwanie skutków powodzi',
    'Ozonowanie pomieszczeń',
    'Usuwanie zapachów',
  ]

  const quickLinks = [
    { name: 'O nas', href: '/o-nas' },
    { name: '<PERSON><PERSON>ugi', href: '/uslugi' },
    { name: 'Dla firm', href: '/dla-firm' },
    { name: 'FAQ', href: '/faq' },
    { name: 'Konta<PERSON>', href: '/kontakt' },
    { name: 'Galeria realizacji', href: '/galeria' },
    { name: 'Blog i poradniki', href: '/blog' },
    { name: '<PERSON><PERSON><PERSON>', href: '/opinie' },
    { name: '<PERSON>nn<PERSON> usług', href: '/cennik' },
  ]

  const legalLinks = [
    { name: 'Polityka prywatności', href: '/polityka-prywatnosci' },
    { name: 'Regulamin', href: '/regulamin' },
    { name: 'RODO', href: '/rodo' },
    { name: 'Cookies', href: '/cookies' },
  ]

  return (
    <footer className="bg-primary-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-white rounded-lg flex items-center justify-center">
                <Shield className="w-6 h-6 text-primary-900" />
              </div>
              <div>
                <h3 className="text-xl font-bold">SOLVICTUS</h3>
                <p className="text-sm text-gray-300">Pomagamy po tragedii</p>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              Profesjonalne, certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach. 
              Działamy z dyskrecją, empatią i najwyższą skutecznością.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Nasze usługi</h4>
            <ul className="space-y-2">
              {services.map((service, index) => (
                <li key={index}>
                  <Link href="/uslugi" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {service}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Szybkie linki</h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="text-gray-300 hover:text-white text-sm transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>



          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Kontakt</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium">24/7 Dostępność</p>
                  <p className="text-xs text-gray-300">Całodobowa linia pomocy</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium">+48 123 456 789</p>
                  <p className="text-xs text-gray-300">Natychmiastowa pomoc</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium"><EMAIL></p>
                  <p className="text-xs text-gray-300">Odpowiedź w 24h</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium">Warszawa, Polska</p>
                  <p className="text-xs text-gray-300">Działamy w całym kraju</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-300">
              © {currentYear} SOLVICTUS. Wszystkie prawa zastrzeżone.
            </div>
            <div className="flex flex-wrap justify-center md:justify-end space-x-4 text-xs">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
          <div className="mt-4 text-center">
            <div className="inline-flex items-center space-x-2 text-xs text-gray-400">
              <Shield className="w-4 h-4" />
              <span>Certyfikowane przez Państwowy Zakład Higieny | Licencja nr: PZH/2024/001</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
