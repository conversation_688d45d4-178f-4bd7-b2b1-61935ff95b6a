/**
 * Turn a declaration into an expression.
 *
 * Doesn’t work for variable declarations, but that’s fine for our use case
 * because currently we’re using this utility for export default declarations,
 * which can’t contain variable declarations.
 *
 * @param {Readonly<Declaration | MaybeNamedClassDeclaration | MaybeNamedFunctionDeclaration>} declaration
 *   Declaration.
 * @returns {Expression}
 *   Expression.
 */
export function declarationToExpression(declaration: Readonly<Declaration | MaybeNamedClassDeclaration | MaybeNamedFunctionDeclaration>): Expression;
import type { Declaration } from 'estree-jsx';
import type { MaybeNamedClassDeclaration } from 'estree-jsx';
import type { MaybeNamedFunctionDeclaration } from 'estree-jsx';
import type { Expression } from 'estree-jsx';
//# sourceMappingURL=estree-util-declaration-to-expression.d.ts.map