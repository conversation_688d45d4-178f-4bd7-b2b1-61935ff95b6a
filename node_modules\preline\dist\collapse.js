!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var i in n)("object"==typeof exports?exports:e)[i]=n[i]}}(self,(()=>(()=>{"use strict";var e={292:function(e,t){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0;t.stringToBoolean=e=>"true"===e;t.getClassProperty=(e,t,n="")=>(window.getComputedStyle(e).getPropertyValue(t)||n).replace(" ","");t.getClassPropertyAlt=(e,t,n="")=>{let i="";return e.classList.forEach((e=>{e.includes(t)&&(i=e)})),i.match(/:(.*)]/)?i.match(/:(.*)]/)[1]:n};const n=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=n;t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let i=n(e);"auto"!==i&&(i=parseInt(i,10),i>t&&(t=i))})),t};t.isDirectChild=(e,t)=>{const n=e.children;for(let e=0;e<n.length;e++)if(n[e]===t)return!0;return!1};t.isEnoughSpace=(e,t,n="auto",i=10,s=null)=>{const o=t.getBoundingClientRect(),l=s?s.getBoundingClientRect():null,r=window.innerHeight,a=l?o.top-l.top:o.top,c=(s?l.bottom:r)-o.bottom,d=e.clientHeight+i;return"bottom"===n?c>=d:"top"===n?a>=d:a>=d||c>=d};t.isFocused=e=>document.activeElement===e;t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement;t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],n=e.trim().slice(-1);if("{"===t&&"}"===n||"["===t&&"]"===n)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const i=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||i(e.parentElement)};t.isParentOrElementHidden=i;t.isScrollable=e=>{const t=window.getComputedStyle(e),n=t.overflowY,i=t.overflowX,s=("scroll"===n||"auto"===n)&&e.scrollHeight>e.clientHeight,o=("scroll"===i||"auto"===i)&&e.scrollWidth>e.clientWidth;return s||o};t.debounce=(e,t=200)=>{let n;return(...i)=>{clearTimeout(n),n=setTimeout((()=>{e.apply(this,i)}),t)}};t.dispatch=(e,t,n=null)=>{const i=new CustomEvent(e,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(i)};t.afterTransition=(e,t)=>{const n=()=>{t(),e.removeEventListener("transitionend",n,!0)},i=window.getComputedStyle(e),s=i.getPropertyValue("transition-duration");"none"!==i.getPropertyValue("transition-property")&&parseFloat(s)>0?e.addEventListener("transitionend",n,!0):t()};t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild};t.classToClassList=(e,t,n=" ",i="add")=>{e.split(n).forEach((e=>"add"===i?t.classList.add(e):t.classList.remove(e)))};const s={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=s},485:function(e,t,n){
/*
 * HSCollapse
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=n(292),o=i(n(961));class l extends o.default{constructor(e,t,n){super(e,t,n),this.contentId=this.el.dataset.hsCollapse,this.content=document.querySelector(this.contentId),this.animationInProcess=!1,this.content&&this.init()}elementClick(){this.content.classList.contains("open")?this.hide():this.show()}init(){var e;this.createCollection(window.$hsCollapseCollection,this),this.onElementClickListener=()=>this.elementClick(),(null===(e=null==this?void 0:this.el)||void 0===e?void 0:e.ariaExpanded)&&(this.el.classList.contains("open")?this.el.ariaExpanded="true":this.el.ariaExpanded="false"),this.el.addEventListener("click",this.onElementClickListener)}hideAllMegaMenuItems(){this.content.querySelectorAll(".hs-mega-menu-content.block").forEach((e=>{e.classList.remove("block"),e.classList.add("hidden")}))}show(){var e;if(this.animationInProcess||this.el.classList.contains("open"))return!1;this.animationInProcess=!0,this.el.classList.add("open"),(null===(e=null==this?void 0:this.el)||void 0===e?void 0:e.ariaExpanded)&&(this.el.ariaExpanded="true"),this.content.classList.add("open"),this.content.classList.remove("hidden"),this.content.style.height="0",setTimeout((()=>{this.content.style.height=`${this.content.scrollHeight}px`,this.fireEvent("beforeOpen",this.el),(0,s.dispatch)("beforeOpen.hs.collapse",this.el,this.el)})),(0,s.afterTransition)(this.content,(()=>{this.content.style.height="",this.fireEvent("open",this.el),(0,s.dispatch)("open.hs.collapse",this.el,this.el),this.animationInProcess=!1}))}hide(){var e;if(this.animationInProcess||!this.el.classList.contains("open"))return!1;this.animationInProcess=!0,this.el.classList.remove("open"),(null===(e=null==this?void 0:this.el)||void 0===e?void 0:e.ariaExpanded)&&(this.el.ariaExpanded="false"),this.content.style.height=`${this.content.scrollHeight}px`,setTimeout((()=>{this.content.style.height="0"})),this.content.classList.remove("open"),(0,s.afterTransition)(this.content,(()=>{this.content.classList.add("hidden"),this.content.style.height="",this.fireEvent("hide",this.el),(0,s.dispatch)("hide.hs.collapse",this.el,this.el),this.animationInProcess=!1})),this.content.querySelectorAll(".hs-mega-menu-content.block").length&&this.hideAllMegaMenuItems()}destroy(){this.el.removeEventListener("click",this.onElementClickListener),this.content=null,this.animationInProcess=!1,window.$hsCollapseCollection=window.$hsCollapseCollection.filter((({element:e})=>e.el!==this.el))}static findInCollection(e){return window.$hsCollapseCollection.find((t=>e instanceof l?t.element.el===e.el:"string"==typeof e?t.element.el===document.querySelector(e):t.element.el===e))||null}static getInstance(e,t=!1){const n=window.$hsCollapseCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return n?t?n:n.element.el:null}static autoInit(){window.$hsCollapseCollection||(window.$hsCollapseCollection=[]),window.$hsCollapseCollection&&(window.$hsCollapseCollection=window.$hsCollapseCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll(".hs-collapse-toggle:not(.--prevent-on-load-init)").forEach((e=>{window.$hsCollapseCollection.find((t=>{var n;return(null===(n=null==t?void 0:t.element)||void 0===n?void 0:n.el)===e}))||new l(e)}))}static show(e){const t=l.findInCollection(e);t&&t.element.content.classList.contains("hidden")&&t.element.show()}static hide(e){const t=l.findInCollection(e);t&&!t.element.content.classList.contains("hidden")&&t.element.hide()}static on(e,t,n){const i=l.findInCollection(t);i&&(i.element.events[e]=n)}}window.addEventListener("load",(()=>{l.autoInit()})),"undefined"!=typeof window&&(window.HSCollapse=l),t.default=l},961:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t,n){this.el=e,this.options=t,this.events=n,this.el=e,this.options=t,this.events={}}createCollection(e,t){var n;e.push({id:(null===(n=null==t?void 0:t.el)||void 0===n?void 0:n.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={};var n=function n(i){var s=t[i];if(void 0!==s)return s.exports;var o=t[i]={exports:{}};return e[i].call(o.exports,o,o.exports,n),o.exports}(485);return n})()));