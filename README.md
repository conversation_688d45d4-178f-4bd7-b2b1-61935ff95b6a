# VERICTUS - Edukacyjne zaplecze treściowe

VERICTUS to nowoczesna strona edukacyjna stworzona z myślą o osobach znajdujących się w trudnych sytuacjach życiowych. Projekt oferuje praktyczne poradniki, checklisty, case studies i inne materiały pomocne w radzeniu sobie z traumatycznymi wydarzeniami.

## 🚀 Technologie

- **Next.js 14** z App Router
- **TypeScript** dla bezpieczeństwa typów
- **Tailwind CSS** z Preline UI dla stylizacji
- **MDX** dla treści edukacyjnych
- **Static Site Generation (SSG)** dla optymalnej wydajności

## 📦 Instalacja

```bash
# Klonowanie repozytorium
git clone [repository-url]
cd verictus

# Instalacja zależności
npm install

# Uruchomienie w trybie deweloperskim
npm run dev
```

## 🏗️ Budowanie

### Tryb deweloperski
```bash
npm run dev
```
Uruchamia serwer deweloperski na http://localhost:3000

### Budowanie statyczne (SSG)
```bash
npm run build:static
```
Tworzy statyczną wersję strony w folderze `out/`

### Serwowanie statycznej wersji
```bash
npm run serve:static
```
Uruchamia lokalny serwer HTTP dla statycznej wersji na http://localhost:8080

## 📁 Struktura projektu

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Layout główny
│   ├── page.tsx           # Strona główna
│   ├── globals.css        # Style globalne
│   ├── poradniki/         # Strona poradników
│   ├── checklisty/        # Strona checklisty
│   ├── case-studies/      # Strona case studies
│   ├── faq/               # FAQ
│   ├── o-projekcie/       # O projekcie
│   └── ...                # Inne podstrony
├── components/            # Komponenty React
│   ├── Header.tsx         # Nagłówek
│   ├── Footer.tsx         # Stopka
│   ├── HeroSection.tsx    # Sekcja hero
│   └── ...                # Inne komponenty
content/
├── poradniki/             # Treści MDX poradników
public/
├── images/                # Obrazy i media
└── site.webmanifest      # Manifest PWA
```

## 🎨 Design System

Projekt wykorzystuje jasną, ekspercką estetykę z następującymi elementami:

- **Czcionka:** Open Sans
- **Kolory główne:** Niebieski (primary), Szary (secondary), Neutralny (base)
- **Komponenty:** Preline UI z Tailwind CSS
- **Cienie:** Soft, medium, large
- **Zaokrąglenia:** xl, 2xl, 3xl

## 📄 Strony

- **`/`** - Strona główna z przeglądem wszystkich sekcji
- **`/poradniki`** - Biblioteka szczegółowych poradników
- **`/checklisty`** - Listy kontrolne do pobrania
- **`/case-studies`** - Rzeczywiste historie i przykłady
- **`/faq`** - Często zadawane pytania
- **`/o-projekcie`** - Informacje o projekcie i zespole
- **`/zasoby`** - Centrum wszystkich materiałów
- **`/slownik`** - Słownik specjalistycznych pojęć
- **`/linki`** - Kontakty do organizacji pomocowych

## 🔧 Konfiguracja SSG

Projekt jest w pełni skonfigurowany do eksportu statycznego:

### next.config.mjs
```javascript
const nextConfig = {
  output: 'export',           // Eksport statyczny
  trailingSlash: true,        // Trailing slash dla kompatybilności
  images: {
    unoptimized: true,        // Wyłączenie optymalizacji obrazów
  },
}
```

### Cechy SSG
- ✅ Brak API routes
- ✅ Brak server-side funkcji
- ✅ Wszystkie strony są statyczne
- ✅ Obrazy są nieoptymalizowane (kompatybilność z CDN)
- ✅ Trailing slash dla lepszej kompatybilności z serwerami

## 🚀 Deployment

### Statyczny hosting (zalecane)
1. Zbuduj projekt: `npm run build:static`
2. Upload folder `out/` na dowolny statyczny hosting:
   - Netlify
   - Vercel
   - GitHub Pages
   - AWS S3 + CloudFront
   - Azure Static Web Apps

### Serwer HTTP
Folder `out/` można serwować przez dowolny serwer HTTP:
```bash
# Python
cd out && python -m http.server 8080

# Node.js (serve)
npx serve out

# Apache/Nginx
# Skopiuj zawartość out/ do document root
```

## 🔗 Linki zewnętrzne

Projekt zawiera linki do **SOLVICTUS** (https://www.solvictus.pl) - partnera oferującego profesjonalne usługi sprzątania po traumatycznych wydarzeniach.

## 📝 Treści

Wszystkie materiały edukacyjne są:
- Sprawdzone przez ekspertów
- Regularnie aktualizowane
- Dostępne za darmo
- Zgodne z najlepszymi praktykami

## 🤝 Wsparcie

W przypadku pytań lub problemów:
- Email: <EMAIL>
- Sprawdź sekcję FAQ na stronie
- Skorzystaj z linków do organizacji pomocowych

## 📄 Licencja

Projekt ma charakter edukacyjny i non-profit. Wszystkie treści są dostępne do niekomercyjnego użytku z zachowaniem informacji o źródle.

---

**VERICTUS** - Wiedza i wsparcie w trudnych chwilach
