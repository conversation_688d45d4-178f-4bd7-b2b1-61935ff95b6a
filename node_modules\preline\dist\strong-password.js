!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var s=e();for(var i in s)("object"==typeof exports?exports:t)[i]=s[i]}}(self,(()=>(()=>{"use strict";var t={97:function(t,e,s){
/*
 * HSStrongPassword
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const n=s(292),r=i(s(961));class o extends r.default{constructor(t,e){super(t,e),this.isOpened=!1,this.strength=0,this.passedRules=new Set;const s=t.getAttribute("data-hs-strong-password"),i=s?JSON.parse(s):{},n=Object.assign(Object.assign({},i),e);this.target=(null==n?void 0:n.target)?"string"==typeof(null==n?void 0:n.target)?document.querySelector(n.target):n.target:null,this.hints=(null==n?void 0:n.hints)?"string"==typeof(null==n?void 0:n.hints)?document.querySelector(n.hints):n.hints:null,this.stripClasses=(null==n?void 0:n.stripClasses)||null,this.minLength=(null==n?void 0:n.minLength)||6,this.mode=(null==n?void 0:n.mode)||"default",this.popoverSpace=(null==n?void 0:n.popoverSpace)||10,this.checksExclude=(null==n?void 0:n.checksExclude)||[],this.availableChecks=["lowercase","uppercase","numbers","special-characters","min-length"].filter((t=>!this.checksExclude.includes(t))),this.specialCharactersSet=(null==n?void 0:n.specialCharactersSet)||"!\"#$%&'()*+,-./:;<=>?@[\\\\\\]^_`{|}~",this.target&&this.init()}targetInput(t){this.setStrength(t.target.value)}targetFocus(){this.isOpened=!0,this.hints.classList.remove("hidden"),this.hints.classList.add("block"),this.recalculateDirection()}targetBlur(){this.isOpened=!1,this.hints.classList.remove("block","bottom-full","top-full"),this.hints.classList.add("hidden"),this.hints.style.marginTop="",this.hints.style.marginBottom=""}targetInputSecond(){this.setWeaknessText()}targetInputThird(){this.setRulesText()}init(){this.createCollection(window.$hsStrongPasswordCollection,this),this.availableChecks.length&&this.build()}build(){this.buildStrips(),this.hints&&this.buildHints(),this.setStrength(this.target.value),this.onTargetInputListener=t=>this.targetInput(t),this.target.addEventListener("input",this.onTargetInputListener)}buildStrips(){if(this.el.innerHTML="",this.stripClasses)for(let t=0;t<this.availableChecks.length;t++){const t=(0,n.htmlToElement)("<div></div>");(0,n.classToClassList)(this.stripClasses,t),this.el.append(t)}}buildHints(){this.weakness=this.hints.querySelector("[data-hs-strong-password-hints-weakness-text]")||null,this.rules=Array.from(this.hints.querySelectorAll("[data-hs-strong-password-hints-rule-text]"))||null,this.rules.forEach((t=>{var e;const s=t.getAttribute("data-hs-strong-password-hints-rule-text");(null===(e=this.checksExclude)||void 0===e?void 0:e.includes(s))&&t.remove()})),this.weakness&&this.buildWeakness(),this.rules&&this.buildRules(),"popover"===this.mode&&(this.onTargetFocusListener=()=>this.targetFocus(),this.onTargetBlurListener=()=>this.targetBlur(),this.target.addEventListener("focus",this.onTargetFocusListener),this.target.addEventListener("blur",this.onTargetBlurListener))}buildWeakness(){this.checkStrength(this.target.value),this.setWeaknessText(),this.onTargetInputSecondListener=()=>setTimeout((()=>this.targetInputSecond())),this.target.addEventListener("input",this.onTargetInputSecondListener)}buildRules(){this.setRulesText(),this.onTargetInputThirdListener=()=>setTimeout((()=>this.targetInputThird())),this.target.addEventListener("input",this.onTargetInputThirdListener)}setWeaknessText(){const t=this.weakness.getAttribute("data-hs-strong-password-hints-weakness-text"),e=JSON.parse(t);this.weakness.textContent=e[this.strength]}setRulesText(){this.rules.forEach((t=>{const e=t.getAttribute("data-hs-strong-password-hints-rule-text");this.checkIfPassed(t,this.passedRules.has(e))}))}togglePopover(){const t=this.el.querySelector(".popover");t&&t.classList.toggle("show")}checkStrength(t){const e=new Set,s={lowercase:/[a-z]+/,uppercase:/[A-Z]+/,numbers:/[0-9]+/,"special-characters":new RegExp(`[${this.specialCharactersSet}]`)};let i=0;return this.availableChecks.includes("lowercase")&&t.match(s.lowercase)&&(i+=1,e.add("lowercase")),this.availableChecks.includes("uppercase")&&t.match(s.uppercase)&&(i+=1,e.add("uppercase")),this.availableChecks.includes("numbers")&&t.match(s.numbers)&&(i+=1,e.add("numbers")),this.availableChecks.includes("special-characters")&&t.match(s["special-characters"])&&(i+=1,e.add("special-characters")),this.availableChecks.includes("min-length")&&t.length>=this.minLength&&(i+=1,e.add("min-length")),t.length||(i=0),i===this.availableChecks.length?this.el.classList.add("accepted"):this.el.classList.remove("accepted"),this.strength=i,this.passedRules=e,{strength:this.strength,rules:this.passedRules}}checkIfPassed(t,e=!1){const s=t.querySelector("[data-check]"),i=t.querySelector("[data-uncheck]");e?(t.classList.add("active"),s.classList.remove("hidden"),i.classList.add("hidden")):(t.classList.remove("active"),s.classList.add("hidden"),i.classList.remove("hidden"))}setStrength(t){const{strength:e,rules:s}=this.checkStrength(t),i={strength:e,rules:s};this.hideStrips(e),this.fireEvent("change",i),(0,n.dispatch)("change.hs.strongPassword",this.el,i)}hideStrips(t){Array.from(this.el.children).forEach(((e,s)=>{s<t?e.classList.add("passed"):e.classList.remove("passed")}))}recalculateDirection(){(0,n.isEnoughSpace)(this.hints,this.target,"bottom",this.popoverSpace)?(this.hints.classList.remove("bottom-full"),this.hints.classList.add("top-full"),this.hints.style.marginBottom="",this.hints.style.marginTop=`${this.popoverSpace}px`):(this.hints.classList.remove("top-full"),this.hints.classList.add("bottom-full"),this.hints.style.marginTop="",this.hints.style.marginBottom=`${this.popoverSpace}px`)}destroy(){this.target.removeEventListener("input",this.onTargetInputListener),this.target.removeEventListener("focus",this.onTargetFocusListener),this.target.removeEventListener("blur",this.onTargetBlurListener),this.target.removeEventListener("input",this.onTargetInputSecondListener),this.target.removeEventListener("input",this.onTargetInputThirdListener),window.$hsStrongPasswordCollection=window.$hsStrongPasswordCollection.filter((({element:t})=>t.el!==this.el))}static getInstance(t,e){const s=window.$hsStrongPasswordCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)));return s?e?s:s.element.el:null}static autoInit(){window.$hsStrongPasswordCollection||(window.$hsStrongPasswordCollection=[]),window.$hsStrongPasswordCollection&&(window.$hsStrongPasswordCollection=window.$hsStrongPasswordCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll("[data-hs-strong-password]:not(.--prevent-on-load-init)").forEach((t=>{if(!window.$hsStrongPasswordCollection.find((e=>{var s;return(null===(s=null==e?void 0:e.element)||void 0===s?void 0:s.el)===t}))){const e=t.getAttribute("data-hs-strong-password"),s=e?JSON.parse(e):{};new o(t,s)}}))}}window.addEventListener("load",(()=>{o.autoInit()})),document.addEventListener("scroll",(()=>{if(!window.$hsStrongPasswordCollection)return!1;const t=window.$hsStrongPasswordCollection.find((t=>t.element.isOpened));t&&t.element.recalculateDirection()})),"undefined"!=typeof window&&(window.HSStrongPassword=o),e.default=o},292:function(t,e){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(e,"__esModule",{value:!0}),e.menuSearchHistory=e.classToClassList=e.htmlToElement=e.afterTransition=e.dispatch=e.debounce=e.isScrollable=e.isParentOrElementHidden=e.isJson=e.isIpadOS=e.isIOS=e.isDirectChild=e.isFormElement=e.isFocused=e.isEnoughSpace=e.getHighestZIndex=e.getZIndex=e.getClassPropertyAlt=e.getClassProperty=e.stringToBoolean=void 0;e.stringToBoolean=t=>"true"===t;e.getClassProperty=(t,e,s="")=>(window.getComputedStyle(t).getPropertyValue(e)||s).replace(" ","");e.getClassPropertyAlt=(t,e,s="")=>{let i="";return t.classList.forEach((t=>{t.includes(e)&&(i=t)})),i.match(/:(.*)]/)?i.match(/:(.*)]/)[1]:s};const s=t=>window.getComputedStyle(t).getPropertyValue("z-index");e.getZIndex=s;e.getHighestZIndex=t=>{let e=Number.NEGATIVE_INFINITY;return t.forEach((t=>{let i=s(t);"auto"!==i&&(i=parseInt(i,10),i>e&&(e=i))})),e};e.isDirectChild=(t,e)=>{const s=t.children;for(let t=0;t<s.length;t++)if(s[t]===e)return!0;return!1};e.isEnoughSpace=(t,e,s="auto",i=10,n=null)=>{const r=e.getBoundingClientRect(),o=n?n.getBoundingClientRect():null,a=window.innerHeight,l=o?r.top-o.top:r.top,h=(n?o.bottom:a)-r.bottom,c=t.clientHeight+i;return"bottom"===s?h>=c:"top"===s?l>=c:l>=c||h>=c};e.isFocused=t=>document.activeElement===t;e.isFormElement=t=>t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement;e.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isJson=t=>{if("string"!=typeof t)return!1;const e=t.trim()[0],s=t.trim().slice(-1);if("{"===e&&"}"===s||"["===e&&"]"===s)try{return JSON.parse(t),!0}catch(t){return!1}return!1};const i=t=>{if(!t)return!1;return"none"===window.getComputedStyle(t).display||i(t.parentElement)};e.isParentOrElementHidden=i;e.isScrollable=t=>{const e=window.getComputedStyle(t),s=e.overflowY,i=e.overflowX,n=("scroll"===s||"auto"===s)&&t.scrollHeight>t.clientHeight,r=("scroll"===i||"auto"===i)&&t.scrollWidth>t.clientWidth;return n||r};e.debounce=(t,e=200)=>{let s;return(...i)=>{clearTimeout(s),s=setTimeout((()=>{t.apply(this,i)}),e)}};e.dispatch=(t,e,s=null)=>{const i=new CustomEvent(t,{detail:{payload:s},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(i)};e.afterTransition=(t,e)=>{const s=()=>{e(),t.removeEventListener("transitionend",s,!0)},i=window.getComputedStyle(t),n=i.getPropertyValue("transition-duration");"none"!==i.getPropertyValue("transition-property")&&parseFloat(n)>0?t.addEventListener("transitionend",s,!0):e()};e.htmlToElement=t=>{const e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild};e.classToClassList=(t,e,s=" ",i="add")=>{t.split(s).forEach((t=>"add"===i?e.classList.add(t):e.classList.remove(t)))};const n={historyIndex:-1,addHistory(t){this.historyIndex=t},existsInHistory(t){return t>this.historyIndex},clearHistory(){this.historyIndex=-1}};e.menuSearchHistory=n},961:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(t,e,s){this.el=t,this.options=e,this.events=s,this.el=t,this.options=e,this.events={}}createCollection(t,e){var s;t.push({id:(null===(s=null==e?void 0:e.el)||void 0===s?void 0:s.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}}},e={};var s=function s(i){var n=e[i];if(void 0!==n)return n.exports;var r=e[i]={exports:{}};return t[i].call(r.exports,r,r.exports,s),r.exports}(97);return s})()));