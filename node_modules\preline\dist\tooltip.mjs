var t={189:(t,e,n)=>{n.d(e,{lP:()=>o});const o={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"}},615:(t,e,n)=>{n.d(e,{A:()=>o});class o{constructor(t,e,n){this.el=t,this.options=e,this.events=n,this.el=t,this.options=e,this.events={}}createCollection(t,e){var n;t.push({id:(null===(n=null==e?void 0:e.el)||void 0===n?void 0:n.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}},663:(t,e,n)=>{n.d(e,{ll:()=>K,rD:()=>Z,cY:()=>Q});const o=Math.min,i=Math.max,s=Math.round,l=Math.floor,r=t=>({x:t,y:t});function c(t){return t.split("-")[0]}function a(t){return t.split("-")[1]}function h(t){return"y"===t?"height":"width"}function u(t){return["top","bottom"].includes(c(t))?"y":"x"}function d(t){return"x"===u(t)?"y":"x"}function f(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function g(t,e,n){let{reference:o,floating:i}=t;const s=u(e),l=d(e),r=h(l),f=c(e),g="y"===s,p=o.x+o.width/2-i.width/2,m=o.y+o.height/2-i.height/2,v=o[r]/2-i[r]/2;let w;switch(f){case"top":w={x:p,y:o.y-i.height};break;case"bottom":w={x:p,y:o.y+o.height};break;case"right":w={x:o.x+o.width,y:m};break;case"left":w={x:o.x-i.width,y:m};break;default:w={x:o.x,y:o.y}}switch(a(e)){case"start":w[l]-=v*(n&&g?-1:1);break;case"end":w[l]+=v*(n&&g?-1:1)}return w}function p(){return"undefined"!=typeof window}function m(t){return y(t)?(t.nodeName||"").toLowerCase():"#document"}function v(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function w(t){var e;return null==(e=(y(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function y(t){return!!p()&&(t instanceof Node||t instanceof v(t).Node)}function x(t){return!!p()&&(t instanceof Element||t instanceof v(t).Element)}function L(t){return!!p()&&(t instanceof HTMLElement||t instanceof v(t).HTMLElement)}function b(t){return!(!p()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof v(t).ShadowRoot)}function T(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=F(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function E(t){return["table","td","th"].includes(m(t))}function C(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(t){return!1}}))}function A(t){const e=k(),n=x(t)?F(t):t;return["transform","translate","scale","rotate","perspective"].some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function k(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function R(t){return["html","body","#document"].includes(m(t))}function F(t){return v(t).getComputedStyle(t)}function M(t){return x(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function S(t){if("html"===m(t))return t;const e=t.assignedSlot||t.parentNode||b(t)&&t.host||w(t);return b(e)?e.host:e}function D(t){const e=S(t);return R(e)?t.ownerDocument?t.ownerDocument.body:t.body:L(e)&&T(e)?e:D(e)}function H(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=D(t),s=i===(null==(o=t.ownerDocument)?void 0:o.body),l=v(i);if(s){const t=I(l);return e.concat(l,l.visualViewport||[],T(i)?i:[],t&&n?H(t):[])}return e.concat(i,H(i,[],n))}function I(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function O(t){const e=F(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=L(t),l=i?t.offsetWidth:n,r=i?t.offsetHeight:o,c=s(n)!==l||s(o)!==r;return c&&(n=l,o=r),{width:n,height:o,$:c}}function $(t){return x(t)?t:t.contextElement}function P(t){const e=$(t);if(!L(e))return r(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:l}=O(e);let c=(l?s(n.width):n.width)/o,a=(l?s(n.height):n.height)/i;return c&&Number.isFinite(c)||(c=1),a&&Number.isFinite(a)||(a=1),{x:c,y:a}}const U=r(0);function j(t){const e=v(t);return k()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:U}function V(t,e,n,o){void 0===e&&(e=!1),void 0===n&&(n=!1);const i=t.getBoundingClientRect(),s=$(t);let l=r(1);e&&(o?x(o)&&(l=P(o)):l=P(t));const c=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==v(t))&&e}(s,n,o)?j(s):r(0);let a=(i.left+c.x)/l.x,h=(i.top+c.y)/l.y,u=i.width/l.x,d=i.height/l.y;if(s){const t=v(s),e=o&&x(o)?v(o):o;let n=t,i=I(n);for(;i&&o&&e!==n;){const t=P(i),e=i.getBoundingClientRect(),o=F(i),s=e.left+(i.clientLeft+parseFloat(o.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(o.paddingTop))*t.y;a*=t.x,h*=t.y,u*=t.x,d*=t.y,a+=s,h+=l,n=v(i),i=I(n)}}return f({width:u,height:d,x:a,y:h})}function W(t,e){const n=M(t).scrollLeft;return e?e.left+n:V(w(t)).left+n}function B(t,e,n){void 0===n&&(n=!1);const o=t.getBoundingClientRect();return{x:o.left+e.scrollLeft-(n?0:W(t,o)),y:o.top+e.scrollTop}}function q(t,e,n){let o;if("viewport"===e)o=function(t,e){const n=v(t),o=w(t),i=n.visualViewport;let s=o.clientWidth,l=o.clientHeight,r=0,c=0;if(i){s=i.width,l=i.height;const t=k();(!t||t&&"fixed"===e)&&(r=i.offsetLeft,c=i.offsetTop)}return{width:s,height:l,x:r,y:c}}(t,n);else if("document"===e)o=function(t){const e=w(t),n=M(t),o=t.ownerDocument.body,s=i(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),l=i(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let r=-n.scrollLeft+W(t);const c=-n.scrollTop;return"rtl"===F(o).direction&&(r+=i(e.clientWidth,o.clientWidth)-s),{width:s,height:l,x:r,y:c}}(w(t));else if(x(e))o=function(t,e){const n=V(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,s=L(t)?P(t):r(1);return{width:t.clientWidth*s.x,height:t.clientHeight*s.y,x:i*s.x,y:o*s.y}}(e,n);else{const n=j(t);o={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return f(o)}function z(t,e){const n=S(t);return!(n===e||!x(n)||R(n))&&("fixed"===F(n).position||z(n,e))}function N(t,e,n){const o=L(e),i=w(e),s="fixed"===n,l=V(t,!0,s,e);let c={scrollLeft:0,scrollTop:0};const a=r(0);if(o||!o&&!s)if(("body"!==m(e)||T(i))&&(c=M(e)),o){const t=V(e,!0,s,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else i&&(a.x=W(i));const h=!i||o||s?r(0):B(i,c);return{x:l.left+c.scrollLeft-a.x-h.x,y:l.top+c.scrollTop-a.y-h.y,width:l.width,height:l.height}}function _(t){return"static"===F(t).position}function Y(t,e){if(!L(t)||"fixed"===F(t).position)return null;if(e)return e(t);let n=t.offsetParent;return w(t)===n&&(n=n.ownerDocument.body),n}function J(t,e){const n=v(t);if(C(t))return n;if(!L(t)){let e=S(t);for(;e&&!R(e);){if(x(e)&&!_(e))return e;e=S(e)}return n}let o=Y(t,e);for(;o&&E(o)&&_(o);)o=Y(o,e);return o&&R(o)&&_(o)&&!A(o)?n:o||function(t){let e=S(t);for(;L(e)&&!R(e);){if(A(e))return e;if(C(e))return null;e=S(e)}return null}(t)||n}const X={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const s="fixed"===i,l=w(o),c=!!e&&C(e.floating);if(o===l||c&&s)return n;let a={scrollLeft:0,scrollTop:0},h=r(1);const u=r(0),d=L(o);if((d||!d&&!s)&&(("body"!==m(o)||T(l))&&(a=M(o)),L(o))){const t=V(o);h=P(o),u.x=t.x+o.clientLeft,u.y=t.y+o.clientTop}const f=!l||d||s?r(0):B(l,a,!0);return{width:n.width*h.x,height:n.height*h.y,x:n.x*h.x-a.scrollLeft*h.x+u.x+f.x,y:n.y*h.y-a.scrollTop*h.y+u.y+f.y}},getDocumentElement:w,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:s,strategy:l}=t;const r=[..."clippingAncestors"===n?C(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let o=H(t,[],!1).filter((t=>x(t)&&"body"!==m(t))),i=null;const s="fixed"===F(t).position;let l=s?S(t):t;for(;x(l)&&!R(l);){const e=F(l),n=A(l);n||"fixed"!==e.position||(i=null),(s?!n&&!i:!n&&"static"===e.position&&i&&["absolute","fixed"].includes(i.position)||T(l)&&!n&&z(t,l))?o=o.filter((t=>t!==l)):i=e,l=S(l)}return e.set(t,o),o}(e,this._c):[].concat(n),s],c=r[0],a=r.reduce(((t,n)=>{const s=q(e,n,l);return t.top=i(s.top,t.top),t.right=o(s.right,t.right),t.bottom=o(s.bottom,t.bottom),t.left=i(s.left,t.left),t}),q(e,c,l));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:J,getElementRects:async function(t){const e=this.getOffsetParent||J,n=this.getDimensions,o=await n(t.floating);return{reference:N(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=O(t);return{width:e,height:n}},getScale:P,isElement:x,isRTL:function(t){return"rtl"===F(t).direction}};function G(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function K(t,e,n,s){void 0===s&&(s={});const{ancestorScroll:r=!0,ancestorResize:c=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:h="function"==typeof IntersectionObserver,animationFrame:u=!1}=s,d=$(t),f=r||c?[...d?H(d):[],...H(e)]:[];f.forEach((t=>{r&&t.addEventListener("scroll",n,{passive:!0}),c&&t.addEventListener("resize",n)}));const g=d&&h?function(t,e){let n,s=null;const r=w(t);function c(){var t;clearTimeout(n),null==(t=s)||t.disconnect(),s=null}return function a(h,u){void 0===h&&(h=!1),void 0===u&&(u=1),c();const d=t.getBoundingClientRect(),{left:f,top:g,width:p,height:m}=d;if(h||e(),!p||!m)return;const v={rootMargin:-l(g)+"px "+-l(r.clientWidth-(f+p))+"px "+-l(r.clientHeight-(g+m))+"px "+-l(f)+"px",threshold:i(0,o(1,u))||1};let w=!0;function y(e){const o=e[0].intersectionRatio;if(o!==u){if(!w)return a();o?a(!1,o):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}1!==o||G(d,t.getBoundingClientRect())||a(),w=!1}try{s=new IntersectionObserver(y,{...v,root:r.ownerDocument})}catch(t){s=new IntersectionObserver(y,v)}s.observe(t)}(!0),c}(d,n):null;let p,m=-1,v=null;a&&(v=new ResizeObserver((t=>{let[o]=t;o&&o.target===d&&v&&(v.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame((()=>{var t;null==(t=v)||t.observe(e)}))),n()})),d&&!u&&v.observe(d),v.observe(e));let y=u?V(t):null;return u&&function e(){const o=V(t);y&&!G(y,o)&&n();y=o,p=requestAnimationFrame(e)}(),n(),()=>{var t;f.forEach((t=>{r&&t.removeEventListener("scroll",n),c&&t.removeEventListener("resize",n)})),null==g||g(),null==(t=v)||t.disconnect(),v=null,u&&cancelAnimationFrame(p)}}const Q=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:s,placement:l,middlewareData:r}=e,h=await async function(t,e){const{placement:n,platform:o,elements:i}=t,s=await(null==o.isRTL?void 0:o.isRTL(i.floating)),l=c(n),r=a(n),h="y"===u(n),d=["left","top"].includes(l)?-1:1,f=s&&h?-1:1,g=(m=t,"function"==typeof(p=e)?p(m):p);var p,m;let{mainAxis:v,crossAxis:w,alignmentAxis:y}="number"==typeof g?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:g.mainAxis||0,crossAxis:g.crossAxis||0,alignmentAxis:g.alignmentAxis};return r&&"number"==typeof y&&(w="end"===r?-1*y:y),h?{x:w*f,y:v*d}:{x:v*d,y:w*f}}(e,t);return l===(null==(n=r.offset)?void 0:n.placement)&&null!=(o=r.arrow)&&o.alignmentOffset?{}:{x:i+h.x,y:s+h.y,data:{...h,placement:l}}}}},Z=(t,e,n)=>{const o=new Map,i={platform:X,...n},s={...i.platform,_c:o};return(async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:s=[],platform:l}=n,r=s.filter(Boolean),c=await(null==l.isRTL?void 0:l.isRTL(e));let a=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:h,y:u}=g(a,o,c),d=o,f={},p=0;for(let n=0;n<r.length;n++){const{name:s,fn:m}=r[n],{x:v,y:w,data:y,reset:x}=await m({x:h,y:u,initialPlacement:o,placement:d,strategy:i,middlewareData:f,rects:a,platform:l,elements:{reference:t,floating:e}});h=null!=v?v:h,u=null!=w?w:u,f={...f,[s]:{...f[s],...y}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(a=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),({x:h,y:u}=g(a,d,c))),n=-1)}return{x:h,y:u,placement:d,strategy:i,middlewareData:f}})(t,e,{...i,platform:s})}},926:(t,e,n)=>{n.d(e,{JD:()=>i,gj:()=>o,yd:()=>s});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const o=(t,e,n="")=>(window.getComputedStyle(t).getPropertyValue(e)||n).replace(" ",""),i=(t,e,n=null)=>{const o=new CustomEvent(t,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(o)},s=(t,e)=>{const n=()=>{e(),t.removeEventListener("transitionend",n,!0)},o=window.getComputedStyle(t),i=o.getPropertyValue("transition-duration");"none"!==o.getPropertyValue("transition-property")&&parseFloat(i)>0?t.addEventListener("transitionend",n,!0):e()}}},e={};function n(o){var i=e[o];if(void 0!==i)return i.exports;var s=e[o]={exports:{}};return t[o](s,s.exports,n),s.exports}n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var o={};n.d(o,{A:()=>a});var i=n(663),s=n(926),l=n(615),r=n(189);
/*
 * HSTooltip
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
class c extends l.A{constructor(t,e,n){super(t,e,n),this.cleanupAutoUpdate=null,this.el&&(this.toggle=this.el.querySelector(".hs-tooltip-toggle")||this.el,this.content=this.el.querySelector(".hs-tooltip-content"),this.eventMode=(0,s.gj)(this.el,"--trigger")||"hover",this.preventFloatingUI=(0,s.gj)(this.el,"--prevent-popper","false"),this.placement=(0,s.gj)(this.el,"--placement"),this.strategy=(0,s.gj)(this.el,"--strategy"),this.scope=(0,s.gj)(this.el,"--scope")||"parent"),this.el&&this.toggle&&this.content&&this.init()}toggleClick(){this.click()}toggleFocus(){this.focus()}toggleMouseEnter(){this.enter()}toggleMouseLeave(){this.leave()}toggleHandle(){this.hide(),this.toggle.removeEventListener("click",this.onToggleHandleListener,!0),this.toggle.removeEventListener("blur",this.onToggleHandleListener,!0)}init(){this.createCollection(window.$hsTooltipCollection,this),"click"===this.eventMode?(this.onToggleClickListener=()=>this.toggleClick(),this.toggle.addEventListener("click",this.onToggleClickListener)):"focus"===this.eventMode?(this.onToggleFocusListener=()=>this.toggleFocus(),this.toggle.addEventListener("click",this.onToggleFocusListener)):"hover"===this.eventMode&&(this.onToggleMouseEnterListener=()=>this.toggleMouseEnter(),this.onToggleMouseLeaveListener=()=>this.toggleMouseLeave(),this.toggle.addEventListener("mouseenter",this.onToggleMouseEnterListener),this.toggle.addEventListener("mouseleave",this.onToggleMouseLeaveListener)),"false"===this.preventFloatingUI&&this.buildFloatingUI()}enter(){this._show()}leave(){this.hide()}click(){if(this.el.classList.contains("show"))return!1;this._show(),this.onToggleHandleListener=()=>{setTimeout((()=>this.toggleHandle()))},this.toggle.addEventListener("click",this.onToggleHandleListener,!0),this.toggle.addEventListener("blur",this.onToggleHandleListener,!0)}focus(){this._show();const t=()=>{this.hide(),this.toggle.removeEventListener("blur",t,!0)};this.toggle.addEventListener("blur",t,!0)}buildFloatingUI(){"window"===this.scope&&document.body.appendChild(this.content),(0,i.rD)(this.toggle,this.content,{placement:r.lP[this.placement]||"top",strategy:this.strategy||"fixed",middleware:[(0,i.cY)(5)]}).then((({x:t,y:e})=>{Object.assign(this.content.style,{position:this.strategy||"fixed",left:`${t}px`,top:`${e}px`})})),this.cleanupAutoUpdate=(0,i.ll)(this.toggle,this.content,(()=>{(0,i.rD)(this.toggle,this.content,{placement:r.lP[this.placement]||"top",strategy:this.strategy||"fixed",middleware:[(0,i.cY)(5)]}).then((({x:t,y:e})=>{Object.assign(this.content.style,{left:`${t}px`,top:`${e}px`})}))}))}_show(){this.content.classList.remove("hidden"),"window"===this.scope&&this.content.classList.add("show"),"false"!==this.preventFloatingUI||this.cleanupAutoUpdate||this.buildFloatingUI(),setTimeout((()=>{this.el.classList.add("show"),this.fireEvent("show",this.el),(0,s.JD)("show.hs.tooltip",this.el,this.el)}))}show(){switch(this.eventMode){case"click":this.click();break;case"focus":this.focus();break;default:this.enter()}this.toggle.focus(),this.toggle.style.outline="none"}hide(){this.el.classList.remove("show"),"window"===this.scope&&this.content.classList.remove("show"),"false"===this.preventFloatingUI&&this.cleanupAutoUpdate&&(this.cleanupAutoUpdate(),this.cleanupAutoUpdate=null),this.fireEvent("hide",this.el),(0,s.JD)("hide.hs.tooltip",this.el,this.el),(0,s.yd)(this.content,(()=>{if(this.el.classList.contains("show"))return!1;this.content.classList.add("hidden"),this.toggle.style.outline=""}))}destroy(){this.el.classList.remove("show"),this.content.classList.add("hidden"),"click"===this.eventMode?this.toggle.removeEventListener("click",this.onToggleClickListener):"focus"===this.eventMode?this.toggle.removeEventListener("click",this.onToggleFocusListener):"hover"===this.eventMode&&(this.toggle.removeEventListener("mouseenter",this.onToggleMouseEnterListener),this.toggle.removeEventListener("mouseleave",this.onToggleMouseLeaveListener)),this.toggle.removeEventListener("click",this.onToggleHandleListener,!0),this.toggle.removeEventListener("blur",this.onToggleHandleListener,!0),this.cleanupAutoUpdate&&(this.cleanupAutoUpdate(),this.cleanupAutoUpdate=null),window.$hsTooltipCollection=window.$hsTooltipCollection.filter((({element:t})=>t.el!==this.el))}static findInCollection(t){return window.$hsTooltipCollection.find((e=>t instanceof c?e.element.el===t.el:"string"==typeof t?e.element.el===document.querySelector(t):e.element.el===t))||null}static getInstance(t,e=!1){const n=window.$hsTooltipCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)));return n?e?n:n.element.el:null}static autoInit(){window.$hsTooltipCollection||(window.$hsTooltipCollection=[]),window.$hsTooltipCollection&&(window.$hsTooltipCollection=window.$hsTooltipCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll(".hs-tooltip:not(.--prevent-on-load-init)").forEach((t=>{window.$hsTooltipCollection.find((e=>{var n;return(null===(n=null==e?void 0:e.element)||void 0===n?void 0:n.el)===t}))||new c(t)}))}static show(t){const e=c.findInCollection(t);e&&e.element.show()}static hide(t){const e=c.findInCollection(t);e&&e.element.hide()}static on(t,e,n){const o=c.findInCollection(e);o&&(o.element.events[t]=n)}}window.addEventListener("load",(()=>{c.autoInit()})),"undefined"!=typeof window&&(window.HSTooltip=c);const a=c;var h=o.A;export{h as default};