!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n=e();for(var o in n)("object"==typeof exports?exports:t)[o]=n[o]}}(self,(()=>(()=>{"use strict";var t={223:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BREAKPOINTS=e.COMBO_BOX_ACCESSIBILITY_KEY_SET=e.SELECT_ACCESSIBILITY_KEY_SET=e.TABS_ACCESSIBILITY_KEY_SET=e.OVERLAY_ACCESSIBILITY_KEY_SET=e.DROPDOWN_ACCESSIBILITY_KEY_SET=e.POSITIONS=void 0,e.POSITIONS={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"},e.DROPDOWN_ACCESSIBILITY_KEY_SET=["Escape","ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Home","End","Enter"],e.OVERLAY_ACCESSIBILITY_KEY_SET=["Escape","Tab"],e.TABS_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End"],e.SELECT_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter","Space","Tab"],e.COMBO_BOX_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter"],e.BREAKPOINTS={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},292:function(t,e){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(e,"__esModule",{value:!0}),e.menuSearchHistory=e.classToClassList=e.htmlToElement=e.afterTransition=e.dispatch=e.debounce=e.isScrollable=e.isParentOrElementHidden=e.isJson=e.isIpadOS=e.isIOS=e.isDirectChild=e.isFormElement=e.isFocused=e.isEnoughSpace=e.getHighestZIndex=e.getZIndex=e.getClassPropertyAlt=e.getClassProperty=e.stringToBoolean=void 0;e.stringToBoolean=t=>"true"===t;e.getClassProperty=(t,e,n="")=>(window.getComputedStyle(t).getPropertyValue(e)||n).replace(" ","");e.getClassPropertyAlt=(t,e,n="")=>{let o="";return t.classList.forEach((t=>{t.includes(e)&&(o=t)})),o.match(/:(.*)]/)?o.match(/:(.*)]/)[1]:n};const n=t=>window.getComputedStyle(t).getPropertyValue("z-index");e.getZIndex=n;e.getHighestZIndex=t=>{let e=Number.NEGATIVE_INFINITY;return t.forEach((t=>{let o=n(t);"auto"!==o&&(o=parseInt(o,10),o>e&&(e=o))})),e};e.isDirectChild=(t,e)=>{const n=t.children;for(let t=0;t<n.length;t++)if(n[t]===e)return!0;return!1};e.isEnoughSpace=(t,e,n="auto",o=10,i=null)=>{const r=e.getBoundingClientRect(),l=i?i.getBoundingClientRect():null,s=window.innerHeight,c=l?r.top-l.top:r.top,a=(i?l.bottom:s)-r.bottom,f=t.clientHeight+o;return"bottom"===n?a>=f:"top"===n?c>=f:c>=f||a>=f};e.isFocused=t=>document.activeElement===t;e.isFormElement=t=>t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement;e.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isJson=t=>{if("string"!=typeof t)return!1;const e=t.trim()[0],n=t.trim().slice(-1);if("{"===e&&"}"===n||"["===e&&"]"===n)try{return JSON.parse(t),!0}catch(t){return!1}return!1};const o=t=>{if(!t)return!1;return"none"===window.getComputedStyle(t).display||o(t.parentElement)};e.isParentOrElementHidden=o;e.isScrollable=t=>{const e=window.getComputedStyle(t),n=e.overflowY,o=e.overflowX,i=("scroll"===n||"auto"===n)&&t.scrollHeight>t.clientHeight,r=("scroll"===o||"auto"===o)&&t.scrollWidth>t.clientWidth;return i||r};e.debounce=(t,e=200)=>{let n;return(...o)=>{clearTimeout(n),n=setTimeout((()=>{t.apply(this,o)}),e)}};e.dispatch=(t,e,n=null)=>{const o=new CustomEvent(t,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(o)};e.afterTransition=(t,e)=>{const n=()=>{e(),t.removeEventListener("transitionend",n,!0)},o=window.getComputedStyle(t),i=o.getPropertyValue("transition-duration");"none"!==o.getPropertyValue("transition-property")&&parseFloat(i)>0?t.addEventListener("transitionend",n,!0):e()};e.htmlToElement=t=>{const e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild};e.classToClassList=(t,e,n=" ",o="add")=>{t.split(n).forEach((t=>"add"===o?e.classList.add(t):e.classList.remove(t)))};const i={historyIndex:-1,addHistory(t){this.historyIndex=t},existsInHistory(t){return t>this.historyIndex},clearHistory(){this.historyIndex=-1}};e.menuSearchHistory=i},949:(t,e,n)=>{n.r(e),n.d(e,{arrow:()=>bt,autoPlacement:()=>mt,autoUpdate:()=>dt,computePosition:()=>Lt,detectOverflow:()=>gt,flip:()=>yt,getOverflowAncestors:()=>X,hide:()=>xt,inline:()=>Et,limitShift:()=>Tt,offset:()=>pt,platform:()=>ut,shift:()=>wt,size:()=>vt});const o=["top","right","bottom","left"],i=["start","end"],r=o.reduce(((t,e)=>t.concat(e,e+"-"+i[0],e+"-"+i[1])),[]),l=Math.min,s=Math.max,c=Math.round,a=Math.floor,f=t=>({x:t,y:t}),u={left:"right",right:"left",bottom:"top",top:"bottom"},h={start:"end",end:"start"};function d(t,e,n){return s(t,l(e,n))}function g(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function m(t){return t.split("-")[1]}function w(t){return"x"===t?"y":"x"}function y(t){return"y"===t?"height":"width"}function v(t){return["top","bottom"].includes(p(t))?"y":"x"}function x(t){return w(v(t))}function b(t,e,n){void 0===n&&(n=!1);const o=m(t),i=x(t),r=y(i);let l="x"===i?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[r]>e.floating[r]&&(l=T(l)),[l,T(l)]}function E(t){return t.replace(/start|end/g,(t=>h[t]))}function T(t){return t.replace(/left|right|bottom|top/g,(t=>u[t]))}function L(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function C(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function S(t,e,n){let{reference:o,floating:i}=t;const r=v(e),l=x(e),s=y(l),c=p(e),a="y"===r,f=o.x+o.width/2-i.width/2,u=o.y+o.height/2-i.height/2,h=o[s]/2-i[s]/2;let d;switch(c){case"top":d={x:f,y:o.y-i.height};break;case"bottom":d={x:f,y:o.y+o.height};break;case"right":d={x:o.x+o.width,y:u};break;case"left":d={x:o.x-i.width,y:u};break;default:d={x:o.x,y:o.y}}switch(m(e)){case"start":d[l]-=h*(n&&a?-1:1);break;case"end":d[l]+=h*(n&&a?-1:1)}return d}async function A(t,e){var n;void 0===e&&(e={});const{x:o,y:i,platform:r,rects:l,elements:s,strategy:c}=t,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:u="floating",altBoundary:h=!1,padding:d=0}=g(e,t),p=L(d),m=s[h?"floating"===u?"reference":"floating":u],w=C(await r.getClippingRect({element:null==(n=await(null==r.isElement?void 0:r.isElement(m)))||n?m:m.contextElement||await(null==r.getDocumentElement?void 0:r.getDocumentElement(s.floating)),boundary:a,rootBoundary:f,strategy:c})),y="floating"===u?{x:o,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await(null==r.getOffsetParent?void 0:r.getOffsetParent(s.floating)),x=await(null==r.isElement?void 0:r.isElement(v))&&await(null==r.getScale?void 0:r.getScale(v))||{x:1,y:1},b=C(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:v,strategy:c}):y);return{top:(w.top-b.top+p.top)/x.y,bottom:(b.bottom-w.bottom+p.bottom)/x.y,left:(w.left-b.left+p.left)/x.x,right:(b.right-w.right+p.right)/x.x}}function I(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function O(t){return o.some((e=>t[e]>=0))}function P(t){const e=l(...t.map((t=>t.left))),n=l(...t.map((t=>t.top)));return{x:e,y:n,width:s(...t.map((t=>t.right)))-e,height:s(...t.map((t=>t.bottom)))-n}}function R(){return"undefined"!=typeof window}function _(t){return B(t)?(t.nodeName||"").toLowerCase():"#document"}function H(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function M(t){var e;return null==(e=(B(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function B(t){return!!R()&&(t instanceof Node||t instanceof H(t).Node)}function D(t){return!!R()&&(t instanceof Element||t instanceof H(t).Element)}function k(t){return!!R()&&(t instanceof HTMLElement||t instanceof H(t).HTMLElement)}function F(t){return!(!R()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof H(t).ShadowRoot)}function Y(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=j(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function U(t){return["table","td","th"].includes(_(t))}function N(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(t){return!1}}))}function W(t){const e=$(),n=D(t)?j(t):t;return["transform","translate","scale","rotate","perspective"].some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function $(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function V(t){return["html","body","#document"].includes(_(t))}function j(t){return H(t).getComputedStyle(t)}function K(t){return D(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function z(t){if("html"===_(t))return t;const e=t.assignedSlot||t.parentNode||F(t)&&t.host||M(t);return F(e)?e.host:e}function q(t){const e=z(t);return V(e)?t.ownerDocument?t.ownerDocument.body:t.body:k(e)&&Y(e)?e:q(e)}function X(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=q(t),r=i===(null==(o=t.ownerDocument)?void 0:o.body),l=H(i);if(r){const t=Z(l);return e.concat(l,l.visualViewport||[],Y(i)?i:[],t&&n?X(t):[])}return e.concat(i,X(i,[],n))}function Z(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function J(t){const e=j(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=k(t),r=i?t.offsetWidth:n,l=i?t.offsetHeight:o,s=c(n)!==r||c(o)!==l;return s&&(n=r,o=l),{width:n,height:o,$:s}}function G(t){return D(t)?t:t.contextElement}function Q(t){const e=G(t);if(!k(e))return f(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:r}=J(e);let l=(r?c(n.width):n.width)/o,s=(r?c(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}const tt=f(0);function et(t){const e=H(t);return $()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tt}function nt(t,e,n,o){void 0===e&&(e=!1),void 0===n&&(n=!1);const i=t.getBoundingClientRect(),r=G(t);let l=f(1);e&&(o?D(o)&&(l=Q(o)):l=Q(t));const s=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==H(t))&&e}(r,n,o)?et(r):f(0);let c=(i.left+s.x)/l.x,a=(i.top+s.y)/l.y,u=i.width/l.x,h=i.height/l.y;if(r){const t=H(r),e=o&&D(o)?H(o):o;let n=t,i=Z(n);for(;i&&o&&e!==n;){const t=Q(i),e=i.getBoundingClientRect(),o=j(i),r=e.left+(i.clientLeft+parseFloat(o.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(o.paddingTop))*t.y;c*=t.x,a*=t.y,u*=t.x,h*=t.y,c+=r,a+=l,n=H(i),i=Z(n)}}return C({width:u,height:h,x:c,y:a})}function ot(t,e){const n=K(t).scrollLeft;return e?e.left+n:nt(M(t)).left+n}function it(t,e,n){void 0===n&&(n=!1);const o=t.getBoundingClientRect();return{x:o.left+e.scrollLeft-(n?0:ot(t,o)),y:o.top+e.scrollTop}}function rt(t,e,n){let o;if("viewport"===e)o=function(t,e){const n=H(t),o=M(t),i=n.visualViewport;let r=o.clientWidth,l=o.clientHeight,s=0,c=0;if(i){r=i.width,l=i.height;const t=$();(!t||t&&"fixed"===e)&&(s=i.offsetLeft,c=i.offsetTop)}return{width:r,height:l,x:s,y:c}}(t,n);else if("document"===e)o=function(t){const e=M(t),n=K(t),o=t.ownerDocument.body,i=s(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),r=s(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let l=-n.scrollLeft+ot(t);const c=-n.scrollTop;return"rtl"===j(o).direction&&(l+=s(e.clientWidth,o.clientWidth)-i),{width:i,height:r,x:l,y:c}}(M(t));else if(D(e))o=function(t,e){const n=nt(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=k(t)?Q(t):f(1);return{width:t.clientWidth*r.x,height:t.clientHeight*r.y,x:i*r.x,y:o*r.y}}(e,n);else{const n=et(t);o={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return C(o)}function lt(t,e){const n=z(t);return!(n===e||!D(n)||V(n))&&("fixed"===j(n).position||lt(n,e))}function st(t,e,n){const o=k(e),i=M(e),r="fixed"===n,l=nt(t,!0,r,e);let s={scrollLeft:0,scrollTop:0};const c=f(0);if(o||!o&&!r)if(("body"!==_(e)||Y(i))&&(s=K(e)),o){const t=nt(e,!0,r,e);c.x=t.x+e.clientLeft,c.y=t.y+e.clientTop}else i&&(c.x=ot(i));const a=!i||o||r?f(0):it(i,s);return{x:l.left+s.scrollLeft-c.x-a.x,y:l.top+s.scrollTop-c.y-a.y,width:l.width,height:l.height}}function ct(t){return"static"===j(t).position}function at(t,e){if(!k(t)||"fixed"===j(t).position)return null;if(e)return e(t);let n=t.offsetParent;return M(t)===n&&(n=n.ownerDocument.body),n}function ft(t,e){const n=H(t);if(N(t))return n;if(!k(t)){let e=z(t);for(;e&&!V(e);){if(D(e)&&!ct(e))return e;e=z(e)}return n}let o=at(t,e);for(;o&&U(o)&&ct(o);)o=at(o,e);return o&&V(o)&&ct(o)&&!W(o)?n:o||function(t){let e=z(t);for(;k(e)&&!V(e);){if(W(e))return e;if(N(e))return null;e=z(e)}return null}(t)||n}const ut={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r="fixed"===i,l=M(o),s=!!e&&N(e.floating);if(o===l||s&&r)return n;let c={scrollLeft:0,scrollTop:0},a=f(1);const u=f(0),h=k(o);if((h||!h&&!r)&&(("body"!==_(o)||Y(l))&&(c=K(o)),k(o))){const t=nt(o);a=Q(o),u.x=t.x+o.clientLeft,u.y=t.y+o.clientTop}const d=!l||h||r?f(0):it(l,c,!0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-c.scrollLeft*a.x+u.x+d.x,y:n.y*a.y-c.scrollTop*a.y+u.y+d.y}},getDocumentElement:M,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const r=[..."clippingAncestors"===n?N(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let o=X(t,[],!1).filter((t=>D(t)&&"body"!==_(t))),i=null;const r="fixed"===j(t).position;let l=r?z(t):t;for(;D(l)&&!V(l);){const e=j(l),n=W(l);n||"fixed"!==e.position||(i=null),(r?!n&&!i:!n&&"static"===e.position&&i&&["absolute","fixed"].includes(i.position)||Y(l)&&!n&&lt(t,l))?o=o.filter((t=>t!==l)):i=e,l=z(l)}return e.set(t,o),o}(e,this._c):[].concat(n),o],c=r[0],a=r.reduce(((t,n)=>{const o=rt(e,n,i);return t.top=s(o.top,t.top),t.right=l(o.right,t.right),t.bottom=l(o.bottom,t.bottom),t.left=s(o.left,t.left),t}),rt(e,c,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ft,getElementRects:async function(t){const e=this.getOffsetParent||ft,n=this.getDimensions,o=await n(t.floating);return{reference:st(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=J(t);return{width:e,height:n}},getScale:Q,isElement:D,isRTL:function(t){return"rtl"===j(t).direction}};function ht(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function dt(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:u=!1}=o,h=G(t),d=i||r?[...h?X(h):[],...X(e)]:[];d.forEach((t=>{i&&t.addEventListener("scroll",n,{passive:!0}),r&&t.addEventListener("resize",n)}));const g=h&&f?function(t,e){let n,o=null;const i=M(t);function r(){var t;clearTimeout(n),null==(t=o)||t.disconnect(),o=null}return function c(f,u){void 0===f&&(f=!1),void 0===u&&(u=1),r();const h=t.getBoundingClientRect(),{left:d,top:g,width:p,height:m}=h;if(f||e(),!p||!m)return;const w={rootMargin:-a(g)+"px "+-a(i.clientWidth-(d+p))+"px "+-a(i.clientHeight-(g+m))+"px "+-a(d)+"px",threshold:s(0,l(1,u))||1};let y=!0;function v(e){const o=e[0].intersectionRatio;if(o!==u){if(!y)return c();o?c(!1,o):n=setTimeout((()=>{c(!1,1e-7)}),1e3)}1!==o||ht(h,t.getBoundingClientRect())||c(),y=!1}try{o=new IntersectionObserver(v,{...w,root:i.ownerDocument})}catch(t){o=new IntersectionObserver(v,w)}o.observe(t)}(!0),r}(h,n):null;let p,m=-1,w=null;c&&(w=new ResizeObserver((t=>{let[o]=t;o&&o.target===h&&w&&(w.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame((()=>{var t;null==(t=w)||t.observe(e)}))),n()})),h&&!u&&w.observe(h),w.observe(e));let y=u?nt(t):null;return u&&function e(){const o=nt(t);y&&!ht(y,o)&&n();y=o,p=requestAnimationFrame(e)}(),n(),()=>{var t;d.forEach((t=>{i&&t.removeEventListener("scroll",n),r&&t.removeEventListener("resize",n)})),null==g||g(),null==(t=w)||t.disconnect(),w=null,u&&cancelAnimationFrame(p)}}const gt=A,pt=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:r,placement:l,middlewareData:s}=e,c=await async function(t,e){const{placement:n,platform:o,elements:i}=t,r=await(null==o.isRTL?void 0:o.isRTL(i.floating)),l=p(n),s=m(n),c="y"===v(n),a=["left","top"].includes(l)?-1:1,f=r&&c?-1:1,u=g(e,t);let{mainAxis:h,crossAxis:d,alignmentAxis:w}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return s&&"number"==typeof w&&(d="end"===s?-1*w:w),c?{x:d*f,y:h*a}:{x:h*a,y:d*f}}(e,t);return l===(null==(n=s.offset)?void 0:n.placement)&&null!=(o=s.arrow)&&o.alignmentOffset?{}:{x:i+c.x,y:r+c.y,data:{...c,placement:l}}}}},mt=function(t){return void 0===t&&(t={}),{name:"autoPlacement",options:t,async fn(e){var n,o,i;const{rects:l,middlewareData:s,placement:c,platform:a,elements:f}=e,{crossAxis:u=!1,alignment:h,allowedPlacements:d=r,autoAlignment:w=!0,...y}=g(t,e),v=void 0!==h||d===r?function(t,e,n){return(t?[...n.filter((e=>m(e)===t)),...n.filter((e=>m(e)!==t))]:n.filter((t=>p(t)===t))).filter((n=>!t||m(n)===t||!!e&&E(n)!==n))}(h||null,w,d):d,x=await A(e,y),T=(null==(n=s.autoPlacement)?void 0:n.index)||0,L=v[T];if(null==L)return{};const C=b(L,l,await(null==a.isRTL?void 0:a.isRTL(f.floating)));if(c!==L)return{reset:{placement:v[0]}};const S=[x[p(L)],x[C[0]],x[C[1]]],I=[...(null==(o=s.autoPlacement)?void 0:o.overflows)||[],{placement:L,overflows:S}],O=v[T+1];if(O)return{data:{index:T+1,overflows:I},reset:{placement:O}};const P=I.map((t=>{const e=m(t.placement);return[t.placement,e&&u?t.overflows.slice(0,2).reduce(((t,e)=>t+e),0):t.overflows[0],t.overflows]})).sort(((t,e)=>t[1]-e[1])),R=(null==(i=P.filter((t=>t[2].slice(0,m(t[0])?2:3).every((t=>t<=0))))[0])?void 0:i[0])||P[0][0];return R!==c?{data:{index:T+1,overflows:I},reset:{placement:R}}:{}}}},wt=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,{mainAxis:r=!0,crossAxis:l=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=g(t,e),a={x:n,y:o},f=await A(e,c),u=v(p(i)),h=w(u);let m=a[h],y=a[u];if(r){const t="y"===h?"bottom":"right";m=d(m+f["y"===h?"top":"left"],m,m-f[t])}if(l){const t="y"===u?"bottom":"right";y=d(y+f["y"===u?"top":"left"],y,y-f[t])}const x=s.fn({...e,[h]:m,[u]:y});return{...x,data:{x:x.x-n,y:x.y-o,enabled:{[h]:r,[u]:l}}}}}},yt=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:i,middlewareData:r,rects:l,initialPlacement:s,platform:c,elements:a}=e,{mainAxis:f=!0,crossAxis:u=!0,fallbackPlacements:h,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:y=!0,...x}=g(t,e);if(null!=(n=r.arrow)&&n.alignmentOffset)return{};const L=p(i),C=v(s),S=p(s)===s,I=await(null==c.isRTL?void 0:c.isRTL(a.floating)),O=h||(S||!y?[T(s)]:function(t){const e=T(t);return[E(t),e,E(e)]}(s)),P="none"!==w;!h&&P&&O.push(...function(t,e,n,o){const i=m(t);let r=function(t,e,n){const o=["left","right"],i=["right","left"],r=["top","bottom"],l=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:o:e?o:i;case"left":case"right":return e?r:l;default:return[]}}(p(t),"start"===n,o);return i&&(r=r.map((t=>t+"-"+i)),e&&(r=r.concat(r.map(E)))),r}(s,y,w,I));const R=[s,...O],_=await A(e,x),H=[];let M=(null==(o=r.flip)?void 0:o.overflows)||[];if(f&&H.push(_[L]),u){const t=b(i,l,I);H.push(_[t[0]],_[t[1]])}if(M=[...M,{placement:i,overflows:H}],!H.every((t=>t<=0))){var B,D;const t=((null==(B=r.flip)?void 0:B.index)||0)+1,e=R[t];if(e)return{data:{index:t,overflows:M},reset:{placement:e}};let n=null==(D=M.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:D.placement;if(!n)switch(d){case"bestFit":{var k;const t=null==(k=M.filter((t=>{if(P){const e=v(t.placement);return e===C||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:k[0];t&&(n=t);break}case"initialPlacement":n=s}if(i!==n)return{reset:{placement:n}}}return{}}}},vt=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:i,rects:r,platform:c,elements:a}=e,{apply:f=()=>{},...u}=g(t,e),h=await A(e,u),d=p(i),w=m(i),y="y"===v(i),{width:x,height:b}=r.floating;let E,T;"top"===d||"bottom"===d?(E=d,T=w===(await(null==c.isRTL?void 0:c.isRTL(a.floating))?"start":"end")?"left":"right"):(T=d,E="end"===w?"top":"bottom");const L=b-h.top-h.bottom,C=x-h.left-h.right,S=l(b-h[E],L),I=l(x-h[T],C),O=!e.middlewareData.shift;let P=S,R=I;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(R=C),null!=(o=e.middlewareData.shift)&&o.enabled.y&&(P=L),O&&!w){const t=s(h.left,0),e=s(h.right,0),n=s(h.top,0),o=s(h.bottom,0);y?R=x-2*(0!==t||0!==e?t+e:s(h.left,h.right)):P=b-2*(0!==n||0!==o?n+o:s(h.top,h.bottom))}await f({...e,availableWidth:R,availableHeight:P});const _=await c.getDimensions(a.floating);return x!==_.width||b!==_.height?{reset:{rects:!0}}:{}}}},xt=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:o="referenceHidden",...i}=g(t,e);switch(o){case"referenceHidden":{const t=I(await A(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:O(t)}}}case"escaped":{const t=I(await A(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:O(t)}}}default:return{}}}}},bt=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:i,rects:r,platform:s,elements:c,middlewareData:a}=e,{element:f,padding:u=0}=g(t,e)||{};if(null==f)return{};const h=L(u),p={x:n,y:o},w=x(i),v=y(w),b=await s.getDimensions(f),E="y"===w,T=E?"top":"left",C=E?"bottom":"right",S=E?"clientHeight":"clientWidth",A=r.reference[v]+r.reference[w]-p[w]-r.floating[v],I=p[w]-r.reference[w],O=await(null==s.getOffsetParent?void 0:s.getOffsetParent(f));let P=O?O[S]:0;P&&await(null==s.isElement?void 0:s.isElement(O))||(P=c.floating[S]||r.floating[v]);const R=A/2-I/2,_=P/2-b[v]/2-1,H=l(h[T],_),M=l(h[C],_),B=H,D=P-b[v]-M,k=P/2-b[v]/2+R,F=d(B,k,D),Y=!a.arrow&&null!=m(i)&&k!==F&&r.reference[v]/2-(k<B?H:M)-b[v]/2<0,U=Y?k<B?k-B:k-D:0;return{[w]:p[w]+U,data:{[w]:F,centerOffset:k-F-U,...Y&&{alignmentOffset:U}},reset:Y}}}),Et=function(t){return void 0===t&&(t={}),{name:"inline",options:t,async fn(e){const{placement:n,elements:o,rects:i,platform:r,strategy:c}=e,{padding:a=2,x:f,y:u}=g(t,e),h=Array.from(await(null==r.getClientRects?void 0:r.getClientRects(o.reference))||[]),d=function(t){const e=t.slice().sort(((t,e)=>t.y-e.y)),n=[];let o=null;for(let t=0;t<e.length;t++){const i=e[t];!o||i.y-o.y>o.height/2?n.push([i]):n[n.length-1].push(i),o=i}return n.map((t=>C(P(t))))}(h),m=C(P(h)),w=L(a);const y=await r.getElementRects({reference:{getBoundingClientRect:function(){if(2===d.length&&d[0].left>d[1].right&&null!=f&&null!=u)return d.find((t=>f>t.left-w.left&&f<t.right+w.right&&u>t.top-w.top&&u<t.bottom+w.bottom))||m;if(d.length>=2){if("y"===v(n)){const t=d[0],e=d[d.length-1],o="top"===p(n),i=t.top,r=e.bottom,l=o?t.left:e.left,s=o?t.right:e.right;return{top:i,bottom:r,left:l,right:s,width:s-l,height:r-i,x:l,y:i}}const t="left"===p(n),e=s(...d.map((t=>t.right))),o=l(...d.map((t=>t.left))),i=d.filter((n=>t?n.left===o:n.right===e)),r=i[0].top,c=i[i.length-1].bottom;return{top:r,bottom:c,left:o,right:e,width:e-o,height:c-r,x:o,y:r}}return m}},floating:o.floating,strategy:c});return i.reference.x!==y.reference.x||i.reference.y!==y.reference.y||i.reference.width!==y.reference.width||i.reference.height!==y.reference.height?{reset:{rects:y}}:{}}}},Tt=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:o,placement:i,rects:r,middlewareData:l}=e,{offset:s=0,mainAxis:c=!0,crossAxis:a=!0}=g(t,e),f={x:n,y:o},u=v(i),h=w(u);let d=f[h],m=f[u];const y=g(s,e),x="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(c){const t="y"===h?"height":"width",e=r.reference[h]-r.floating[t]+x.mainAxis,n=r.reference[h]+r.reference[t]-x.mainAxis;d<e?d=e:d>n&&(d=n)}if(a){var b,E;const t="y"===h?"width":"height",e=["top","left"].includes(p(i)),n=r.reference[u]-r.floating[t]+(e&&(null==(b=l.offset)?void 0:b[u])||0)+(e?0:x.crossAxis),o=r.reference[u]+r.reference[t]+(e?0:(null==(E=l.offset)?void 0:E[u])||0)-(e?x.crossAxis:0);m<n?m=n:m>o&&(m=o)}return{[h]:d,[u]:m}}}},Lt=(t,e,n)=>{const o=new Map,i={platform:ut,...n},r={...i.platform,_c:o};return(async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:r=[],platform:l}=n,s=r.filter(Boolean),c=await(null==l.isRTL?void 0:l.isRTL(e));let a=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:f,y:u}=S(a,o,c),h=o,d={},g=0;for(let n=0;n<s.length;n++){const{name:r,fn:p}=s[n],{x:m,y:w,data:y,reset:v}=await p({x:f,y:u,initialPlacement:o,placement:h,strategy:i,middlewareData:d,rects:a,platform:l,elements:{reference:t,floating:e}});f=null!=m?m:f,u=null!=w?w:u,d={...d,[r]:{...d[r],...y}},v&&g<=50&&(g++,"object"==typeof v&&(v.placement&&(h=v.placement),v.rects&&(a=!0===v.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):v.rects),({x:f,y:u}=S(a,h,c))),n=-1)}return{x:f,y:u,placement:h,strategy:i,middlewareData:d}})(t,e,{...i,platform:r})}},961:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(t,e,n){this.el=t,this.options=e,this.events=n,this.el=t,this.options=e,this.events={}}createCollection(t,e){var n;t.push({id:(null===(n=null==e?void 0:e.el)||void 0===n?void 0:n.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}},969:function(t,e,n){
/*
 * HSTooltip
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const i=n(949),r=n(292),l=o(n(961)),s=n(223);class c extends l.default{constructor(t,e,n){super(t,e,n),this.cleanupAutoUpdate=null,this.el&&(this.toggle=this.el.querySelector(".hs-tooltip-toggle")||this.el,this.content=this.el.querySelector(".hs-tooltip-content"),this.eventMode=(0,r.getClassProperty)(this.el,"--trigger")||"hover",this.preventFloatingUI=(0,r.getClassProperty)(this.el,"--prevent-popper","false"),this.placement=(0,r.getClassProperty)(this.el,"--placement"),this.strategy=(0,r.getClassProperty)(this.el,"--strategy"),this.scope=(0,r.getClassProperty)(this.el,"--scope")||"parent"),this.el&&this.toggle&&this.content&&this.init()}toggleClick(){this.click()}toggleFocus(){this.focus()}toggleMouseEnter(){this.enter()}toggleMouseLeave(){this.leave()}toggleHandle(){this.hide(),this.toggle.removeEventListener("click",this.onToggleHandleListener,!0),this.toggle.removeEventListener("blur",this.onToggleHandleListener,!0)}init(){this.createCollection(window.$hsTooltipCollection,this),"click"===this.eventMode?(this.onToggleClickListener=()=>this.toggleClick(),this.toggle.addEventListener("click",this.onToggleClickListener)):"focus"===this.eventMode?(this.onToggleFocusListener=()=>this.toggleFocus(),this.toggle.addEventListener("click",this.onToggleFocusListener)):"hover"===this.eventMode&&(this.onToggleMouseEnterListener=()=>this.toggleMouseEnter(),this.onToggleMouseLeaveListener=()=>this.toggleMouseLeave(),this.toggle.addEventListener("mouseenter",this.onToggleMouseEnterListener),this.toggle.addEventListener("mouseleave",this.onToggleMouseLeaveListener)),"false"===this.preventFloatingUI&&this.buildFloatingUI()}enter(){this._show()}leave(){this.hide()}click(){if(this.el.classList.contains("show"))return!1;this._show(),this.onToggleHandleListener=()=>{setTimeout((()=>this.toggleHandle()))},this.toggle.addEventListener("click",this.onToggleHandleListener,!0),this.toggle.addEventListener("blur",this.onToggleHandleListener,!0)}focus(){this._show();const t=()=>{this.hide(),this.toggle.removeEventListener("blur",t,!0)};this.toggle.addEventListener("blur",t,!0)}buildFloatingUI(){"window"===this.scope&&document.body.appendChild(this.content);const t=(t,e,n)=>{const o=this.content.getBoundingClientRect(),i=window.innerWidth,r=window.innerHeight,l=i-(window.innerWidth-document.documentElement.clientWidth),s=this.toggle.getBoundingClientRect();let c=t,a=e,f=n;return n.includes("right")&&t+o.width>l?(f=n.replace("right","left"),c=s.left-o.width-5):n.includes("left")&&t<0&&(f=n.replace("left","right"),c=s.right+5),n.includes("top")&&e-o.height<0?(f=n.replace("top","bottom"),a=s.bottom+5):n.includes("bottom")&&e+o.height>r&&(f=n.replace("bottom","top"),a=s.top-o.height-5),{x:c,y:a,placement:f}};(0,i.computePosition)(this.toggle,this.content,{placement:s.POSITIONS[this.placement]||"top",strategy:this.strategy||"fixed",middleware:[(0,i.offset)(5)]}).then((({x:e,y:n,placement:o})=>{const i=t(e,n,o);Object.assign(this.content.style,{position:this.strategy||"fixed",left:`${i.x}px`,top:`${i.y}px`}),this.content.setAttribute("data-placement",i.placement)})),this.cleanupAutoUpdate=(0,i.autoUpdate)(this.toggle,this.content,(()=>{(0,i.computePosition)(this.toggle,this.content,{placement:s.POSITIONS[this.placement]||"top",strategy:this.strategy||"fixed",middleware:[(0,i.offset)(5)]}).then((({x:e,y:n,placement:o})=>{const i=t(e,n,o);Object.assign(this.content.style,{left:`${i.x}px`,top:`${i.y}px`}),this.content.setAttribute("data-placement",i.placement)}))}))}_show(){this.content.classList.remove("hidden"),"window"===this.scope&&this.content.classList.add("show"),"false"!==this.preventFloatingUI||this.cleanupAutoUpdate||this.buildFloatingUI(),setTimeout((()=>{this.el.classList.add("show"),this.fireEvent("show",this.el),(0,r.dispatch)("show.hs.tooltip",this.el,this.el)}))}show(){switch(this.eventMode){case"click":this.click();break;case"focus":this.focus();break;default:this.enter()}this.toggle.focus(),this.toggle.style.outline="none"}hide(){this.el.classList.remove("show"),"window"===this.scope&&this.content.classList.remove("show"),"false"===this.preventFloatingUI&&this.cleanupAutoUpdate&&(this.cleanupAutoUpdate(),this.cleanupAutoUpdate=null),this.fireEvent("hide",this.el),(0,r.dispatch)("hide.hs.tooltip",this.el,this.el),(0,r.afterTransition)(this.content,(()=>{if(this.el.classList.contains("show"))return!1;this.content.classList.add("hidden"),this.toggle.style.outline=""}))}destroy(){this.el.classList.remove("show"),this.content.classList.add("hidden"),"click"===this.eventMode?this.toggle.removeEventListener("click",this.onToggleClickListener):"focus"===this.eventMode?this.toggle.removeEventListener("click",this.onToggleFocusListener):"hover"===this.eventMode&&(this.toggle.removeEventListener("mouseenter",this.onToggleMouseEnterListener),this.toggle.removeEventListener("mouseleave",this.onToggleMouseLeaveListener)),this.toggle.removeEventListener("click",this.onToggleHandleListener,!0),this.toggle.removeEventListener("blur",this.onToggleHandleListener,!0),this.cleanupAutoUpdate&&(this.cleanupAutoUpdate(),this.cleanupAutoUpdate=null),window.$hsTooltipCollection=window.$hsTooltipCollection.filter((({element:t})=>t.el!==this.el))}static findInCollection(t){return window.$hsTooltipCollection.find((e=>t instanceof c?e.element.el===t.el:"string"==typeof t?e.element.el===document.querySelector(t):e.element.el===t))||null}static getInstance(t,e=!1){const n=window.$hsTooltipCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)));return n?e?n:n.element.el:null}static autoInit(){window.$hsTooltipCollection||(window.$hsTooltipCollection=[]),window.$hsTooltipCollection&&(window.$hsTooltipCollection=window.$hsTooltipCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll(".hs-tooltip:not(.--prevent-on-load-init)").forEach((t=>{window.$hsTooltipCollection.find((e=>{var n;return(null===(n=null==e?void 0:e.element)||void 0===n?void 0:n.el)===t}))||new c(t)}))}static show(t){const e=c.findInCollection(t);e&&e.element.show()}static hide(t){const e=c.findInCollection(t);e&&e.element.hide()}static on(t,e,n){const o=c.findInCollection(e);o&&(o.element.events[t]=n)}}window.addEventListener("load",(()=>{c.autoInit()})),"undefined"!=typeof window&&(window.HSTooltip=c),e.default=c}},e={};function n(o){var i=e[o];if(void 0!==i)return i.exports;var r=e[o]={exports:{}};return t[o].call(r.exports,r,r.exports,n),r.exports}return n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n(969)})()));