var e={189:(e,t,n)=>{n.d(t,{Fy:()=>o,LO:()=>i});const o=["ArrowU<PERSON>","ArrowLeft","ArrowDown","ArrowRight","Home","End"],i={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},615:(e,t,n)=>{n.d(t,{A:()=>o});class o{constructor(e,t,n){this.el=e,this.options=t,this.events=n,this.el=e,this.options=t,this.events={}}createCollection(e,t){var n;e.push({id:(null===(n=null==t?void 0:t.el)||void 0===n?void 0:n.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}},926:(e,t,n)=>{n.d(t,{JD:()=>o});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const o=(e,t,n=null)=>{const o=new CustomEvent(e,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(o)}}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,n),s.exports}n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o={};n.d(o,{A:()=>a});var i=n(926),s=n(615),r=n(189);
/*
 * HSTabs
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
class l extends s.A{constructor(e,t,n){var o,i;super(e,t,n);const s=e.getAttribute("data-hs-tabs"),l=s?JSON.parse(s):{},a=Object.assign(Object.assign({},l),t);this.eventType=null!==(o=a.eventType)&&void 0!==o?o:"click",this.preventNavigationResolution="number"==typeof a.preventNavigationResolution?a.preventNavigationResolution:r.LO[a.preventNavigationResolution]||null,this.toggles=this.el.querySelectorAll("[data-hs-tab]"),this.extraToggleId=this.el.getAttribute("data-hs-tab-select"),this.extraToggle=this.extraToggleId?document.querySelector(this.extraToggleId):null,this.current=Array.from(this.toggles).find((e=>e.classList.contains("active"))),this.currentContentId=(null===(i=this.current)||void 0===i?void 0:i.getAttribute("data-hs-tab"))||null,this.currentContent=this.currentContentId?document.querySelector(this.currentContentId):null,this.prev=null,this.prevContentId=null,this.prevContent=null,this.onToggleHandler=[],this.init()}toggle(e){this.open(e)}extraToggleChange(e){this.change(e)}init(){this.createCollection(window.$hsTabsCollection,this),this.toggles.forEach((e=>{const t=t=>{"click"===this.eventType&&this.preventNavigationResolution&&document.body.clientWidth<=+this.preventNavigationResolution&&t.preventDefault(),this.toggle(e)},n=e=>{this.preventNavigationResolution&&document.body.clientWidth<=+this.preventNavigationResolution&&e.preventDefault()};this.onToggleHandler.push({el:e,fn:t,preventClickFn:n}),"click"===this.eventType?e.addEventListener("click",t):(e.addEventListener("mouseenter",t),e.addEventListener("click",n))})),this.extraToggle&&(this.onExtraToggleChangeListener=e=>this.extraToggleChange(e),this.extraToggle.addEventListener("change",this.onExtraToggleChangeListener))}open(e){var t,n,o,s,r;this.prev=this.current,this.prevContentId=this.currentContentId,this.prevContent=this.currentContent,this.current=e,this.currentContentId=e.getAttribute("data-hs-tab"),this.currentContent=this.currentContentId?document.querySelector(this.currentContentId):null,(null===(t=null==this?void 0:this.prev)||void 0===t?void 0:t.ariaSelected)&&(this.prev.ariaSelected="false"),null===(n=this.prev)||void 0===n||n.classList.remove("active"),null===(o=this.prevContent)||void 0===o||o.classList.add("hidden"),(null===(s=null==this?void 0:this.current)||void 0===s?void 0:s.ariaSelected)&&(this.current.ariaSelected="true"),this.current.classList.add("active"),null===(r=this.currentContent)||void 0===r||r.classList.remove("hidden"),this.fireEvent("change",{el:e,prev:this.prevContentId,current:this.currentContentId,tabsId:this.el.id}),(0,i.JD)("change.hs.tab",e,{el:e,prev:this.prevContentId,current:this.currentContentId,tabsId:this.el.id})}change(e){const t=document.querySelector(`[data-hs-tab="${e.target.value}"]`);t&&("hover"===this.eventType?t.dispatchEvent(new Event("mouseenter")):t.click())}destroy(){this.toggles.forEach((e=>{var t;const n=null===(t=this.onToggleHandler)||void 0===t?void 0:t.find((({el:t})=>t===e));n&&("click"===this.eventType?e.removeEventListener("click",n.fn):(e.removeEventListener("mouseenter",n.fn),e.removeEventListener("click",n.preventClickFn)))})),this.onToggleHandler=[],this.extraToggle&&this.extraToggle.removeEventListener("change",this.onExtraToggleChangeListener),window.$hsTabsCollection=window.$hsTabsCollection.filter((({element:e})=>e.el!==this.el))}static getInstance(e,t){const n=window.$hsTabsCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return n?t?n:n.element:null}static autoInit(){window.$hsTabsCollection||(window.$hsTabsCollection=[],document.addEventListener("keydown",(e=>l.accessibility(e)))),window.$hsTabsCollection&&(window.$hsTabsCollection=window.$hsTabsCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll('[role="tablist"]:not(select):not(.--prevent-on-load-init)').forEach((e=>{window.$hsTabsCollection.find((t=>{var n;return(null===(n=null==t?void 0:t.element)||void 0===n?void 0:n.el)===e}))||new l(e)}))}static open(e){const t=window.$hsTabsCollection.find((t=>Array.from(t.element.toggles).includes("string"==typeof e?document.querySelector(e):e))),n=t?Array.from(t.element.toggles).find((t=>t===("string"==typeof e?document.querySelector(e):e))):null;n&&!n.classList.contains("active")&&t.element.open(n)}static accessibility(e){var t;const n=document.querySelector("[data-hs-tab]:focus");if(n&&r.Fy.includes(e.code)&&!e.metaKey){const o=null===(t=n.closest('[role="tablist"]'))||void 0===t?void 0:t.getAttribute("data-hs-tabs-vertical");switch(e.preventDefault(),e.code){case"true"===o?"ArrowUp":"ArrowLeft":this.onArrow();break;case"true"===o?"ArrowDown":"ArrowRight":this.onArrow(!1);break;case"Home":this.onStartEnd();break;case"End":this.onStartEnd(!1)}}}static onArrow(e=!0){var t;const n=null===(t=document.querySelector("[data-hs-tab]:focus"))||void 0===t?void 0:t.closest('[role="tablist"]');if(!n)return;const o=window.$hsTabsCollection.find((e=>e.element.el===n));if(o){const t=e?Array.from(o.element.toggles).reverse():Array.from(o.element.toggles),n=t.find((e=>document.activeElement===e));let i=t.findIndex((e=>e===n));i=i+1<t.length?i+1:0,t[i].focus(),t[i].click()}}static onStartEnd(e=!0){var t;const n=null===(t=document.querySelector("[data-hs-tab]:focus"))||void 0===t?void 0:t.closest('[role="tablist"]');if(!n)return;const o=window.$hsTabsCollection.find((e=>e.element.el===n));if(o){const t=e?Array.from(o.element.toggles):Array.from(o.element.toggles).reverse();t.length&&(t[0].focus(),t[0].click())}}static on(e,t,n){const o=window.$hsTabsCollection.find((e=>Array.from(e.element.toggles).includes("string"==typeof t?document.querySelector(t):t)));o&&(o.element.events[e]=n)}}window.addEventListener("load",(()=>{l.autoInit()})),"undefined"!=typeof window&&(window.HSTabs=l);const a=l;var c=o.A;export{c as default};