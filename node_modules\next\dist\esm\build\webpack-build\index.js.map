{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["Log", "NextBuildContext", "Worker", "origDebug", "path", "exportTraceState", "recordTraceEvents", "debug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNamesArg", "compilerNames", "nextBuildSpan", "prunedBuildContext", "getWorker", "compilerName", "_worker", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "getStderr", "pipe", "stderr", "getStdout", "stdout", "worker", "_workerPool", "_workers", "_child", "on", "code", "signal", "combinedResult", "duration", "buildTraceContext", "curR<PERSON>ult", "worker<PERSON>ain", "buildContext", "traceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "end", "telemetryState", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "event", "webpackBuild", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": "AACA,YAAYA,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,SAASC,MAAM,QAAQ,iCAAgC;AACvD,OAAOC,eAAe,2BAA0B;AAEhD,OAAOC,UAAU,OAAM;AACvB,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,cAAa;AAEjE,MAAMC,QAAQJ,UAAU;AAExB,MAAMK,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAAsD;IAEtD,MAAMC,gBAAgBD,oBAAoBZ;IAC1C,MAAM,EAAEc,aAAa,EAAE,GAAGC,oBAAoB,GAAGtB;IAEjDsB,mBAAmBd,WAAW,GAAGA;IAEjC,MAAMe,YAAY,CAACC;YAeK;QAdtB,MAAMC,UAAU,IAAIxB,OAAOE,KAAKuB,IAAI,CAACC,WAAW,YAAY;YAC1DC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QACAT,QAAQU,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACvCZ,QAAQa,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEvC,KAAK,MAAMC,UAAW,EAAA,sBAAA,AAACf,QAAgBgB,WAAW,qBAA5B,oBAA8BC,QAAQ,KAAI,EAAE,CAE7D;YACHF,OAAOG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC,MAAMC;gBAC9B,IAAID,QAASC,UAAUA,WAAW,UAAW;oBAC3CxC,MACE,CAAC,SAAS,EAAEkB,aAAa,gCAAgC,EAAEqB,KAAK,aAAa,EAAEC,OAAO,CAAC;gBAE3F;YACF;QACF;QAEA,OAAOrB;IACT;IAEA,MAAMsB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAMzB,gBAAgBJ,cAAe;YA4BpC8B;QA3BJ,MAAMV,SAASjB,UAAUC;QAEzB,MAAM0B,YAAY,MAAMV,OAAOW,UAAU,CAAC;YACxCC,cAAc9B;YACdE;YACA6B,YAAY;gBACV,GAAGjD,kBAAkB;gBACrBkD,mBAAmB,EAAEjC,iCAAAA,cAAekC,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAInC,iBAAiB6B,UAAUO,gBAAgB,EAAE;YAC/CpD,kBAAkB6C,UAAUO,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMjB,OAAOkB,GAAG;QAEhB,sBAAsB;QACtBlD,cAAcC,UAAUD,aAAa0C,UAAU1C,WAAW;QAC1Dc,mBAAmBd,WAAW,GAAGA;QAEjC,IAAI0C,UAAUS,cAAc,EAAE;YAC5B3D,iBAAiB2D,cAAc,GAAGT,UAAUS,cAAc;QAC5D;QAEAZ,eAAeC,QAAQ,IAAIE,UAAUF,QAAQ;QAE7C,KAAIE,+BAAAA,UAAUD,iBAAiB,qBAA3BC,6BAA6BU,YAAY,EAAE;gBAUzCV;YATJ,MAAM,EAAEW,YAAY,EAAE,GAAGX,UAAUD,iBAAiB,CAACW,YAAY;YAEjE,IAAIC,cAAc;gBAChBd,eAAeE,iBAAiB,CAACW,YAAY,GAC3CV,UAAUD,iBAAiB,CAACW,YAAY;gBAC1Cb,eAAeE,iBAAiB,CAACW,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIX,gCAAAA,UAAUD,iBAAiB,qBAA3BC,8BAA6BY,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGb,UAAUD,iBAAiB,CAACa,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBhB,eAAeE,iBAAiB,CAACa,WAAW,GAC1CZ,UAAUD,iBAAiB,CAACa,WAAW;oBAEzCf,eAAeE,iBAAiB,CAACa,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAI3C,cAAc4C,MAAM,KAAK,GAAG;QAC9BjE,IAAIkE,KAAK,CAAC;IACZ;IAEA,OAAOlB;AACT;AAEA,OAAO,SAASmB,aACdC,UAAmB,EACnB/C,aAAmD;IAEnD,IAAI+C,YAAY;QACd7D,MAAM;QACN,OAAOY,uBAAuBE;IAChC,OAAO;QACLd,MAAM;QACN,MAAM8D,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAOA,iBAAiB,MAAM;IAChC;AACF"}