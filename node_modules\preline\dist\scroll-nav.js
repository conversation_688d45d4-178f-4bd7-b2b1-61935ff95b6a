!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var n in i)("object"==typeof exports?exports:t)[n]=i[n]}}(self,(()=>(()=>{"use strict";var t={292:function(t,e){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(e,"__esModule",{value:!0}),e.menuSearchHistory=e.classToClassList=e.htmlToElement=e.afterTransition=e.dispatch=e.debounce=e.isScrollable=e.isParentOrElementHidden=e.isJson=e.isIpadOS=e.isIOS=e.isDirectChild=e.isFormElement=e.isFocused=e.isEnoughSpace=e.getHighestZIndex=e.getZIndex=e.getClassPropertyAlt=e.getClassProperty=e.stringToBoolean=void 0;e.stringToBoolean=t=>"true"===t;e.getClassProperty=(t,e,i="")=>(window.getComputedStyle(t).getPropertyValue(e)||i).replace(" ","");e.getClassPropertyAlt=(t,e,i="")=>{let n="";return t.classList.forEach((t=>{t.includes(e)&&(n=t)})),n.match(/:(.*)]/)?n.match(/:(.*)]/)[1]:i};const i=t=>window.getComputedStyle(t).getPropertyValue("z-index");e.getZIndex=i;e.getHighestZIndex=t=>{let e=Number.NEGATIVE_INFINITY;return t.forEach((t=>{let n=i(t);"auto"!==n&&(n=parseInt(n,10),n>e&&(e=n))})),e};e.isDirectChild=(t,e)=>{const i=t.children;for(let t=0;t<i.length;t++)if(i[t]===e)return!0;return!1};e.isEnoughSpace=(t,e,i="auto",n=10,s=null)=>{const o=e.getBoundingClientRect(),r=s?s.getBoundingClientRect():null,l=window.innerHeight,a=r?o.top-r.top:o.top,c=(s?r.bottom:l)-o.bottom,d=t.clientHeight+n;return"bottom"===i?c>=d:"top"===i?a>=d:a>=d||c>=d};e.isFocused=t=>document.activeElement===t;e.isFormElement=t=>t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement;e.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isJson=t=>{if("string"!=typeof t)return!1;const e=t.trim()[0],i=t.trim().slice(-1);if("{"===e&&"}"===i||"["===e&&"]"===i)try{return JSON.parse(t),!0}catch(t){return!1}return!1};const n=t=>{if(!t)return!1;return"none"===window.getComputedStyle(t).display||n(t.parentElement)};e.isParentOrElementHidden=n;e.isScrollable=t=>{const e=window.getComputedStyle(t),i=e.overflowY,n=e.overflowX,s=("scroll"===i||"auto"===i)&&t.scrollHeight>t.clientHeight,o=("scroll"===n||"auto"===n)&&t.scrollWidth>t.clientWidth;return s||o};e.debounce=(t,e=200)=>{let i;return(...n)=>{clearTimeout(i),i=setTimeout((()=>{t.apply(this,n)}),e)}};e.dispatch=(t,e,i=null)=>{const n=new CustomEvent(t,{detail:{payload:i},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(n)};e.afterTransition=(t,e)=>{const i=()=>{e(),t.removeEventListener("transitionend",i,!0)},n=window.getComputedStyle(t),s=n.getPropertyValue("transition-duration");"none"!==n.getPropertyValue("transition-property")&&parseFloat(s)>0?t.addEventListener("transitionend",i,!0):e()};e.htmlToElement=t=>{const e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild};e.classToClassList=(t,e,i=" ",n="add")=>{t.split(i).forEach((t=>"add"===n?e.classList.add(t):e.classList.remove(t)))};const s={historyIndex:-1,addHistory(t){this.historyIndex=t},existsInHistory(t){return t>this.historyIndex},clearHistory(){this.historyIndex=-1}};e.menuSearchHistory=s},639:function(t,e,i){
/*
 * HSScrollNav
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const s=i(292),o=n(i(961));class r extends o.default{constructor(t,e){var i,n;super(t,e);const s=t.getAttribute("data-hs-scroll-nav"),o=s?JSON.parse(s):{},r=Object.assign(Object.assign(Object.assign({},{paging:!0,autoCentering:!1}),o),e);this.paging=null===(i=r.paging)||void 0===i||i,this.autoCentering=null!==(n=r.autoCentering)&&void 0!==n&&n,this.body=this.el.querySelector(".hs-scroll-nav-body"),this.items=this.body?Array.from(this.body.querySelectorAll(":scope > *")):[],this.prev=this.el.querySelector(".hs-scroll-nav-prev")||null,this.next=this.el.querySelector(".hs-scroll-nav-next")||null,this.setCurrentState(),this.init()}init(){if(!this.body||!this.items.length)return!1;this.createCollection(window.$hsScrollNavCollection,this),this.setCurrentState(),this.paging?(this.prev&&this.buildPrev(),this.next&&this.buildNext()):(this.prev&&this.buildPrevSingle(),this.next&&this.buildNextSingle()),this.autoCentering&&this.scrollToActiveElement(),this.body.addEventListener("scroll",(0,s.debounce)((()=>this.setCurrentState()),200)),window.addEventListener("resize",(0,s.debounce)((()=>{this.setCurrentState(),this.autoCentering&&this.scrollToActiveElement()}),200))}setCurrentState(){this.currentState={first:this.getFirstVisibleItem(),last:this.getLastVisibleItem(),center:this.getCenterVisibleItem()},this.prev&&this.setPrevToDisabled(),this.next&&this.setNextToDisabled()}setPrevToDisabled(){this.currentState.first===this.items[0]?(this.prev.setAttribute("disabled","disabled"),this.prev.classList.add("disabled")):(this.prev.removeAttribute("disabled"),this.prev.classList.remove("disabled"))}setNextToDisabled(){this.currentState.last===this.items[this.items.length-1]?(this.next.setAttribute("disabled","disabled"),this.next.classList.add("disabled")):(this.next.removeAttribute("disabled"),this.next.classList.remove("disabled"))}buildPrev(){this.prev&&this.prev.addEventListener("click",(()=>{const t=this.currentState.first;if(!t)return;const e=this.getVisibleItemsCount();let i=t;for(let t=0;t<e&&i.previousElementSibling;t++)i=i.previousElementSibling;this.goTo(i)}))}buildNext(){this.next&&this.next.addEventListener("click",(()=>{const t=this.currentState.last;if(!t)return;const e=this.getVisibleItemsCount();let i=t;for(let t=0;t<e&&i.nextElementSibling;t++)i=i.nextElementSibling;this.goTo(i)}))}buildPrevSingle(){var t;null===(t=this.prev)||void 0===t||t.addEventListener("click",(()=>{const t=this.currentState.first;if(!t)return;const e=t.previousElementSibling;e&&this.goTo(e)}))}buildNextSingle(){var t;null===(t=this.next)||void 0===t||t.addEventListener("click",(()=>{const t=this.currentState.last;if(!t)return;const e=t.nextElementSibling;e&&this.goTo(e)}))}getCenterVisibleItem(){const t=this.body.scrollLeft+this.body.clientWidth/2;let e=null,i=1/0;return this.items.forEach((n=>{const s=n.offsetLeft+n.offsetWidth/2,o=Math.abs(s-t);o<i&&(i=o,e=n)})),e}getFirstVisibleItem(){const t=this.body.getBoundingClientRect();for(let e of this.items){const i=e.getBoundingClientRect();if(i.left>=t.left&&i.right<=t.right)return e}return null}getLastVisibleItem(){const t=this.body.getBoundingClientRect();for(let e=this.items.length-1;e>=0;e--){const i=this.items[e],n=i.getBoundingClientRect();if(n.left<t.right&&n.right>t.left)return i}return null}getVisibleItemsCount(){const t=this.body.clientWidth;let e=0,i=0;for(let n of this.items){if(i+=n.offsetWidth,!(i<=t))break;e++}return e}scrollToActiveElement(){const t=this.body.querySelector(".active");if(!t)return!1;this.centerElement(t)}getCurrentState(){return this.currentState}goTo(t,e){t.scrollIntoView({behavior:"smooth",block:"nearest",inline:"nearest"});new IntersectionObserver(((i,n)=>{i.forEach((i=>{i.target===t&&i.isIntersecting&&("function"==typeof e&&e(),n.disconnect())}))}),{root:this.body,threshold:1}).observe(t)}centerElement(t,e="smooth"){if(!this.body.contains(t))return;const i=t.offsetLeft+t.offsetWidth/2-this.body.clientWidth/2;this.body.scrollTo({left:i,behavior:e})}destroy(){this.paging?(this.prev&&this.prev.removeEventListener("click",this.buildPrev),this.next&&this.next.removeEventListener("click",this.buildNext)):(this.prev&&this.prev.removeEventListener("click",this.buildPrevSingle),this.next&&this.next.removeEventListener("click",this.buildNextSingle)),window.removeEventListener("resize",(0,s.debounce)((()=>this.setCurrentState()),200)),window.$hsScrollNavCollection=window.$hsScrollNavCollection.filter((({element:t})=>t.el!==this.el))}static getInstance(t,e){const i=window.$hsScrollNavCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)||e.element.el===("string"==typeof t?document.querySelector(t):t)));return i?e?i:i.element.el:null}static autoInit(){window.$hsScrollNavCollection||(window.$hsScrollNavCollection=[]),window.$hsScrollNavCollection&&(window.$hsRemoveElementCollection=window.$hsRemoveElementCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll("[data-hs-scroll-nav]:not(.--prevent-on-load-init)").forEach((t=>{window.$hsScrollNavCollection.find((e=>{var i;return(null===(i=null==e?void 0:e.element)||void 0===i?void 0:i.el)===t}))||new r(t)}))}}window.addEventListener("load",(()=>{r.autoInit()})),"undefined"!=typeof window&&(window.HSScrollNav=r),e.default=r},961:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(t,e,i){this.el=t,this.options=e,this.events=i,this.el=t,this.options=e,this.events={}}createCollection(t,e){var i;t.push({id:(null===(i=null==e?void 0:e.el)||void 0===i?void 0:i.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}}},e={};var i=function i(n){var s=e[n];if(void 0!==s)return s.exports;var o=e[n]={exports:{}};return t[n].call(o.exports,o,o.exports,i),o.exports}(639);return i})()));