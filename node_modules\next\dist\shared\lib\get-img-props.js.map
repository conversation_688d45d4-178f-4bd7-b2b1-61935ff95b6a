{"version": 3, "sources": ["../../../src/shared/lib/get-img-props.ts"], "names": ["getImgProps", "VALID_LOADING_VALUES", "undefined", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "push", "length", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "Set", "map", "w", "find", "p", "generateImgAttrs", "config", "unoptimized", "quality", "loader", "srcSet", "last", "i", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "decoding", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "c", "imageConfigDefault", "imageSizes", "sort", "a", "b", "Error", "isDefaultLoader", "customImageLoader", "obj", "_", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "endsWith", "dangerouslyAllowSVG", "qualityInt", "process", "env", "NODE_ENV", "output", "position", "isNaN", "includes", "String", "warnOnce", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "pathname", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "window", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "type", "buffered", "console", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "getImageBlurSvg", "placeholder<PERSON><PERSON><PERSON>", "backgroundSize", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "props", "meta"], "mappings": ";;;;+BA2OgBA;;;eAAAA;;;0BA3OS;8BACO;6BACG;AA6EnC,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAkBzD,SAASC,gBACPC,GAAoC;IAEpC,OAAO,AAACA,IAAsBC,OAAO,KAAKH;AAC5C;AAEA,SAASI,kBACPF,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKF;AAC1C;AAEA,SAASK,eAAeH,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACdD,CAAAA,gBAAgBC,QACfE,kBAAkBF,IAAmB;AAE3C;AAEA,MAAMI,UAAU,IAAIC;AAIpB,IAAIC;AAEJ,SAASC,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,aAAa;QAC5B,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOC,OAAOC,QAAQ,CAACF,KAAKA,IAAIG;IAClC;IACA,IAAI,OAAOH,MAAM,YAAY,WAAWI,IAAI,CAACJ,IAAI;QAC/C,OAAOK,SAASL,GAAG;IACrB;IACA,OAAOG;AACT;AAEA,SAASG,UACP,KAAsC,EACtCC,KAAyB,EACzBC,KAAyB;IAFzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAIA,IAAIF,OAAO;QACT,yDAAyD;QACzD,MAAMG,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAaG,IAAI,CAACV,SAASQ,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAaI,MAAM,EAAE;YACvB,MAAMC,gBAAgBC,KAAKC,GAAG,IAAIP,gBAAgB;YAClD,OAAO;gBACLQ,QAAQV,SAASW,MAAM,CAAC,CAACC,IAAMA,KAAKb,WAAW,CAAC,EAAE,GAAGQ;gBACrDM,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQV;YAAUa,MAAM;QAAI;IACvC;IACA,IAAI,OAAOhB,UAAU,UAAU;QAC7B,OAAO;YAAEa,QAAQX;YAAac,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAII,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACjB;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACkB,GAAG,CACpC,CAACC,IAAMhB,SAASiB,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMhB,QAAQ,CAACA,SAASM,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAEI;QAAQG,MAAM;IAAI;AAC7B;AAkBA,SAASM,iBAAiB,KAQR;IARQ,IAAA,EACxBC,MAAM,EACNtC,GAAG,EACHuC,WAAW,EACXxB,KAAK,EACLyB,OAAO,EACPxB,KAAK,EACLyB,MAAM,EACU,GARQ;IASxB,IAAIF,aAAa;QACf,OAAO;YAAEvC;YAAK0C,QAAQ5C;YAAWkB,OAAOlB;QAAU;IACpD;IAEA,MAAM,EAAE8B,MAAM,EAAEG,IAAI,EAAE,GAAGjB,UAAUwB,QAAQvB,OAAOC;IAClD,MAAM2B,OAAOf,OAAOJ,MAAM,GAAG;IAE7B,OAAO;QACLR,OAAO,CAACA,SAASe,SAAS,MAAM,UAAUf;QAC1C0B,QAAQd,OACLK,GAAG,CACF,CAACC,GAAGU,IACF,AAAGH,OAAO;gBAAEH;gBAAQtC;gBAAKwC;gBAASzB,OAAOmB;YAAE,KAAG,MAC5CH,CAAAA,SAAS,MAAMG,IAAIU,IAAI,CAAA,IACtBb,MAENc,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD7C,KAAKyC,OAAO;YAAEH;YAAQtC;YAAKwC;YAASzB,OAAOa,MAAM,CAACe,KAAK;QAAC;IAC1D;AACF;AAKO,SAAS/C,YACd,KAyBa,EACbkD,MAKC;IA/BD,IAAA,EACE9C,GAAG,EACHgB,KAAK,EACLuB,cAAc,KAAK,EACnBQ,WAAW,KAAK,EAChBC,OAAO,EACPC,SAAS,EACTT,OAAO,EACPzB,KAAK,EACLmC,MAAM,EACNC,OAAO,KAAK,EACZC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,iBAAiB,EACjBC,cAAc,OAAO,EACrBC,WAAW,EACXC,aAAa,EACbC,WAAW,OAAO,EAClBC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACR,GAAGC,MACQ,GAzBb;IAyCA,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGvB;IAC9D,IAAIR;IACJ,IAAIgC,IAAIJ,WAAWK,+BAAkB;IACrC,IAAI,cAAcD,GAAG;QACnBhC,SAASgC;IACX,OAAO;QACL,MAAMpD,WAAW;eAAIoD,EAAErD,WAAW;eAAKqD,EAAEE,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAM1D,cAAcqD,EAAErD,WAAW,CAACwD,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrDrC,SAAS;YAAE,GAAGgC,CAAC;YAAEpD;YAAUD;QAAY;IACzC;IAEA,IAAI,OAAOoD,kBAAkB,aAAa;QACxC,MAAM,IAAIO,MACR;IAEJ;IACA,IAAInC,SAAgCwB,KAAKxB,MAAM,IAAI4B;IAEnD,sDAAsD;IACtD,OAAOJ,KAAKxB,MAAM;IAClB,OAAO,AAACwB,KAAavB,MAAM;IAE3B,6CAA6C;IAC7C,oDAAoD;IACpD,MAAMmC,kBAAkB,wBAAwBpC;IAEhD,IAAIoC,iBAAiB;QACnB,IAAIvC,OAAOG,MAAM,KAAK,UAAU;YAC9B,MAAM,IAAImC,MACR,AAAC,qBAAkB5E,MAAI,gCACpB;QAEP;IACF,OAAO;QACL,8CAA8C;QAC9C,+CAA+C;QAC/C,iDAAiD;QACjD,MAAM8E,oBAAoBrC;QAC1BA,SAAS,CAACsC;YACR,MAAM,EAAEzC,QAAQ0C,CAAC,EAAE,GAAGC,MAAM,GAAGF;YAC/B,OAAOD,kBAAkBG;QAC3B;IACF;IAEA,IAAIrB,QAAQ;QACV,IAAIA,WAAW,QAAQ;YACrBT,OAAO;QACT;QACA,MAAM+B,gBAAoE;YACxEC,WAAW;gBAAEC,UAAU;gBAAQlC,QAAQ;YAAO;YAC9CmC,YAAY;gBAAEtE,OAAO;gBAAQmC,QAAQ;YAAO;QAC9C;QACA,MAAMoC,gBAAoD;YACxDD,YAAY;YACZlC,MAAM;QACR;QACA,MAAMoC,cAAcL,aAAa,CAACtB,OAAO;QACzC,IAAI2B,aAAa;YACfnC,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGmC,WAAW;YAAC;QACrC;QACA,MAAMC,cAAcF,aAAa,CAAC1B,OAAO;QACzC,IAAI4B,eAAe,CAACxE,OAAO;YACzBA,QAAQwE;QACV;IACF;IAEA,IAAIC,YAAY;IAChB,IAAIC,WAAWnF,OAAOQ;IACtB,IAAI4E,YAAYpF,OAAO2C;IACvB,IAAI0C;IACJ,IAAIC;IACJ,IAAI1F,eAAeH,MAAM;QACvB,MAAM8F,kBAAkB/F,gBAAgBC,OAAOA,IAAIC,OAAO,GAAGD;QAE7D,IAAI,CAAC8F,gBAAgB9F,GAAG,EAAE;YACxB,MAAM,IAAI4E,MACR,AAAC,gJAA6ImB,KAAKC,SAAS,CAC1JF;QAGN;QACA,IAAI,CAACA,gBAAgB5C,MAAM,IAAI,CAAC4C,gBAAgB/E,KAAK,EAAE;YACrD,MAAM,IAAI6D,MACR,AAAC,6JAA0JmB,KAAKC,SAAS,CACvKF;QAGN;QAEAF,YAAYE,gBAAgBF,SAAS;QACrCC,aAAaC,gBAAgBD,UAAU;QACvCpC,cAAcA,eAAeqC,gBAAgBrC,WAAW;QACxDgC,YAAYK,gBAAgB9F,GAAG;QAE/B,IAAI,CAACmD,MAAM;YACT,IAAI,CAACuC,YAAY,CAACC,WAAW;gBAC3BD,WAAWI,gBAAgB/E,KAAK;gBAChC4E,YAAYG,gBAAgB5C,MAAM;YACpC,OAAO,IAAIwC,YAAY,CAACC,WAAW;gBACjC,MAAMM,QAAQP,WAAWI,gBAAgB/E,KAAK;gBAC9C4E,YAAYjE,KAAKwE,KAAK,CAACJ,gBAAgB5C,MAAM,GAAG+C;YAClD,OAAO,IAAI,CAACP,YAAYC,WAAW;gBACjC,MAAMM,QAAQN,YAAYG,gBAAgB5C,MAAM;gBAChDwC,WAAWhE,KAAKwE,KAAK,CAACJ,gBAAgB/E,KAAK,GAAGkF;YAChD;QACF;IACF;IACAjG,MAAM,OAAOA,QAAQ,WAAWA,MAAMyF;IAEtC,IAAIU,SACF,CAACpD,YAAaC,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI,CAAChD,OAAOA,IAAIoG,UAAU,CAAC,YAAYpG,IAAIoG,UAAU,CAAC,UAAU;QAC9D,uEAAuE;QACvE7D,cAAc;QACd4D,SAAS;IACX;IACA,IAAI7D,OAAOC,WAAW,EAAE;QACtBA,cAAc;IAChB;IACA,IAAIsC,mBAAmB7E,IAAIqG,QAAQ,CAAC,WAAW,CAAC/D,OAAOgE,mBAAmB,EAAE;QAC1E,yDAAyD;QACzD,+CAA+C;QAC/C/D,cAAc;IAChB;IACA,IAAIQ,UAAU;QACZW,gBAAgB;IAClB;IAEA,MAAM6C,aAAahG,OAAOiC;IAE1B,IAAIgE,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIpE,OAAOqE,MAAM,KAAK,YAAY9B,mBAAmB,CAACtC,aAAa;YACjE,MAAM,IAAIqC,MACP;QAML;QACA,IAAI,CAAC5E,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CuC,cAAc;QAChB,OAAO;YACL,IAAIY,MAAM;gBACR,IAAIpC,OAAO;oBACT,MAAM,IAAI6D,MACR,AAAC,qBAAkB5E,MAAI;gBAE3B;gBACA,IAAIkD,QAAQ;oBACV,MAAM,IAAI0B,MACR,AAAC,qBAAkB5E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOwD,QAAQ,KAAIxD,MAAMwD,QAAQ,KAAK,YAAY;oBACpD,MAAM,IAAIhC,MACR,AAAC,qBAAkB5E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOrC,KAAK,KAAIqC,MAAMrC,KAAK,KAAK,QAAQ;oBAC1C,MAAM,IAAI6D,MACR,AAAC,qBAAkB5E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOF,MAAM,KAAIE,MAAMF,MAAM,KAAK,QAAQ;oBAC5C,MAAM,IAAI0B,MACR,AAAC,qBAAkB5E,MAAI;gBAE3B;YACF,OAAO;gBACL,IAAI,OAAO0F,aAAa,aAAa;oBACnC,MAAM,IAAId,MACR,AAAC,qBAAkB5E,MAAI;gBAE3B,OAAO,IAAI6G,MAAMnB,WAAW;oBAC1B,MAAM,IAAId,MACR,AAAC,qBAAkB5E,MAAI,sFAAmFe,QAAM;gBAEpH;gBACA,IAAI,OAAO4E,cAAc,aAAa;oBACpC,MAAM,IAAIf,MACR,AAAC,qBAAkB5E,MAAI;gBAE3B,OAAO,IAAI6G,MAAMlB,YAAY;oBAC3B,MAAM,IAAIf,MACR,AAAC,qBAAkB5E,MAAI,uFAAoFkD,SAAO;gBAEtH;YACF;QACF;QACA,IAAI,CAACrD,qBAAqBiH,QAAQ,CAAC9D,UAAU;YAC3C,MAAM,IAAI4B,MACR,AAAC,qBAAkB5E,MAAI,iDAA8CgD,UAAQ,wBAAqBnD,qBAAqBoC,GAAG,CACxH8E,QACAlE,IAAI,CAAC,OAAK;QAEhB;QACA,IAAIE,YAAYC,YAAY,QAAQ;YAClC,MAAM,IAAI4B,MACR,AAAC,qBAAkB5E,MAAI;QAE3B;QACA,IACEwD,gBAAgB,WAChBA,gBAAgB,UAChB,CAACA,YAAY4C,UAAU,CAAC,gBACxB;YACA,MAAM,IAAIxB,MACR,AAAC,qBAAkB5E,MAAI,2CAAwCwD,cAAY;QAE/E;QACA,IAAIA,gBAAgB,SAAS;YAC3B,IAAIkC,YAAYC,aAAaD,WAAWC,YAAY,MAAM;gBACxDqB,IAAAA,kBAAQ,EACN,AAAC,qBAAkBhH,MAAI;YAE3B;QACF;QACA,IAAIwD,gBAAgB,UAAU,CAACC,aAAa;YAC1C,MAAMwD,iBAAiB;gBAAC;gBAAQ;gBAAO;gBAAQ;aAAO,CAAC,iCAAiC;;YAExF,MAAM,IAAIrC,MACR,AAAC,qBAAkB5E,MAAI,6TAGkEiH,eAAepE,IAAI,CACxG,OACA;QAIR;QACA,IAAI,SAASoB,MAAM;YACjB+C,IAAAA,kBAAQ,EACN,AAAC,qBAAkBhH,MAAI;QAE3B;QAEA,IAAI,CAACuC,eAAe,CAACsC,iBAAiB;YACpC,MAAMqC,SAASzE,OAAO;gBACpBH;gBACAtC;gBACAe,OAAO2E,YAAY;gBACnBlD,SAAS+D,cAAc;YACzB;YACA,IAAIY;YACJ,IAAI;gBACFA,MAAM,IAAIC,IAAIF;YAChB,EAAE,OAAOG,KAAK,CAAC;YACf,IAAIH,WAAWlH,OAAQmH,OAAOA,IAAIG,QAAQ,KAAKtH,OAAO,CAACmH,IAAII,MAAM,EAAG;gBAClEP,IAAAA,kBAAQ,EACN,AAAC,qBAAkBhH,MAAI,4HACpB;YAEP;QACF;QAEA,IAAIuD,mBAAmB;YACrByD,IAAAA,kBAAQ,EACN,AAAC,qBAAkBhH,MAAI;QAE3B;QAEA,KAAK,MAAM,CAACwH,WAAWC,YAAY,IAAIC,OAAOC,OAAO,CAAC;YACpD/D;YACAC;YACAC;YACAC;YACAC;QACF,GAAI;YACF,IAAIyD,aAAa;gBACfT,IAAAA,kBAAQ,EACN,AAAC,qBAAkBhH,MAAI,wBAAqBwH,YAAU,0CACnD;YAEP;QACF;QAEA,IACE,OAAOI,WAAW,eAClB,CAACtH,gBACDsH,OAAOC,mBAAmB,EAC1B;YACAvH,eAAe,IAAIuH,oBAAoB,CAACC;gBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;wBAE3BD;oBADf,0EAA0E;oBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgB/H,GAAG,KAAI;oBACtC,MAAMmI,WAAW/H,QAAQgI,GAAG,CAACH;oBAC7B,IACEE,YACA,CAACA,SAASpF,QAAQ,IAClBoF,SAAS3E,WAAW,KAAK,WACzB,CAAC2E,SAASnI,GAAG,CAACoG,UAAU,CAAC,YACzB,CAAC+B,SAASnI,GAAG,CAACoG,UAAU,CAAC,UACzB;wBACA,iDAAiD;wBACjDY,IAAAA,kBAAQ,EACN,AAAC,qBAAkBmB,SAASnI,GAAG,GAAC,8HAC7B;oBAEP;gBACF;YACF;YACA,IAAI;gBACFM,aAAa+H,OAAO,CAAC;oBACnBC,MAAM;oBACNC,UAAU;gBACZ;YACF,EAAE,OAAOlB,KAAK;gBACZ,oCAAoC;gBACpCmB,QAAQC,KAAK,CAACpB;YAChB;QACF;IACF;IACA,MAAMqB,WAAWhB,OAAOiB,MAAM,CAC5BxF,OACI;QACEyD,UAAU;QACV1D,QAAQ;QACRnC,OAAO;QACP6H,MAAM;QACNC,KAAK;QACLC,OAAO;QACPC,QAAQ;QACRlF;QACAC;IACF,IACA,CAAC,GACLK,cAAc,CAAC,IAAI;QAAE6E,OAAO;IAAc,GAC1C5F;IAGF,MAAM6F,kBACJ,CAAC7E,gBAAgBZ,gBAAgB,UAC7BA,gBAAgB,SACd,AAAC,2CAAwC0F,IAAAA,6BAAe,EAAC;QACvDxD;QACAC;QACAC;QACAC;QACApC,aAAaA,eAAe;QAC5BI,WAAW6E,SAAS7E,SAAS;IAC/B,KAAG,OACH,AAAC,UAAOL,cAAY,KAAI,uBAAuB;OACjD;IAEN,IAAI2F,mBAAmBF,kBACnB;QACEG,gBAAgBV,SAAS7E,SAAS,IAAI;QACtCwF,oBAAoBX,SAAS5E,cAAc,IAAI;QAC/CwF,kBAAkB;QAClBL;IACF,IACA,CAAC;IAEL,IAAIzC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IACEyC,iBAAiBF,eAAe,IAChCzF,gBAAgB,WAChBC,+BAAAA,YAAa2C,UAAU,CAAC,OACxB;YACA,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF+C,iBAAiBF,eAAe,GAAG,AAAC,UAAOxF,cAAY;QACzD;IACF;IAEA,MAAM8F,gBAAgBlH,iBAAiB;QACrCC;QACAtC;QACAuC;QACAxB,OAAO2E;QACPlD,SAAS+D;QACTvF;QACAyB;IACF;IAEA,IAAI+D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAOkB,WAAW,aAAa;YACjC,IAAI4B;YACJ,IAAI;gBACFA,UAAU,IAAIpC,IAAImC,cAAcvJ,GAAG;YACrC,EAAE,OAAOyJ,GAAG;gBACVD,UAAU,IAAIpC,IAAImC,cAAcvJ,GAAG,EAAE4H,OAAO8B,QAAQ,CAACC,IAAI;YAC3D;YACAvJ,QAAQwJ,GAAG,CAACJ,QAAQG,IAAI,EAAE;gBAAE3J;gBAAK+C;gBAAUS;YAAY;QACzD;IACF;IAEA,MAAMqG,QAAkB;QACtB,GAAG5F,IAAI;QACPjB,SAASmD,SAAS,SAASnD;QAC3BU;QACA3C,OAAO2E;QACPxC,QAAQyC;QACRhC;QACAV;QACAG,OAAO;YAAE,GAAGsF,QAAQ;YAAE,GAAGS,gBAAgB;QAAC;QAC1CnI,OAAOuI,cAAcvI,KAAK;QAC1B0B,QAAQ6G,cAAc7G,MAAM;QAC5B1C,KAAKqD,eAAekG,cAAcvJ,GAAG;IACvC;IACA,MAAM8J,OAAO;QAAEvH;QAAaQ;QAAUS;QAAaL;IAAK;IACxD,OAAO;QAAE0G;QAAOC;IAAK;AACvB"}