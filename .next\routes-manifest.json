{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/case-studies", "regex": "^/case\\-studies(?:/)?$", "routeKeys": {}, "namedRegex": "^/case\\-studies(?:/)?$"}, {"page": "/checklisty", "regex": "^/checklisty(?:/)?$", "routeKeys": {}, "namedRegex": "^/checklisty(?:/)?$"}, {"page": "/cookies", "regex": "^/cookies(?:/)?$", "routeKeys": {}, "namedRegex": "^/cookies(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/linki", "regex": "^/linki(?:/)?$", "routeKeys": {}, "namedRegex": "^/linki(?:/)?$"}, {"page": "/o-projekcie", "regex": "^/o\\-projekcie(?:/)?$", "routeKeys": {}, "namedRegex": "^/o\\-projekcie(?:/)?$"}, {"page": "/polityka-p<PERSON><PERSON><PERSON><PERSON>", "regex": "^/polityka\\-p<PERSON><PERSON><PERSON><PERSON>(?:/)?$", "routeKeys": {}, "namedRegex": "^/polityka\\-p<PERSON><PERSON><PERSON><PERSON>(?:/)?$"}, {"page": "/poradniki", "regex": "^/poradniki(?:/)?$", "routeKeys": {}, "namedRegex": "^/poradniki(?:/)?$"}, {"page": "/regulamin", "regex": "^/regulamin(?:/)?$", "routeKeys": {}, "namedRegex": "^/regulamin(?:/)?$"}, {"page": "/slownik", "regex": "^/slownik(?:/)?$", "routeKeys": {}, "namedRegex": "^/slownik(?:/)?$"}, {"page": "/zasoby", "regex": "^/zasoby(?:/)?$", "routeKeys": {}, "namedRegex": "^/zasoby(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}