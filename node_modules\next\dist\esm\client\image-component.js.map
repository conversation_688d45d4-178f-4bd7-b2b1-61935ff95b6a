{"version": 3, "sources": ["../../src/client/image-component.tsx"], "names": ["React", "useRef", "useEffect", "useCallback", "useContext", "useMemo", "useState", "forwardRef", "use", "ReactDOM", "Head", "getImgProps", "imageConfigDefault", "ImageConfigContext", "warnOnce", "RouterContext", "defaultLoader", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "placeholder", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "unoptimized", "sizesInput", "src", "p", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "current", "event", "Event", "Object", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "NODE_ENV", "origSrc", "URL", "searchParams", "get", "getAttribute", "widthViewportRatio", "getBoundingClientRect", "width", "innerWidth", "position", "getComputedStyle", "valid", "includes", "map", "String", "join", "height", "heightModified", "toString", "widthModified", "getDynamicProps", "fetchPriority", "Boolean", "fetchpriority", "ImageElement", "forwardedRef", "srcSet", "sizes", "decoding", "className", "style", "loading", "fill", "setShowAltText", "onLoad", "onError", "rest", "data-nimg", "ref", "console", "error", "complete", "ImagePreload", "isAppRouter", "imgAttributes", "opts", "as", "imageSrcSet", "imageSizes", "crossOrigin", "referrerPolicy", "preload", "link", "rel", "href", "undefined", "Image", "props", "pagesRouter", "configContext", "config", "c", "allSizes", "deviceSizes", "sort", "a", "b", "onLoadingComplete", "blurComplete", "showAltText", "meta", "imgMeta", "imgConf", "priority"], "mappings": "AAAA;;AAEA,OAAOA,SACLC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,GAAG,QACE,QAAO;AACd,OAAOC,cAAc,YAAW;AAChC,OAAOC,UAAU,qBAAoB;AACrC,SAASC,WAAW,QAAQ,8BAA6B;AAYzD,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,SAASC,QAAQ,QAAQ,gCAA+B;AACxD,SAASC,aAAa,QAAQ,8CAA6C;AAE3E,iDAAiD;AACjD,OAAOC,mBAAmB,oCAAmC;AAE7D,4CAA4C;AAC5C,MAAMC,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAE/C,IAAI,OAAOC,WAAW,aAAa;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAmBA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASC,cACPC,GAA2B,EAC3BC,WAA6B,EAC7BC,SAAqD,EACrDC,oBAA2E,EAC3EC,eAAqC,EACrCC,WAAoB,EACpBC,UAA8B;IAE9B,MAAMC,MAAMP,uBAAAA,IAAKO,GAAG;IACpB,IAAI,CAACP,OAAOA,GAAG,CAAC,kBAAkB,KAAKO,KAAK;QAC1C;IACF;IACAP,GAAG,CAAC,kBAAkB,GAAGO;IACzB,MAAMC,IAAI,YAAYR,MAAMA,IAAIS,MAAM,KAAKC,QAAQC,OAAO;IAC1DH,EAAEI,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACb,IAAIc,aAAa,IAAI,CAACd,IAAIe,WAAW,EAAE;YAC1C,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACA,IAAId,gBAAgB,SAAS;YAC3BG,gBAAgB;QAClB;QACA,IAAIF,6BAAAA,UAAWc,OAAO,EAAE;YACtB,+CAA+C;YAC/C,0CAA0C;YAC1C,2CAA2C;YAC3C,MAAMC,QAAQ,IAAIC,MAAM;YACxBC,OAAOC,cAAc,CAACH,OAAO,UAAU;gBAAEI,UAAU;gBAAOC,OAAOtB;YAAI;YACrE,IAAIuB,YAAY;YAChB,IAAIC,UAAU;YACdtB,UAAUc,OAAO,CAAC;gBAChB,GAAGC,KAAK;gBACRQ,aAAaR;gBACbS,eAAe1B;gBACf2B,QAAQ3B;gBACR4B,oBAAoB,IAAML;gBAC1BM,sBAAsB,IAAML;gBAC5BM,SAAS,KAAO;gBAChBC,gBAAgB;oBACdR,YAAY;oBACZN,MAAMc,cAAc;gBACtB;gBACAC,iBAAiB;oBACfR,UAAU;oBACVP,MAAMe,eAAe;gBACvB;YACF;QACF;QACA,IAAI7B,wCAAAA,qBAAsBa,OAAO,EAAE;YACjCb,qBAAqBa,OAAO,CAAChB;QAC/B;QACA,IAAIP,QAAQC,GAAG,CAACuC,QAAQ,KAAK,cAAc;YACzC,MAAMC,UAAU,IAAIC,IAAI5B,KAAK,YAAY6B,YAAY,CAACC,GAAG,CAAC,UAAU9B;YACpE,IAAIP,IAAIsC,YAAY,CAAC,iBAAiB,QAAQ;gBAC5C,IAAI,CAACjC,eAAgB,CAAA,CAACC,cAAcA,eAAe,OAAM,GAAI;oBAC3D,IAAIiC,qBACFvC,IAAIwC,qBAAqB,GAAGC,KAAK,GAAG7C,OAAO8C,UAAU;oBACvD,IAAIH,qBAAqB,KAAK;wBAC5B,IAAIjC,eAAe,SAAS;4BAC1BjB,SACE,AAAC,qBAAkB6C,UAAQ;wBAE/B,OAAO;4BACL7C,SACE,AAAC,qBAAkB6C,UAAQ;wBAE/B;oBACF;gBACF;gBACA,IAAIlC,IAAIc,aAAa,EAAE;oBACrB,MAAM,EAAE6B,QAAQ,EAAE,GAAG/C,OAAOgD,gBAAgB,CAAC5C,IAAIc,aAAa;oBAC9D,MAAM+B,QAAQ;wBAAC;wBAAY;wBAAS;qBAAW;oBAC/C,IAAI,CAACA,MAAMC,QAAQ,CAACH,WAAW;wBAC7BtD,SACE,AAAC,qBAAkB6C,UAAQ,wEAAqES,WAAS,wBAAqBE,MAC3HE,GAAG,CAACC,QACJC,IAAI,CAAC,OAAK;oBAEjB;gBACF;gBACA,IAAIjD,IAAIkD,MAAM,KAAK,GAAG;oBACpB7D,SACE,AAAC,qBAAkB6C,UAAQ;gBAE/B;YACF;YAEA,MAAMiB,iBACJnD,IAAIkD,MAAM,CAACE,QAAQ,OAAOpD,IAAIsC,YAAY,CAAC;YAC7C,MAAMe,gBAAgBrD,IAAIyC,KAAK,CAACW,QAAQ,OAAOpD,IAAIsC,YAAY,CAAC;YAChE,IACE,AAACa,kBAAkB,CAACE,iBACnB,CAACF,kBAAkBE,eACpB;gBACAhE,SACE,AAAC,qBAAkB6C,UAAQ;YAE/B;QACF;IACF;AACF;AAEA,SAASoB,gBACPC,aAAsB;IAEtB,IAAIC,QAAQzE,MAAM;QAChB,kDAAkD;QAClD,iDAAiD;QACjD,mDAAmD;QACnD,OAAO;YAAEwE;QAAc;IACzB;IACA,uDAAuD;IACvD,4CAA4C;IAC5C,OAAO;QAAEE,eAAeF;IAAc;AACxC;AAEA,MAAMG,6BAAe5E,WACnB,QAwBE6E;QAvBA,EACEpD,GAAG,EACHqD,MAAM,EACNC,KAAK,EACLX,MAAM,EACNT,KAAK,EACLqB,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLT,aAAa,EACbtD,WAAW,EACXgE,OAAO,EACP5D,WAAW,EACX6D,IAAI,EACJhE,SAAS,EACTC,oBAAoB,EACpBC,eAAe,EACf+D,cAAc,EACd7D,UAAU,EACV8D,MAAM,EACNC,OAAO,EACP,GAAGC,MACJ;IAGD,qBACE,KAACtE;QACE,GAAGsE,IAAI;QACP,GAAGhB,gBAAgBC,cAAc;QAClC,qEAAqE;QACrE,wEAAwE;QACxE,qDAAqD;QACrDU,SAASA;QACTxB,OAAOA;QACPS,QAAQA;QACRY,UAAUA;QACVS,aAAWL,OAAO,SAAS;QAC3BH,WAAWA;QACXC,OAAOA;QACP,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtDH,OAAOA;QACPD,QAAQA;QACRrD,KAAKA;QACLiE,KAAK9F,YACH,CAACsB;YACC,IAAI2D,cAAc;gBAChB,IAAI,OAAOA,iBAAiB,YAAYA,aAAa3D;qBAChD,IAAI,OAAO2D,iBAAiB,UAAU;oBACzC,+EAA+E;oBAC/EA,aAAa3C,OAAO,GAAGhB;gBACzB;YACF;YACA,IAAI,CAACA,KAAK;gBACR;YACF;YACA,IAAIqE,SAAS;gBACX,2EAA2E;gBAC3E,iFAAiF;gBACjF,kFAAkF;gBAClF,0CAA0C;gBAC1CrE,IAAIO,GAAG,GAAGP,IAAIO,GAAG;YACnB;YACA,IAAId,QAAQC,GAAG,CAACuC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAAC1B,KAAK;oBACRkE,QAAQC,KAAK,CAAE,6CAA4C1E;gBAC7D;gBACA,IAAIA,IAAIsC,YAAY,CAAC,WAAW,MAAM;oBACpCmC,QAAQC,KAAK,CACV;gBAEL;YACF;YACA,IAAI1E,IAAI2E,QAAQ,EAAE;gBAChB5E,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;YAEJ;QACF,GACA;YACEC;YACAN;YACAC;YACAC;YACAC;YACAiE;YACAhE;YACAC;YACAqD;SACD;QAEHS,QAAQ,CAACnD;YACP,MAAMjB,MAAMiB,MAAMS,aAAa;YAC/B3B,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;QAEJ;QACA+D,SAAS,CAACpD;YACR,qEAAqE;YACrEkD,eAAe;YACf,IAAIlE,gBAAgB,SAAS;gBAC3B,2EAA2E;gBAC3EG,gBAAgB;YAClB;YACA,IAAIiE,SAAS;gBACXA,QAAQpD;YACV;QACF;;AAGN;AAGF,SAAS2D,aAAa,KAMrB;IANqB,IAAA,EACpBC,WAAW,EACXC,aAAa,EAId,GANqB;IAOpB,MAAMC,OAAO;QACXC,IAAI;QACJC,aAAaH,cAAclB,MAAM;QACjCsB,YAAYJ,cAAcjB,KAAK;QAC/BsB,aAAaL,cAAcK,WAAW;QACtCC,gBAAgBN,cAAcM,cAAc;QAC5C,GAAG9B,gBAAgBwB,cAAcvB,aAAa,CAAC;IACjD;IAEA,IAAIsB,eAAe7F,SAASqG,OAAO,EAAE;QACnC,mDAAmD;QACnDrG,SAASqG,OAAO,CACdP,cAAcvE,GAAG,EACjB,8DAA8D;QAC9DwE;QAEF,OAAO;IACT;IAEA,qBACE,KAAC9F;kBACC,cAAA,KAACqG;YAOCC,KAAI;YACJ,sEAAsE;YACtE,qEAAqE;YACrE,sDAAsD;YACtD,EAAE;YACF,8EAA8E;YAC9EC,MAAMV,cAAclB,MAAM,GAAG6B,YAAYX,cAAcvE,GAAG;YACzD,GAAGwE,IAAI;WAZN,YACAD,cAAcvE,GAAG,GACjBuE,cAAclB,MAAM,GACpBkB,cAAcjB,KAAK;;AAa7B;AAEA;;;;CAIC,GACD,OAAO,MAAM6B,sBAAQ5G,WACnB,CAAC6G,OAAOhC;IACN,MAAMiC,cAAcjH,WAAWW;IAC/B,0DAA0D;IAC1D,MAAMuF,cAAc,CAACe;IAErB,MAAMC,gBAAgBlH,WAAWS;IACjC,MAAM0G,SAASlH,QAAQ;QACrB,MAAMmH,IAAIvG,aAAaqG,iBAAiB1G;QACxC,MAAM6G,WAAW;eAAID,EAAEE,WAAW;eAAKF,EAAEb,UAAU;SAAC,CAACgB,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMH,cAAcF,EAAEE,WAAW,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,OAAO;YAAE,GAAGL,CAAC;YAAEC;YAAUC;QAAY;IACvC,GAAG;QAACJ;KAAc;IAElB,MAAM,EAAEzB,MAAM,EAAEiC,iBAAiB,EAAE,GAAGV;IACtC,MAAMzF,YAAY1B,OAAO4F;IAEzB3F,UAAU;QACRyB,UAAUc,OAAO,GAAGoD;IACtB,GAAG;QAACA;KAAO;IAEX,MAAMjE,uBAAuB3B,OAAO6H;IAEpC5H,UAAU;QACR0B,qBAAqBa,OAAO,GAAGqF;IACjC,GAAG;QAACA;KAAkB;IAEtB,MAAM,CAACC,cAAclG,gBAAgB,GAAGvB,SAAS;IACjD,MAAM,CAAC0H,aAAapC,eAAe,GAAGtF,SAAS;IAE/C,MAAM,EAAE8G,OAAOb,aAAa,EAAE0B,MAAMC,OAAO,EAAE,GAAGvH,YAAYyG,OAAO;QACjEpG;QACAmH,SAASZ;QACTQ;QACAC;IACF;IAEA,qBACE;;0BAEI,KAAC7C;gBACE,GAAGoB,aAAa;gBACjBzE,aAAaoG,QAAQpG,WAAW;gBAChCJ,aAAawG,QAAQxG,WAAW;gBAChCiE,MAAMuC,QAAQvC,IAAI;gBAClBhE,WAAWA;gBACXC,sBAAsBA;gBACtBC,iBAAiBA;gBACjB+D,gBAAgBA;gBAChB7D,YAAYqF,MAAM9B,KAAK;gBACvBW,KAAKb;;YAGR8C,QAAQE,QAAQ,iBACf,KAAC/B;gBACCC,aAAaA;gBACbC,eAAeA;iBAEf;;;AAGV,GACD"}