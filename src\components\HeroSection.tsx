import Image from 'next/image'
import Link from 'next/link'
import { Book<PERSON><PERSON>, ArrowRight, ExternalLink, CheckCircle, Users, Clock } from 'lucide-react'

const HeroSection = () => {
  return (
    <section className="relative bg-gradient-to-br from-primary-50 via-white to-secondary-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="space-y-10">
            <div className="space-y-6">
              <div className="inline-flex items-center gap-x-3 bg-primary-100 text-primary-800 px-6 py-3 rounded-full font-semibold shadow-soft">
                <BookOpen className="w-5 h-5" />
                <span>Edukacyjne zaplecze treściowe</span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-secondary-900 leading-tight">
                <span className="gradient-text">Wiedza i wsparcie</span>
                <br />
                <span className="text-secondary-700">po trudnych zdarzeniach</span>
              </h1>

              <p className="text-xl text-secondary-600 leading-relaxed max-w-2xl">
                Praktyczne poradniki, checklisty i case studies, które pomogą Ci zrozumieć
                i poradzić sobie z konsekwencjami traumatycznych wydarzeń. Edukacja, która wspiera w trudnych chwilach.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/poradniki"
                className="inline-flex items-center justify-center gap-x-3 bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-xl font-bold transition-all duration-200 hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                <BookOpen className="w-5 h-5" />
                <span>Przeglądaj poradniki</span>
                <ArrowRight className="w-5 h-5" />
              </Link>

              <a
                href="https://www.solvictus.pl"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center gap-x-3 bg-white hover:bg-primary-50 text-primary-600 border-2 border-primary-600 px-8 py-4 rounded-xl font-bold transition-all duration-200 hover:shadow-soft focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                <span>Potrzebujesz pomocy?</span>
                <ExternalLink className="w-5 h-5" />
              </a>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              <div className="flex items-center gap-x-3">
                <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <div className="font-bold text-secondary-900">Sprawdzone</div>
                  <div className="text-sm text-secondary-600">Ekspertami</div>
                </div>
              </div>
              <div className="flex items-center gap-x-3">
                <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                  <Users className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <div className="font-bold text-secondary-900">1000+</div>
                  <div className="text-sm text-secondary-600">Osób pomogło</div>
                </div>
              </div>
              <div className="flex items-center gap-x-3">
                <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                  <Clock className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <div className="font-bold text-secondary-900">24/7</div>
                  <div className="text-sm text-secondary-600">Dostępność</div>
                </div>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="relative lg:order-first">
            <div className="relative rounded-3xl overflow-hidden shadow-large">
              <Image
                src="/images/czesto_zadawane_pytania_1.jpg"
                alt="Edukacja i wsparcie po trudnych zdarzeniach"
                width={600}
                height={500}
                className="w-full h-auto object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
            </div>

            {/* Floating cards */}
            <div className="absolute -bottom-8 -left-8 bg-white rounded-2xl shadow-large p-6 max-w-xs border border-secondary-100">
              <div className="flex items-center gap-x-4">
                <div className="w-14 h-14 bg-primary-100 rounded-2xl flex items-center justify-center">
                  <BookOpen className="w-7 h-7 text-primary-600" />
                </div>
                <div>
                  <div className="font-bold text-secondary-900">Praktyczna wiedza</div>
                  <div className="text-sm text-secondary-600">Sprawdzone rozwiązania</div>
                </div>
              </div>
            </div>

            <div className="absolute -top-8 -right-8 bg-white rounded-2xl shadow-large p-4 border border-secondary-100">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-600">50+</div>
                <div className="text-sm text-secondary-600">Poradników</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute top-0 right-0 -z-10 opacity-20">
        <div className="w-96 h-96 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full blur-3xl"></div>
      </div>
      <div className="absolute bottom-0 left-0 -z-10 opacity-15">
        <div className="w-80 h-80 bg-gradient-to-tr from-secondary-300 to-secondary-400 rounded-full blur-3xl"></div>
      </div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -z-10 opacity-5">
        <div className="w-[600px] h-[600px] bg-gradient-to-r from-primary-200 to-secondary-200 rounded-full blur-3xl"></div>
      </div>
    </section>
  )
}

export default HeroSection
