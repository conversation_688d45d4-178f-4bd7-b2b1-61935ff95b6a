!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var o in n)("object"==typeof exports?exports:e)[o]=n[o]}}(self,(()=>(()=>{"use strict";var e={223:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BREAKPOINTS=t.COMBO_BOX_ACCESSIBILITY_KEY_SET=t.SELECT_ACCESSIBILITY_KEY_SET=t.TABS_ACCESSIBILITY_KEY_SET=t.OVERLAY_ACCESSIBILITY_KEY_SET=t.DROPDOWN_ACCESSIBILITY_KEY_SET=t.POSITIONS=void 0,t.POSITIONS={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"},t.DROPDOWN_ACCESSIBILITY_KEY_SET=["Escape","ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Home","End","Enter"],t.OVERLAY_ACCESSIBILITY_KEY_SET=["Escape","Tab"],t.TABS_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End"],t.SELECT_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter","Space","Tab"],t.COMBO_BOX_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter"],t.BREAKPOINTS={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},292:function(e,t){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0;t.stringToBoolean=e=>"true"===e;t.getClassProperty=(e,t,n="")=>(window.getComputedStyle(e).getPropertyValue(t)||n).replace(" ","");t.getClassPropertyAlt=(e,t,n="")=>{let o="";return e.classList.forEach((e=>{e.includes(t)&&(o=e)})),o.match(/:(.*)]/)?o.match(/:(.*)]/)[1]:n};const n=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=n;t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let o=n(e);"auto"!==o&&(o=parseInt(o,10),o>t&&(t=o))})),t};t.isDirectChild=(e,t)=>{const n=e.children;for(let e=0;e<n.length;e++)if(n[e]===t)return!0;return!1};t.isEnoughSpace=(e,t,n="auto",o=10,i=null)=>{const s=t.getBoundingClientRect(),r=i?i.getBoundingClientRect():null,l=window.innerHeight,c=r?s.top-r.top:s.top,a=(i?r.bottom:l)-s.bottom,u=e.clientHeight+o;return"bottom"===n?a>=u:"top"===n?c>=u:c>=u||a>=u};t.isFocused=e=>document.activeElement===e;t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement;t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],n=e.trim().slice(-1);if("{"===t&&"}"===n||"["===t&&"]"===n)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const o=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||o(e.parentElement)};t.isParentOrElementHidden=o;t.isScrollable=e=>{const t=window.getComputedStyle(e),n=t.overflowY,o=t.overflowX,i=("scroll"===n||"auto"===n)&&e.scrollHeight>e.clientHeight,s=("scroll"===o||"auto"===o)&&e.scrollWidth>e.clientWidth;return i||s};t.debounce=(e,t=200)=>{let n;return(...o)=>{clearTimeout(n),n=setTimeout((()=>{e.apply(this,o)}),t)}};t.dispatch=(e,t,n=null)=>{const o=new CustomEvent(e,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(o)};t.afterTransition=(e,t)=>{const n=()=>{t(),e.removeEventListener("transitionend",n,!0)},o=window.getComputedStyle(e),i=o.getPropertyValue("transition-duration");"none"!==o.getPropertyValue("transition-property")&&parseFloat(i)>0?e.addEventListener("transitionend",n,!0):t()};t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild};t.classToClassList=(e,t,n=" ",o="add")=>{e.split(n).forEach((e=>"add"===o?t.classList.add(e):t.classList.remove(e)))};const i={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=i},891:function(e,t,n){
/*
 * HSDropdown
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const i=n(292),s=n(949),r=o(n(961)),l=n(223);class c extends r.default{constructor(e,t,n){super(e,t,n),this.longPressTimer=null,this.onTouchStartListener=null,this.onTouchEndListener=null,this.toggle=this.el.querySelector(":scope > .hs-dropdown-toggle")||this.el.querySelector(":scope > .hs-dropdown-toggle-wrapper > .hs-dropdown-toggle")||this.el.children[0],this.closers=Array.from(this.el.querySelectorAll(":scope .hs-dropdown-close"))||null,this.menu=this.el.querySelector(":scope > .hs-dropdown-menu"),this.eventMode=(0,i.getClassProperty)(this.el,"--trigger","click"),this.closeMode=(0,i.getClassProperty)(this.el,"--auto-close","true"),this.hasAutofocus=(0,i.stringToBoolean)((0,i.getClassProperty)(this.el,"--has-autofocus","true")||"true"),this.animationInProcess=!1,this.onCloserClickListener=[],this.toggle&&this.menu&&this.init()}elementMouseEnter(){this.onMouseEnterHandler()}elementMouseLeave(){this.onMouseLeaveHandler()}toggleClick(e){this.onClickHandler(e)}toggleContextMenu(e){e.preventDefault(),this.onContextMenuHandler(e)}handleTouchStart(e){this.longPressTimer=window.setTimeout((()=>{e.preventDefault();const t=e.touches[0],n=new MouseEvent("contextmenu",{bubbles:!0,cancelable:!0,view:window,clientX:t.clientX,clientY:t.clientY});this.toggle&&this.toggle.dispatchEvent(n)}),400)}handleTouchEnd(e){this.longPressTimer&&(clearTimeout(this.longPressTimer),this.longPressTimer=null)}closerClick(){this.close()}init(){if(this.createCollection(window.$hsDropdownCollection,this),this.toggle.disabled)return!1;this.toggle&&this.buildToggle(),this.menu&&this.buildMenu(),this.closers&&this.buildClosers(),(0,i.isIOS)()||(0,i.isIpadOS)()||(this.onElementMouseEnterListener=()=>this.elementMouseEnter(),this.onElementMouseLeaveListener=()=>this.elementMouseLeave(),this.el.addEventListener("mouseenter",this.onElementMouseEnterListener),this.el.addEventListener("mouseleave",this.onElementMouseLeaveListener))}resizeHandler(){this.eventMode=(0,i.getClassProperty)(this.el,"--trigger","click"),this.closeMode=(0,i.getClassProperty)(this.el,"--auto-close","true")}buildToggle(){var e;(null===(e=null==this?void 0:this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.el.classList.contains("open")?this.toggle.ariaExpanded="true":this.toggle.ariaExpanded="false"),"contextmenu"===this.eventMode?(this.onToggleContextMenuListener=e=>this.toggleContextMenu(e),this.onTouchStartListener=this.handleTouchStart.bind(this),this.onTouchEndListener=this.handleTouchEnd.bind(this),this.toggle.addEventListener("contextmenu",this.onToggleContextMenuListener),this.toggle.addEventListener("touchstart",this.onTouchStartListener,{passive:!1}),this.toggle.addEventListener("touchend",this.onTouchEndListener),this.toggle.addEventListener("touchmove",this.onTouchEndListener)):(this.onToggleClickListener=e=>this.toggleClick(e),this.toggle.addEventListener("click",this.onToggleClickListener))}buildMenu(){this.menu.role=this.menu.getAttribute("role")||"menu";const e=this.menu.querySelectorAll('[role="menuitemcheckbox"]'),t=this.menu.querySelectorAll('[role="menuitemradio"]');e.forEach((e=>e.addEventListener("click",(()=>this.selectCheckbox(e))))),t.forEach((e=>e.addEventListener("click",(()=>this.selectRadio(e)))))}buildClosers(){this.closers.forEach((e=>{this.onCloserClickListener.push({el:e,fn:()=>this.closerClick()}),e.addEventListener("click",this.onCloserClickListener.find((t=>t.el===e)).fn)}))}getScrollbarSize(){let e=document.createElement("div");e.style.overflow="scroll",e.style.width="100px",e.style.height="100px",document.body.appendChild(e);let t=e.offsetWidth-e.clientWidth;return document.body.removeChild(e),t}onContextMenuHandler(e){const t={getBoundingClientRect:(()=>new DOMRect,()=>new DOMRect(e.clientX,e.clientY,0,0))};c.closeCurrentlyOpened(),this.el.classList.contains("open")&&!this.menu.classList.contains("hidden")?(this.close(),document.body.style.overflow="",document.body.style.paddingRight=""):(document.body.style.overflow="hidden",document.body.style.paddingRight=`${this.getScrollbarSize()}px`,this.open(t))}onClickHandler(e){this.el.classList.contains("open")&&!this.menu.classList.contains("hidden")?this.close():this.open()}onMouseEnterHandler(){if("hover"!==this.eventMode)return!1;(!this.el._floatingUI||this.el._floatingUI&&!this.el.classList.contains("open"))&&this.forceClearState(),!this.el.classList.contains("open")&&this.menu.classList.contains("hidden")&&this.open()}onMouseLeaveHandler(){if("hover"!==this.eventMode)return!1;this.el.classList.contains("open")&&!this.menu.classList.contains("hidden")&&this.close()}destroyFloatingUI(){const e=(window.getComputedStyle(this.el).getPropertyValue("--scope")||"").trim();this.menu.classList.remove("block"),this.menu.classList.add("hidden"),this.menu.style.inset=null,this.menu.style.position=null,this.el&&this.el._floatingUI&&(this.el._floatingUI.destroy(),this.el._floatingUI=null),"window"===e&&this.el.appendChild(this.menu),this.animationInProcess=!1}focusElement(){const e=this.menu.querySelector("[autofocus]");if(!e)return!1;e.focus()}setupFloatingUI(e){const t=e||this.el,n=window.getComputedStyle(this.el),o=(n.getPropertyValue("--placement")||"").trim(),i=(n.getPropertyValue("--flip")||"true").trim(),r=(n.getPropertyValue("--strategy")||"fixed").trim(),c=(n.getPropertyValue("--offset")||"10").trim(),a=(n.getPropertyValue("--gpu-acceleration")||"true").trim(),u=(window.getComputedStyle(this.el).getPropertyValue("--adaptive")||"adaptive").replace(" ",""),d=r,h=parseInt(c,10),f=l.POSITIONS[o]||"bottom-start",m=[..."true"===i?[(0,s.flip)()]:[],(0,s.offset)(h)],p={placement:f,strategy:d,middleware:m},g=e=>{const t=this.menu.getBoundingClientRect(),n=window.innerWidth-(window.innerWidth-document.documentElement.clientWidth);return e+t.width>n&&(e=n-t.width),e<0&&(e=0),e},w=()=>{(0,s.computePosition)(t,this.menu,p).then((({x:e,y:t,placement:n})=>{const o=g(e);"absolute"===d&&"none"===u?Object.assign(this.menu.style,{position:d,margin:"0"}):"absolute"===d?Object.assign(this.menu.style,{position:d,transform:`translate3d(${e}px, ${t}px, 0px)`,margin:"0"}):"true"===a?Object.assign(this.menu.style,{position:d,left:"",top:"",inset:"0px auto auto 0px",margin:"0",transform:`translate3d(${"adaptive"===u?o:0}px, ${t}px, 0)`}):Object.assign(this.menu.style,{position:d,left:`${e}px`,top:`${t}px`,transform:""}),this.menu.setAttribute("data-placement",n)}))};w();return{update:w,destroy:(0,s.autoUpdate)(t,this.menu,w)}}selectCheckbox(e){e.ariaChecked="true"===e.ariaChecked?"false":"true"}selectRadio(e){if("true"===e.ariaChecked)return!1;const t=e.closest(".group").querySelectorAll('[role="menuitemradio"]');Array.from(t).filter((t=>t!==e)).forEach((e=>{e.ariaChecked="false"})),e.ariaChecked="true"}calculatePopperPosition(e){const t=this.setupFloatingUI(e),n=this.menu.getAttribute("data-placement");return t.update(),t.destroy(),n}open(e){if(this.el.classList.contains("open")||this.animationInProcess)return!1;this.animationInProcess=!0,this.menu.style.cssText="";const t=e||this.el,n=window.getComputedStyle(this.el),o=(n.getPropertyValue("--scope")||"").trim(),s=(n.getPropertyValue("--strategy")||"fixed").trim();"window"===o&&document.body.appendChild(this.menu),"static"!==s&&(this.el._floatingUI=this.setupFloatingUI(t)),this.menu.style.margin=null,this.menu.classList.remove("hidden"),this.menu.classList.add("block"),setTimeout((()=>{var e;(null===(e=null==this?void 0:this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.toggle.ariaExpanded="true"),this.el.classList.add("open"),"window"===o&&this.menu.classList.add("open"),this.animationInProcess=!1,this.hasAutofocus&&this.focusElement(),this.fireEvent("open",this.el),(0,i.dispatch)("open.hs.dropdown",this.el,this.el)}))}close(e=!0){if(this.animationInProcess||!this.el.classList.contains("open"))return!1;const t=(window.getComputedStyle(this.el).getPropertyValue("--scope")||"").trim();if(this.animationInProcess=!0,"window"===t&&this.menu.classList.remove("open"),e){const e=this.el.querySelector("[data-hs-dropdown-transition]")||this.menu;(0,i.afterTransition)(e,(()=>this.destroyFloatingUI()))}else this.destroyFloatingUI();(()=>{var e;this.menu.style.margin=null,(null===(e=null==this?void 0:this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.toggle.ariaExpanded="false"),this.el.classList.remove("open"),this.fireEvent("close",this.el),(0,i.dispatch)("close.hs.dropdown",this.el,this.el)})()}forceClearState(){this.destroyFloatingUI(),this.menu.style.margin=null,this.el.classList.remove("open"),this.menu.classList.add("hidden")}destroy(){(0,i.isIOS)()||(0,i.isIpadOS)()||(this.el.removeEventListener("mouseenter",this.onElementMouseEnterListener),this.el.removeEventListener("mouseleave",(()=>this.onElementMouseLeaveListener)),this.onElementMouseEnterListener=null,this.onElementMouseLeaveListener=null),"contextmenu"===this.eventMode?(this.toggle&&(this.toggle.removeEventListener("contextmenu",this.onToggleContextMenuListener),this.toggle.removeEventListener("touchstart",this.onTouchStartListener),this.toggle.removeEventListener("touchend",this.onTouchEndListener),this.toggle.removeEventListener("touchmove",this.onTouchEndListener)),this.onToggleContextMenuListener=null,this.onTouchStartListener=null,this.onTouchEndListener=null):(this.toggle&&this.toggle.removeEventListener("click",this.onToggleClickListener),this.onToggleClickListener=null),this.closers.length&&(this.closers.forEach((e=>{e.removeEventListener("click",this.onCloserClickListener.find((t=>t.el===e)).fn)})),this.onCloserClickListener=null),this.el.classList.remove("open"),this.destroyFloatingUI(),window.$hsDropdownCollection=window.$hsDropdownCollection.filter((({element:e})=>e.el!==this.el))}static findInCollection(e){return window.$hsDropdownCollection.find((t=>e instanceof c?t.element.el===e.el:"string"==typeof e?t.element.el===document.querySelector(e):t.element.el===e))||null}static getInstance(e,t){const n=window.$hsDropdownCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return n?t?n:n.element:null}static autoInit(){if(!window.$hsDropdownCollection){window.$hsDropdownCollection=[],document.addEventListener("keydown",(e=>c.accessibility(e))),window.addEventListener("click",(e=>{const t=e.target;c.closeCurrentlyOpened(t)}));let e=window.innerWidth;window.addEventListener("resize",(()=>{window.innerWidth!==e&&(e=innerWidth,c.closeCurrentlyOpened(null,!1))}))}window.$hsDropdownCollection&&(window.$hsDropdownCollection=window.$hsDropdownCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll(".hs-dropdown:not(.--prevent-on-load-init)").forEach((e=>{window.$hsDropdownCollection.find((t=>{var n;return(null===(n=null==t?void 0:t.element)||void 0===n?void 0:n.el)===e}))||new c(e)}))}static open(e){const t=c.findInCollection(e);t&&t.element.menu.classList.contains("hidden")&&t.element.open()}static close(e){const t=c.findInCollection(e);t&&!t.element.menu.classList.contains("hidden")&&t.element.close()}static accessibility(e){this.history=i.menuSearchHistory;const t=window.$hsDropdownCollection.find((e=>e.element.el.classList.contains("open")));if(t&&(l.DROPDOWN_ACCESSIBILITY_KEY_SET.includes(e.code)||4===e.code.length&&e.code[e.code.length-1].match(/^[A-Z]*$/))&&!e.metaKey&&!t.element.menu.querySelector("input:focus")&&!t.element.menu.querySelector("textarea:focus"))switch(e.code){case"Escape":t.element.menu.querySelector(".hs-select.active")||(e.preventDefault(),this.onEscape(e));break;case"Enter":t.element.menu.querySelector(".hs-select button:focus")||t.element.menu.querySelector(".hs-collapse-toggle:focus")||this.onEnter(e);break;case"ArrowUp":e.preventDefault(),e.stopImmediatePropagation(),this.onArrow();break;case"ArrowDown":e.preventDefault(),e.stopImmediatePropagation(),this.onArrow(!1);break;case"ArrowRight":e.preventDefault(),e.stopImmediatePropagation(),this.onArrowX(e,"right");break;case"ArrowLeft":e.preventDefault(),e.stopImmediatePropagation(),this.onArrowX(e,"left");break;case"Home":e.preventDefault(),e.stopImmediatePropagation(),this.onStartEnd();break;case"End":e.preventDefault(),e.stopImmediatePropagation(),this.onStartEnd(!1);break;default:e.preventDefault(),this.onFirstLetter(e.key)}}static onEscape(e){const t=e.target.closest(".hs-dropdown.open");if(window.$hsDropdownCollection.find((e=>e.element.el===t))){const e=window.$hsDropdownCollection.find((e=>e.element.el===t));e&&(e.element.close(),e.element.toggle.focus())}else this.closeCurrentlyOpened()}static onEnter(e){var t;const n=e.target,{element:o}=null!==(t=window.$hsDropdownCollection.find((e=>e.element.el===n.closest(".hs-dropdown"))))&&void 0!==t?t:null;if(o&&n.classList.contains("hs-dropdown-toggle"))e.preventDefault(),o.open();else if(o&&"menuitemcheckbox"===n.getAttribute("role"))o.selectCheckbox(n),o.close();else{if(!o||"menuitemradio"!==n.getAttribute("role"))return!1;o.selectRadio(n),o.close()}}static onArrow(e=!0){const t=window.$hsDropdownCollection.find((e=>e.element.el.classList.contains("open")));if(t){const n=t.element.menu;if(!n)return!1;const o=e?Array.from(n.querySelectorAll('a:not([hidden]), :scope button:not([hidden]), [role="button"]:not([hidden]), [role^="menuitem"]:not([hidden])')).reverse():Array.from(n.querySelectorAll('a:not([hidden]), :scope button:not([hidden]), [role="button"]:not([hidden]), [role^="menuitem"]:not([hidden])')),i=Array.from(o).filter((e=>{const t=e;return null===t.closest("[hidden]")&&null!==t.offsetParent})).filter((e=>!e.classList.contains("disabled"))),s=n.querySelector('a:focus, button:focus, [role="button"]:focus, [role^="menuitem"]:focus');let r=i.findIndex((e=>e===s));r+1<i.length&&r++,i[r].focus()}}static onArrowX(e,t){var n,o;const i=e.target,s=i.closest(".hs-dropdown.open"),r=!!s&&!(null==s?void 0:s.parentElement.closest(".hs-dropdown")),l=null!==(n=c.getInstance(i.closest(".hs-dropdown"),!0))&&void 0!==n?n:null,a=l.element.menu.querySelector('a, button, [role="button"], [role^="menuitem"]');if(r&&!i.classList.contains("hs-dropdown-toggle"))return!1;const u=null!==(o=c.getInstance(i.closest(".hs-dropdown.open"),!0))&&void 0!==o?o:null;if(l.element.el.classList.contains("open")&&l.element.el._floatingUI.state.placement.includes(t))return a.focus(),!1;const d=l.element.calculatePopperPosition();if(r&&!d.includes(t))return!1;d.includes(t)&&i.classList.contains("hs-dropdown-toggle")?(l.element.open(),a.focus()):(u.element.close(!1),u.element.toggle.focus())}static onStartEnd(e=!0){const t=window.$hsDropdownCollection.find((e=>e.element.el.classList.contains("open")));if(t){const n=t.element.menu;if(!n)return!1;const o=(e?Array.from(n.querySelectorAll('a, button, [role="button"], [role^="menuitem"]')):Array.from(n.querySelectorAll('a, button, [role="button"], [role^="menuitem"]')).reverse()).filter((e=>!e.classList.contains("disabled")));o.length&&o[0].focus()}}static onFirstLetter(e){const t=window.$hsDropdownCollection.find((e=>e.element.el.classList.contains("open")));if(t){const n=t.element.menu;if(!n)return!1;const o=Array.from(n.querySelectorAll('a, [role="button"], [role^="menuitem"]')),i=()=>o.findIndex(((t,n)=>t.innerText.toLowerCase().charAt(0)===e.toLowerCase()&&this.history.existsInHistory(n)));let s=i();-1===s&&(this.history.clearHistory(),s=i()),-1!==s&&(o[s].focus(),this.history.addHistory(s))}}static closeCurrentlyOpened(e=null,t=!0){const n=e&&e.closest(".hs-dropdown")&&e.closest(".hs-dropdown").parentElement.closest(".hs-dropdown")?e.closest(".hs-dropdown").parentElement.closest(".hs-dropdown"):null;let o=n?window.$hsDropdownCollection.filter((e=>e.element.el.classList.contains("open")&&e.element.menu.closest(".hs-dropdown").parentElement.closest(".hs-dropdown")===n)):window.$hsDropdownCollection.filter((e=>e.element.el.classList.contains("open")));e&&e.closest(".hs-dropdown")&&"inside"===(0,i.getClassPropertyAlt)(e.closest(".hs-dropdown"),"--auto-close")&&(o=o.filter((t=>t.element.el!==e.closest(".hs-dropdown")))),o&&o.forEach((e=>{if("false"===e.element.closeMode||"outside"===e.element.closeMode)return!1;e.element.close(t)})),o&&o.forEach((e=>{if("contextmenu"!==(0,i.getClassPropertyAlt)(e.element.el,"--trigger"))return!1;document.body.style.overflow="",document.body.style.paddingRight=""}))}static on(e,t,n){const o=c.findInCollection(t);o&&(o.element.events[e]=n)}}window.addEventListener("load",(()=>{c.autoInit()})),window.addEventListener("resize",(()=>{window.$hsDropdownCollection||(window.$hsDropdownCollection=[]),window.$hsDropdownCollection.forEach((e=>e.element.resizeHandler()))})),"undefined"!=typeof window&&(window.HSDropdown=c),t.default=c},949:(e,t,n)=>{n.r(t),n.d(t,{arrow:()=>be,autoPlacement:()=>ge,autoUpdate:()=>fe,computePosition:()=>Ce,detectOverflow:()=>me,flip:()=>ye,getOverflowAncestors:()=>X,hide:()=>xe,inline:()=>Ee,limitShift:()=>Le,offset:()=>pe,platform:()=>de,shift:()=>we,size:()=>ve});const o=["top","right","bottom","left"],i=["start","end"],s=o.reduce(((e,t)=>e.concat(t,t+"-"+i[0],t+"-"+i[1])),[]),r=Math.min,l=Math.max,c=Math.round,a=Math.floor,u=e=>({x:e,y:e}),d={left:"right",right:"left",bottom:"top",top:"bottom"},h={start:"end",end:"start"};function f(e,t,n){return l(e,r(t,n))}function m(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function g(e){return e.split("-")[1]}function w(e){return"x"===e?"y":"x"}function y(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(p(e))?"y":"x"}function x(e){return w(v(e))}function b(e,t,n){void 0===n&&(n=!1);const o=g(e),i=x(e),s=y(i);let r="x"===i?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return t.reference[s]>t.floating[s]&&(r=L(r)),[r,L(r)]}function E(e){return e.replace(/start|end/g,(e=>h[e]))}function L(e){return e.replace(/left|right|bottom|top/g,(e=>d[e]))}function C(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function S(e){const{x:t,y:n,width:o,height:i}=e;return{width:o,height:i,top:n,left:t,right:t+o,bottom:n+i,x:t,y:n}}function T(e,t,n){let{reference:o,floating:i}=e;const s=v(t),r=x(t),l=y(r),c=p(t),a="y"===s,u=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,h=o[l]/2-i[l]/2;let f;switch(c){case"top":f={x:u,y:o.y-i.height};break;case"bottom":f={x:u,y:o.y+o.height};break;case"right":f={x:o.x+o.width,y:d};break;case"left":f={x:o.x-i.width,y:d};break;default:f={x:o.x,y:o.y}}switch(g(t)){case"start":f[r]-=h*(n&&a?-1:1);break;case"end":f[r]+=h*(n&&a?-1:1)}return f}async function A(e,t){var n;void 0===t&&(t={});const{x:o,y:i,platform:s,rects:r,elements:l,strategy:c}=e,{boundary:a="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:h=!1,padding:f=0}=m(t,e),p=C(f),g=l[h?"floating"===d?"reference":"floating":d],w=S(await s.getClippingRect({element:null==(n=await(null==s.isElement?void 0:s.isElement(g)))||n?g:g.contextElement||await(null==s.getDocumentElement?void 0:s.getDocumentElement(l.floating)),boundary:a,rootBoundary:u,strategy:c})),y="floating"===d?{x:o,y:i,width:r.floating.width,height:r.floating.height}:r.reference,v=await(null==s.getOffsetParent?void 0:s.getOffsetParent(l.floating)),x=await(null==s.isElement?void 0:s.isElement(v))&&await(null==s.getScale?void 0:s.getScale(v))||{x:1,y:1},b=S(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:v,strategy:c}):y);return{top:(w.top-b.top+p.top)/x.y,bottom:(b.bottom-w.bottom+p.bottom)/x.y,left:(w.left-b.left+p.left)/x.x,right:(b.right-w.right+p.right)/x.x}}function I(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return o.some((t=>e[t]>=0))}function O(e){const t=r(...e.map((e=>e.left))),n=r(...e.map((e=>e.top)));return{x:t,y:n,width:l(...e.map((e=>e.right)))-t,height:l(...e.map((e=>e.bottom)))-n}}function D(){return"undefined"!=typeof window}function R(e){return _(e)?(e.nodeName||"").toLowerCase():"#document"}function M(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(_(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function _(e){return!!D()&&(e instanceof Node||e instanceof M(e).Node)}function H(e){return!!D()&&(e instanceof Element||e instanceof M(e).Element)}function B(e){return!!D()&&(e instanceof HTMLElement||e instanceof M(e).HTMLElement)}function $(e){return!(!D()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof M(e).ShadowRoot)}function F(e){const{overflow:t,overflowX:n,overflowY:o,display:i}=N(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(i)}function Y(e){return["table","td","th"].includes(R(e))}function q(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(e){return!1}}))}function V(e){const t=W(),n=H(e)?N(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function W(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function U(e){return["html","body","#document"].includes(R(e))}function N(e){return M(e).getComputedStyle(e)}function j(e){return H(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===R(e))return e;const t=e.assignedSlot||e.parentNode||$(e)&&e.host||k(e);return $(t)?t.host:t}function K(e){const t=z(e);return U(t)?e.ownerDocument?e.ownerDocument.body:e.body:B(t)&&F(t)?t:K(t)}function X(e,t,n){var o;void 0===t&&(t=[]),void 0===n&&(n=!0);const i=K(e),s=i===(null==(o=e.ownerDocument)?void 0:o.body),r=M(i);if(s){const e=Z(r);return t.concat(r,r.visualViewport||[],F(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Z(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function J(e){const t=N(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const i=B(e),s=i?e.offsetWidth:n,r=i?e.offsetHeight:o,l=c(n)!==s||c(o)!==r;return l&&(n=s,o=r),{width:n,height:o,$:l}}function G(e){return H(e)?e:e.contextElement}function Q(e){const t=G(e);if(!B(t))return u(1);const n=t.getBoundingClientRect(),{width:o,height:i,$:s}=J(t);let r=(s?c(n.width):n.width)/o,l=(s?c(n.height):n.height)/i;return r&&Number.isFinite(r)||(r=1),l&&Number.isFinite(l)||(l=1),{x:r,y:l}}const ee=u(0);function te(e){const t=M(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function ne(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n=!1);const i=e.getBoundingClientRect(),s=G(e);let r=u(1);t&&(o?H(o)&&(r=Q(o)):r=Q(e));const l=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==M(e))&&t}(s,n,o)?te(s):u(0);let c=(i.left+l.x)/r.x,a=(i.top+l.y)/r.y,d=i.width/r.x,h=i.height/r.y;if(s){const e=M(s),t=o&&H(o)?M(o):o;let n=e,i=Z(n);for(;i&&o&&t!==n;){const e=Q(i),t=i.getBoundingClientRect(),o=N(i),s=t.left+(i.clientLeft+parseFloat(o.paddingLeft))*e.x,r=t.top+(i.clientTop+parseFloat(o.paddingTop))*e.y;c*=e.x,a*=e.y,d*=e.x,h*=e.y,c+=s,a+=r,n=M(i),i=Z(n)}}return S({width:d,height:h,x:c,y:a})}function oe(e,t){const n=j(e).scrollLeft;return t?t.left+n:ne(k(e)).left+n}function ie(e,t,n){void 0===n&&(n=!1);const o=e.getBoundingClientRect();return{x:o.left+t.scrollLeft-(n?0:oe(e,o)),y:o.top+t.scrollTop}}function se(e,t,n){let o;if("viewport"===t)o=function(e,t){const n=M(e),o=k(e),i=n.visualViewport;let s=o.clientWidth,r=o.clientHeight,l=0,c=0;if(i){s=i.width,r=i.height;const e=W();(!e||e&&"fixed"===t)&&(l=i.offsetLeft,c=i.offsetTop)}return{width:s,height:r,x:l,y:c}}(e,n);else if("document"===t)o=function(e){const t=k(e),n=j(e),o=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=l(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let r=-n.scrollLeft+oe(e);const c=-n.scrollTop;return"rtl"===N(o).direction&&(r+=l(t.clientWidth,o.clientWidth)-i),{width:i,height:s,x:r,y:c}}(k(e));else if(H(t))o=function(e,t){const n=ne(e,!0,"fixed"===t),o=n.top+e.clientTop,i=n.left+e.clientLeft,s=B(e)?Q(e):u(1);return{width:e.clientWidth*s.x,height:e.clientHeight*s.y,x:i*s.x,y:o*s.y}}(t,n);else{const n=te(e);o={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return S(o)}function re(e,t){const n=z(e);return!(n===t||!H(n)||U(n))&&("fixed"===N(n).position||re(n,t))}function le(e,t,n){const o=B(t),i=k(t),s="fixed"===n,r=ne(e,!0,s,t);let l={scrollLeft:0,scrollTop:0};const c=u(0);if(o||!o&&!s)if(("body"!==R(t)||F(i))&&(l=j(t)),o){const e=ne(t,!0,s,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else i&&(c.x=oe(i));const a=!i||o||s?u(0):ie(i,l);return{x:r.left+l.scrollLeft-c.x-a.x,y:r.top+l.scrollTop-c.y-a.y,width:r.width,height:r.height}}function ce(e){return"static"===N(e).position}function ae(e,t){if(!B(e)||"fixed"===N(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function ue(e,t){const n=M(e);if(q(e))return n;if(!B(e)){let t=z(e);for(;t&&!U(t);){if(H(t)&&!ce(t))return t;t=z(t)}return n}let o=ae(e,t);for(;o&&Y(o)&&ce(o);)o=ae(o,t);return o&&U(o)&&ce(o)&&!V(o)?n:o||function(e){let t=z(e);for(;B(t)&&!U(t);){if(V(t))return t;if(q(t))return null;t=z(t)}return null}(e)||n}const de={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:o,strategy:i}=e;const s="fixed"===i,r=k(o),l=!!t&&q(t.floating);if(o===r||l&&s)return n;let c={scrollLeft:0,scrollTop:0},a=u(1);const d=u(0),h=B(o);if((h||!h&&!s)&&(("body"!==R(o)||F(r))&&(c=j(o)),B(o))){const e=ne(o);a=Q(o),d.x=e.x+o.clientLeft,d.y=e.y+o.clientTop}const f=!r||h||s?u(0):ie(r,c,!0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-c.scrollLeft*a.x+d.x+f.x,y:n.y*a.y-c.scrollTop*a.y+d.y+f.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:i}=e;const s=[..."clippingAncestors"===n?q(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let o=X(e,[],!1).filter((e=>H(e)&&"body"!==R(e))),i=null;const s="fixed"===N(e).position;let r=s?z(e):e;for(;H(r)&&!U(r);){const t=N(r),n=V(r);n||"fixed"!==t.position||(i=null),(s?!n&&!i:!n&&"static"===t.position&&i&&["absolute","fixed"].includes(i.position)||F(r)&&!n&&re(e,r))?o=o.filter((e=>e!==r)):i=t,r=z(r)}return t.set(e,o),o}(t,this._c):[].concat(n),o],c=s[0],a=s.reduce(((e,n)=>{const o=se(t,n,i);return e.top=l(o.top,e.top),e.right=r(o.right,e.right),e.bottom=r(o.bottom,e.bottom),e.left=l(o.left,e.left),e}),se(t,c,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ue,getElementRects:async function(e){const t=this.getOffsetParent||ue,n=this.getDimensions,o=await n(e.floating);return{reference:le(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=J(e);return{width:t,height:n}},getScale:Q,isElement:H,isRTL:function(e){return"rtl"===N(e).direction}};function he(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function fe(e,t,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=o,h=G(e),f=i||s?[...h?X(h):[],...X(t)]:[];f.forEach((e=>{i&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)}));const m=h&&u?function(e,t){let n,o=null;const i=k(e);function s(){var e;clearTimeout(n),null==(e=o)||e.disconnect(),o=null}return function c(u,d){void 0===u&&(u=!1),void 0===d&&(d=1),s();const h=e.getBoundingClientRect(),{left:f,top:m,width:p,height:g}=h;if(u||t(),!p||!g)return;const w={rootMargin:-a(m)+"px "+-a(i.clientWidth-(f+p))+"px "+-a(i.clientHeight-(m+g))+"px "+-a(f)+"px",threshold:l(0,r(1,d))||1};let y=!0;function v(t){const o=t[0].intersectionRatio;if(o!==d){if(!y)return c();o?c(!1,o):n=setTimeout((()=>{c(!1,1e-7)}),1e3)}1!==o||he(h,e.getBoundingClientRect())||c(),y=!1}try{o=new IntersectionObserver(v,{...w,root:i.ownerDocument})}catch(e){o=new IntersectionObserver(v,w)}o.observe(e)}(!0),s}(h,n):null;let p,g=-1,w=null;c&&(w=new ResizeObserver((e=>{let[o]=e;o&&o.target===h&&w&&(w.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame((()=>{var e;null==(e=w)||e.observe(t)}))),n()})),h&&!d&&w.observe(h),w.observe(t));let y=d?ne(e):null;return d&&function t(){const o=ne(e);y&&!he(y,o)&&n();y=o,p=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach((e=>{i&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)})),null==m||m(),null==(e=w)||e.disconnect(),w=null,d&&cancelAnimationFrame(p)}}const me=A,pe=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:i,y:s,placement:r,middlewareData:l}=t,c=await async function(e,t){const{placement:n,platform:o,elements:i}=e,s=await(null==o.isRTL?void 0:o.isRTL(i.floating)),r=p(n),l=g(n),c="y"===v(n),a=["left","top"].includes(r)?-1:1,u=s&&c?-1:1,d=m(t,e);let{mainAxis:h,crossAxis:f,alignmentAxis:w}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof w&&(f="end"===l?-1*w:w),c?{x:f*u,y:h*a}:{x:h*a,y:f*u}}(t,e);return r===(null==(n=l.offset)?void 0:n.placement)&&null!=(o=l.arrow)&&o.alignmentOffset?{}:{x:i+c.x,y:s+c.y,data:{...c,placement:r}}}}},ge=function(e){return void 0===e&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,o,i;const{rects:r,middlewareData:l,placement:c,platform:a,elements:u}=t,{crossAxis:d=!1,alignment:h,allowedPlacements:f=s,autoAlignment:w=!0,...y}=m(e,t),v=void 0!==h||f===s?function(e,t,n){return(e?[...n.filter((t=>g(t)===e)),...n.filter((t=>g(t)!==e))]:n.filter((e=>p(e)===e))).filter((n=>!e||g(n)===e||!!t&&E(n)!==n))}(h||null,w,f):f,x=await A(t,y),L=(null==(n=l.autoPlacement)?void 0:n.index)||0,C=v[L];if(null==C)return{};const S=b(C,r,await(null==a.isRTL?void 0:a.isRTL(u.floating)));if(c!==C)return{reset:{placement:v[0]}};const T=[x[p(C)],x[S[0]],x[S[1]]],I=[...(null==(o=l.autoPlacement)?void 0:o.overflows)||[],{placement:C,overflows:T}],P=v[L+1];if(P)return{data:{index:L+1,overflows:I},reset:{placement:P}};const O=I.map((e=>{const t=g(e.placement);return[e.placement,t&&d?e.overflows.slice(0,2).reduce(((e,t)=>e+t),0):e.overflows[0],e.overflows]})).sort(((e,t)=>e[1]-t[1])),D=(null==(i=O.filter((e=>e[2].slice(0,g(e[0])?2:3).every((e=>e<=0))))[0])?void 0:i[0])||O[0][0];return D!==c?{data:{index:L+1,overflows:I},reset:{placement:D}}:{}}}},we=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:i}=t,{mainAxis:s=!0,crossAxis:r=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=m(e,t),a={x:n,y:o},u=await A(t,c),d=v(p(i)),h=w(d);let g=a[h],y=a[d];if(s){const e="y"===h?"bottom":"right";g=f(g+u["y"===h?"top":"left"],g,g-u[e])}if(r){const e="y"===d?"bottom":"right";y=f(y+u["y"===d?"top":"left"],y,y-u[e])}const x=l.fn({...t,[h]:g,[d]:y});return{...x,data:{x:x.x-n,y:x.y-o,enabled:{[h]:s,[d]:r}}}}}},ye=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:i,middlewareData:s,rects:r,initialPlacement:l,platform:c,elements:a}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:h,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:y=!0,...x}=m(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};const C=p(i),S=v(l),T=p(l)===l,I=await(null==c.isRTL?void 0:c.isRTL(a.floating)),P=h||(T||!y?[L(l)]:function(e){const t=L(e);return[E(e),t,E(t)]}(l)),O="none"!==w;!h&&O&&P.push(...function(e,t,n,o){const i=g(e);let s=function(e,t,n){const o=["left","right"],i=["right","left"],s=["top","bottom"],r=["bottom","top"];switch(e){case"top":case"bottom":return n?t?i:o:t?o:i;case"left":case"right":return t?s:r;default:return[]}}(p(e),"start"===n,o);return i&&(s=s.map((e=>e+"-"+i)),t&&(s=s.concat(s.map(E)))),s}(l,y,w,I));const D=[l,...P],R=await A(t,x),M=[];let k=(null==(o=s.flip)?void 0:o.overflows)||[];if(u&&M.push(R[C]),d){const e=b(i,r,I);M.push(R[e[0]],R[e[1]])}if(k=[...k,{placement:i,overflows:M}],!M.every((e=>e<=0))){var _,H;const e=((null==(_=s.flip)?void 0:_.index)||0)+1,t=D[e];if(t)return{data:{index:e,overflows:k},reset:{placement:t}};let n=null==(H=k.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:H.placement;if(!n)switch(f){case"bestFit":{var B;const e=null==(B=k.filter((e=>{if(O){const t=v(e.placement);return t===S||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:B[0];e&&(n=e);break}case"initialPlacement":n=l}if(i!==n)return{reset:{placement:n}}}return{}}}},ve=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:i,rects:s,platform:c,elements:a}=t,{apply:u=()=>{},...d}=m(e,t),h=await A(t,d),f=p(i),w=g(i),y="y"===v(i),{width:x,height:b}=s.floating;let E,L;"top"===f||"bottom"===f?(E=f,L=w===(await(null==c.isRTL?void 0:c.isRTL(a.floating))?"start":"end")?"left":"right"):(L=f,E="end"===w?"top":"bottom");const C=b-h.top-h.bottom,S=x-h.left-h.right,T=r(b-h[E],C),I=r(x-h[L],S),P=!t.middlewareData.shift;let O=T,D=I;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=S),null!=(o=t.middlewareData.shift)&&o.enabled.y&&(O=C),P&&!w){const e=l(h.left,0),t=l(h.right,0),n=l(h.top,0),o=l(h.bottom,0);y?D=x-2*(0!==e||0!==t?e+t:l(h.left,h.right)):O=b-2*(0!==n||0!==o?n+o:l(h.top,h.bottom))}await u({...t,availableWidth:D,availableHeight:O});const R=await c.getDimensions(a.floating);return x!==R.width||b!==R.height?{reset:{rects:!0}}:{}}}},xe=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...i}=m(e,t);switch(o){case"referenceHidden":{const e=I(await A(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{const e=I(await A(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}},be=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:i,rects:s,platform:l,elements:c,middlewareData:a}=t,{element:u,padding:d=0}=m(e,t)||{};if(null==u)return{};const h=C(d),p={x:n,y:o},w=x(i),v=y(w),b=await l.getDimensions(u),E="y"===w,L=E?"top":"left",S=E?"bottom":"right",T=E?"clientHeight":"clientWidth",A=s.reference[v]+s.reference[w]-p[w]-s.floating[v],I=p[w]-s.reference[w],P=await(null==l.getOffsetParent?void 0:l.getOffsetParent(u));let O=P?P[T]:0;O&&await(null==l.isElement?void 0:l.isElement(P))||(O=c.floating[T]||s.floating[v]);const D=A/2-I/2,R=O/2-b[v]/2-1,M=r(h[L],R),k=r(h[S],R),_=M,H=O-b[v]-k,B=O/2-b[v]/2+D,$=f(_,B,H),F=!a.arrow&&null!=g(i)&&B!==$&&s.reference[v]/2-(B<_?M:k)-b[v]/2<0,Y=F?B<_?B-_:B-H:0;return{[w]:p[w]+Y,data:{[w]:$,centerOffset:B-$-Y,...F&&{alignmentOffset:Y}},reset:F}}}),Ee=function(e){return void 0===e&&(e={}),{name:"inline",options:e,async fn(t){const{placement:n,elements:o,rects:i,platform:s,strategy:c}=t,{padding:a=2,x:u,y:d}=m(e,t),h=Array.from(await(null==s.getClientRects?void 0:s.getClientRects(o.reference))||[]),f=function(e){const t=e.slice().sort(((e,t)=>e.y-t.y)),n=[];let o=null;for(let e=0;e<t.length;e++){const i=t[e];!o||i.y-o.y>o.height/2?n.push([i]):n[n.length-1].push(i),o=i}return n.map((e=>S(O(e))))}(h),g=S(O(h)),w=C(a);const y=await s.getElementRects({reference:{getBoundingClientRect:function(){if(2===f.length&&f[0].left>f[1].right&&null!=u&&null!=d)return f.find((e=>u>e.left-w.left&&u<e.right+w.right&&d>e.top-w.top&&d<e.bottom+w.bottom))||g;if(f.length>=2){if("y"===v(n)){const e=f[0],t=f[f.length-1],o="top"===p(n),i=e.top,s=t.bottom,r=o?e.left:t.left,l=o?e.right:t.right;return{top:i,bottom:s,left:r,right:l,width:l-r,height:s-i,x:r,y:i}}const e="left"===p(n),t=l(...f.map((e=>e.right))),o=r(...f.map((e=>e.left))),i=f.filter((n=>e?n.left===o:n.right===t)),s=i[0].top,c=i[i.length-1].bottom;return{top:s,bottom:c,left:o,right:t,width:t-o,height:c-s,x:o,y:s}}return g}},floating:o.floating,strategy:c});return i.reference.x!==y.reference.x||i.reference.y!==y.reference.y||i.reference.width!==y.reference.width||i.reference.height!==y.reference.height?{reset:{rects:y}}:{}}}},Le=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:i,rects:s,middlewareData:r}=t,{offset:l=0,mainAxis:c=!0,crossAxis:a=!0}=m(e,t),u={x:n,y:o},d=v(i),h=w(d);let f=u[h],g=u[d];const y=m(l,t),x="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(c){const e="y"===h?"height":"width",t=s.reference[h]-s.floating[e]+x.mainAxis,n=s.reference[h]+s.reference[e]-x.mainAxis;f<t?f=t:f>n&&(f=n)}if(a){var b,E;const e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=s.reference[d]-s.floating[e]+(t&&(null==(b=r.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),o=s.reference[d]+s.reference[e]+(t?0:(null==(E=r.offset)?void 0:E[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>o&&(g=o)}return{[h]:f,[d]:g}}}},Ce=(e,t,n)=>{const o=new Map,i={platform:de,...n},s={...i.platform,_c:o};return(async(e,t,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:s=[],platform:r}=n,l=s.filter(Boolean),c=await(null==r.isRTL?void 0:r.isRTL(t));let a=await r.getElementRects({reference:e,floating:t,strategy:i}),{x:u,y:d}=T(a,o,c),h=o,f={},m=0;for(let n=0;n<l.length;n++){const{name:s,fn:p}=l[n],{x:g,y:w,data:y,reset:v}=await p({x:u,y:d,initialPlacement:o,placement:h,strategy:i,middlewareData:f,rects:a,platform:r,elements:{reference:e,floating:t}});u=null!=g?g:u,d=null!=w?w:d,f={...f,[s]:{...f[s],...y}},v&&m<=50&&(m++,"object"==typeof v&&(v.placement&&(h=v.placement),v.rects&&(a=!0===v.rects?await r.getElementRects({reference:e,floating:t,strategy:i}):v.rects),({x:u,y:d}=T(a,h,c))),n=-1)}return{x:u,y:d,placement:h,strategy:i,middlewareData:f}})(e,t,{...i,platform:s})}},961:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t,n){this.el=e,this.options=t,this.events=n,this.el=e,this.options=t,this.events={}}createCollection(e,t){var n;e.push({id:(null===(n=null==t?void 0:t.el)||void 0===n?void 0:n.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var s=t[o]={exports:{}};return e[o].call(s.exports,s,s.exports,n),s.exports}return n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(891)})()));