import Image from 'next/image'
import Link from 'next/link'
import { Heart, Flame, Droplets, Shield, Brain, Scale, ArrowRight, Clock, Users } from 'lucide-react'

const EducationalTopics = () => {
  const topics = [
    {
      icon: Heart,
      title: 'Radzenie sobie po stracie',
      description: 'Praktyczne porady dotyczące pierwszych kroków po utracie bliskiej osoby, procedur formalnych i wsparcia emocjonalnego.',
      image: '/images/sprzatanie_po_tragediach_1.jpg',
      href: '/poradniki/radzenie-sobie-po-stracie',
      highlights: ['Procedury formalne', 'Wsparcie emocjonalne', '<PERSON><PERSON><PERSON> kroki'],
      readTime: '15 min',
      popularity: 'Bardzo popularne'
    },
    {
      icon: Flame,
      title: 'Procedury po pożarze',
      description: 'Kompleksowy przewodnik po działaniach następujących po pożarze - od zabezpieczenia miejsca po odbudowę.',
      image: '/images/sprzatanie_po_pozarach_1.jpg',
      href: '/poradniki/procedury-po-pozarze',
      highlights: ['Z<PERSON>zpieczenie miejsca', 'Dokumentacja szkód', 'Proces odbudowy'],
      readTime: '20 min',
      popularity: 'Popularne'
    },
    {
      icon: Droplets,
      title: 'Działania po powodzi',
      description: 'Szczegółowe instrukcje dotyczące usuwania skutków zalania, suszenia pomieszczeń i zapobiegania pleśni.',
      image: '/images/sprzatanie_po_zalaniach_1.jpg',
      href: '/poradniki/dzialania-po-powodzi',
      highlights: ['Usuwanie wody', 'Suszenie pomieszczeń', 'Zapobieganie pleśni'],
      readTime: '18 min',
      popularity: 'Popularne'
    },
    {
      icon: Shield,
      title: 'Bezpieczeństwo biologiczne',
      description: 'Zasady bezpiecznego postępowania z materiałami biologicznymi i skażeniami w środowisku domowym.',
      image: '/images/dezynfekcja-1.jpg',
      href: '/poradniki/bezpieczenstwo-biologiczne',
      highlights: ['Środki ochrony', 'Dezynfekcja', 'Bezpieczne usuwanie'],
      readTime: '12 min',
      popularity: 'Ekspertowe'
    },
    {
      icon: Brain,
      title: 'Wsparcie psychologiczne',
      description: 'Informacje o dostępnych formach pomocy psychologicznej i technikach radzenia sobie z traumą.',
      image: '/images/opinie_klientów_1.jpg',
      href: '/poradniki/wsparcie-psychologiczne',
      highlights: ['Pomoc specjalistyczna', 'Techniki radzenia', 'Grupy wsparcia'],
      readTime: '10 min',
      popularity: 'Bardzo popularne'
    },
    {
      icon: Scale,
      title: 'Aspekty prawne',
      description: 'Przewodnik po kwestiach prawnych związanych z trudnymi sytuacjami - ubezpieczenia, odpowiedzialność, procedury.',
      image: '/images/realizacja_1.jpg',
      href: '/poradniki/aspekty-prawne',
      highlights: ['Ubezpieczenia', 'Odpowiedzialność', 'Procedury prawne'],
      readTime: '25 min',
      popularity: 'Ekspertowe'
    }
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-max">
        {/* Header */}
        <div className="section-header">
          <h2 className="section-title">
            Tematy <span className="gradient-text">edukacyjne</span>
          </h2>
          <p className="section-subtitle">
            Praktyczna wiedza i wsparcie w trudnych sytuacjach. Każdy temat zawiera szczegółowe poradniki,
            checklisty i przykłady z praktyki sprawdzone przez ekspertów.
          </p>
        </div>

        {/* Topics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {topics.map((topic, index) => {
            const IconComponent = topic.icon
            return (
              <div key={index} className="group card-elevated hover:scale-105 transition-all duration-300">
                {/* Image */}
                <div className="relative h-56 mb-6 rounded-2xl overflow-hidden">
                  <Image
                    src={topic.image}
                    alt={topic.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                  <div className="absolute top-4 left-4 w-14 h-14 bg-white/95 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-soft">
                    <IconComponent className="w-7 h-7 text-primary-600" />
                  </div>
                  <div className="absolute top-4 right-4 expert-badge">
                    {topic.popularity}
                  </div>
                </div>

                {/* Content */}
                <div className="space-y-6">
                  <div className="space-y-3">
                    <h3 className="text-xl font-bold text-secondary-900 group-hover:text-primary-600 transition-colors">
                      {topic.title}
                    </h3>

                    <p className="text-secondary-600 leading-relaxed">
                      {topic.description}
                    </p>
                  </div>

                  {/* Meta info */}
                  <div className="flex items-center gap-x-4 text-sm text-secondary-500">
                    <div className="flex items-center gap-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{topic.readTime}</span>
                    </div>
                    <div className="flex items-center gap-x-1">
                      <Users className="w-4 h-4" />
                      <span>Sprawdzone</span>
                    </div>
                  </div>

                  {/* Highlights */}
                  <div className="flex flex-wrap gap-2">
                    {topic.highlights.map((highlight, idx) => (
                      <span
                        key={idx}
                        className="inline-block bg-primary-50 text-primary-700 px-3 py-1.5 rounded-xl text-sm font-semibold border border-primary-100"
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>

                  {/* CTA */}
                  <Link
                    href={topic.href}
                    className="inline-flex items-center gap-x-2 text-primary-600 hover:text-primary-700 font-bold group-hover:translate-x-1 transition-all duration-200"
                  >
                    <span>Zobacz poradnik</span>
                    <ArrowRight className="w-5 h-5" />
                  </Link>
                </div>
              </div>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-20">
          <div className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl p-12 border border-primary-100">
            <h3 className="text-3xl font-bold text-secondary-900 mb-6">
              Potrzebujesz natychmiastowej pomocy?
            </h3>
            <p className="text-secondary-600 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
              Jeśli znajdujesz się w sytuacji wymagającej profesjonalnej interwencji,
              skorzystaj z usług naszego zaufanego partnera.
            </p>
            <a
              href="https://www.solvictus.pl"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-x-3 bg-primary-600 hover:bg-primary-700 text-white px-10 py-5 rounded-2xl font-bold transition-all duration-200 hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <span>Zobacz, jak to wygląda w praktyce</span>
              <ArrowRight className="w-6 h-6" />
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default EducationalTopics
