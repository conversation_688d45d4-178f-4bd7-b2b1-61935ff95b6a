'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { BookOpen, Mail, GraduationCap, ChevronDown, ExternalLink } from 'lucide-react'

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navigation = [
    { name: 'Strona główna', href: '/' },
    {
      name: '<PERSON>rad<PERSON><PERSON>',
      href: '/poradniki',
      dropdown: [
        { name: 'Rad<PERSON>ie sobie po stracie', href: '/poradniki/radzenie-sobie-po-stracie' },
        { name: 'Procedury po pożarze', href: '/poradniki/procedury-po-pozarze' },
        { name: 'Działania po powodzi', href: '/poradniki/dzialania-po-powodzi' },
        { name: 'Bezpieczeństwo biologiczne', href: '/poradniki/bezpieczenstwo-biologiczne' },
        { name: 'Wsparcie psychologiczne', href: '/poradniki/wsparcie-psychologiczne' },
        { name: 'Aspekty prawne', href: '/poradniki/aspekty-prawne' },
      ]
    },
    {
      name: 'Zasoby',
      href: '/zasoby',
      dropdown: [
        { name: 'Checklisty', href: '/checklisty' },
        { name: 'Case Studies', href: '/case-studies' },
        { name: 'FAQ', href: '/faq' },
        { name: 'Słownik pojęć', href: '/slownik' },
        { name: 'Linki pomocne', href: '/linki' },
      ]
    },
    { name: 'O projekcie', href: '/o-projekcie' },
  ]

  return (
    <>
      {/* Top bar */}
      <div className="bg-primary-600 text-white py-3 px-4 text-sm">
        <div className="max-w-7xl mx-auto flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <BookOpen className="w-4 h-4" />
              <span className="font-medium">Edukacyjne zaplecze treściowe</span>
            </div>
            <div className="hidden md:flex items-center space-x-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <GraduationCap className="w-4 h-4" />
            <span className="text-sm font-medium">Praktyczna wiedza i wsparcie</span>
          </div>
        </div>
      </div>

      {/* Main header */}
      <header className={`sticky top-0 z-50 transition-all duration-300 border-b border-secondary-200 ${
        isScrolled ? 'bg-white/95 backdrop-blur-md shadow-soft' : 'bg-white'
      }`}>
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" aria-label="Global">
          <div className="flex justify-between items-center py-4">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200">
                <BookOpen className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold gradient-text">VERICTUS</h1>
                <p className="text-sm text-secondary-600 -mt-1 font-medium">Edukacja i wsparcie</p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navigation.map((item) => (
                <div key={item.name} className="relative">
                  {item.dropdown ? (
                    <div className="hs-dropdown relative inline-flex">
                      <button
                        id={`hs-dropdown-${item.name}`}
                        type="button"
                        className="hs-dropdown-toggle inline-flex items-center gap-x-2 text-secondary-700 hover:text-primary-600 font-semibold transition-colors duration-200 focus:outline-none focus:text-primary-600"
                      >
                        {item.name}
                        <ChevronDown className="hs-dropdown-open:rotate-180 w-4 h-4 transition-transform duration-200" />
                      </button>

                      <div className="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-medium rounded-xl p-2 mt-2 border border-secondary-100">
                        {item.dropdown.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            className="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-600 transition-colors focus:outline-none focus:bg-primary-50"
                          >
                            {dropdownItem.name}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className="text-secondary-700 hover:text-primary-600 font-semibold transition-colors duration-200 relative group focus:outline-none focus:text-primary-600"
                    >
                      {item.name}
                      <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"></span>
                    </Link>
                  )}
                </div>
              ))}
            </div>

            {/* CTA Button */}
            <div className="hidden lg:flex items-center space-x-4">
              <a
                href="https://www.solvictus.pl"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-x-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                Potrzebujesz pomocy?
                <ExternalLink className="w-4 h-4" />
              </a>
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden">
              <button
                type="button"
                className="hs-collapse-toggle p-2 inline-flex justify-center items-center gap-x-2 rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-soft hover:bg-secondary-50 disabled:opacity-50 disabled:pointer-events-none"
                data-hs-collapse="#navbar-collapse-with-animation"
                aria-controls="navbar-collapse-with-animation"
                aria-label="Toggle navigation"
              >
                <svg className="hs-collapse-open:hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="3" x2="21" y1="6" y2="6"/>
                  <line x1="3" x2="21" y1="12" y2="12"/>
                  <line x1="3" x2="21" y1="18" y2="18"/>
                </svg>
                <svg className="hs-collapse-open:block hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m18 6-12 12"/>
                  <path d="m6 6 12 12"/>
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <div id="navbar-collapse-with-animation" className="hs-collapse hidden overflow-hidden transition-all duration-300 basis-full grow lg:hidden">
            <div className="flex flex-col gap-5 mt-5 border-t border-secondary-200 pt-5">
              {navigation.map((item) => (
                <div key={item.name}>
                  {item.dropdown ? (
                    <div className="hs-accordion">
                      <button
                        className="hs-accordion-toggle w-full text-start flex justify-between items-center py-2 px-3 text-secondary-700 hover:text-primary-600 font-semibold rounded-lg hover:bg-primary-50 transition-colors"
                        aria-controls={`hs-basic-collapse-${item.name}`}
                      >
                        {item.name}
                        <ChevronDown className="hs-accordion-active:rotate-180 w-4 h-4 transition-transform duration-200" />
                      </button>
                      <div id={`hs-basic-collapse-${item.name}`} className="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300">
                        <div className="ml-4 mt-2 space-y-2">
                          {item.dropdown.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              className="block py-2 px-3 text-sm text-secondary-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className="block py-2 px-3 text-secondary-700 hover:text-primary-600 font-semibold rounded-lg hover:bg-primary-50 transition-colors"
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
              <div className="pt-4 border-t border-secondary-200">
                <a
                  href="https://www.solvictus.pl"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center gap-x-2 w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-3 rounded-xl font-semibold transition-colors"
                >
                  Potrzebujesz pomocy?
                  <ExternalLink className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>
        </nav>
      </header>
    </>
  )
}

export default Header
