!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var s=e();for(var i in s)("object"==typeof exports?exports:t)[i]=s[i]}}(self,(()=>(()=>{"use strict";var t={223:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BREAKPOINTS=e.COMBO_BOX_ACCESSIBILITY_KEY_SET=e.SELECT_ACCESSIBILITY_KEY_SET=e.TABS_ACCESSIBILITY_KEY_SET=e.OVERLAY_ACCESSIBILITY_KEY_SET=e.DROPDOWN_ACCESSIBILITY_KEY_SET=e.POSITIONS=void 0,e.POSITIONS={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"},e.DROPDOWN_ACCESSIBILITY_KEY_SET=["Escape","ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Home","End","Enter"],e.OVERLAY_ACCESSIBILITY_KEY_SET=["Escape","Tab"],e.TABS_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End"],e.SELECT_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter","Space","Tab"],e.COMBO_BOX_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter"],e.BREAKPOINTS={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},292:function(t,e){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(e,"__esModule",{value:!0}),e.menuSearchHistory=e.classToClassList=e.htmlToElement=e.afterTransition=e.dispatch=e.debounce=e.isScrollable=e.isParentOrElementHidden=e.isJson=e.isIpadOS=e.isIOS=e.isDirectChild=e.isFormElement=e.isFocused=e.isEnoughSpace=e.getHighestZIndex=e.getZIndex=e.getClassPropertyAlt=e.getClassProperty=e.stringToBoolean=void 0;e.stringToBoolean=t=>"true"===t;e.getClassProperty=(t,e,s="")=>(window.getComputedStyle(t).getPropertyValue(e)||s).replace(" ","");e.getClassPropertyAlt=(t,e,s="")=>{let i="";return t.classList.forEach((t=>{t.includes(e)&&(i=t)})),i.match(/:(.*)]/)?i.match(/:(.*)]/)[1]:s};const s=t=>window.getComputedStyle(t).getPropertyValue("z-index");e.getZIndex=s;e.getHighestZIndex=t=>{let e=Number.NEGATIVE_INFINITY;return t.forEach((t=>{let i=s(t);"auto"!==i&&(i=parseInt(i,10),i>e&&(e=i))})),e};e.isDirectChild=(t,e)=>{const s=t.children;for(let t=0;t<s.length;t++)if(s[t]===e)return!0;return!1};e.isEnoughSpace=(t,e,s="auto",i=10,o=null)=>{const l=e.getBoundingClientRect(),n=o?o.getBoundingClientRect():null,a=window.innerHeight,r=n?l.top-n.top:l.top,d=(o?n.bottom:a)-l.bottom,h=t.clientHeight+i;return"bottom"===s?d>=h:"top"===s?r>=h:r>=h||d>=h};e.isFocused=t=>document.activeElement===t;e.isFormElement=t=>t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement;e.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isJson=t=>{if("string"!=typeof t)return!1;const e=t.trim()[0],s=t.trim().slice(-1);if("{"===e&&"}"===s||"["===e&&"]"===s)try{return JSON.parse(t),!0}catch(t){return!1}return!1};const i=t=>{if(!t)return!1;return"none"===window.getComputedStyle(t).display||i(t.parentElement)};e.isParentOrElementHidden=i;e.isScrollable=t=>{const e=window.getComputedStyle(t),s=e.overflowY,i=e.overflowX,o=("scroll"===s||"auto"===s)&&t.scrollHeight>t.clientHeight,l=("scroll"===i||"auto"===i)&&t.scrollWidth>t.clientWidth;return o||l};e.debounce=(t,e=200)=>{let s;return(...i)=>{clearTimeout(s),s=setTimeout((()=>{t.apply(this,i)}),e)}};e.dispatch=(t,e,s=null)=>{const i=new CustomEvent(t,{detail:{payload:s},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(i)};e.afterTransition=(t,e)=>{const s=()=>{e(),t.removeEventListener("transitionend",s,!0)},i=window.getComputedStyle(t),o=i.getPropertyValue("transition-duration");"none"!==i.getPropertyValue("transition-property")&&parseFloat(o)>0?t.addEventListener("transitionend",s,!0):e()};e.htmlToElement=t=>{const e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild};e.classToClassList=(t,e,s=" ",i="add")=>{t.split(s).forEach((t=>"add"===i?e.classList.add(t):e.classList.remove(t)))};const o={historyIndex:-1,addHistory(t){this.historyIndex=t},existsInHistory(t){return t>this.historyIndex},clearHistory(){this.historyIndex=-1}};e.menuSearchHistory=o},442:function(t,e,s){
/*
 * HSSelect
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var i=this&&this.__awaiter||function(t,e,s,i){return new(s||(s=Promise))((function(o,l){function n(t){try{r(i.next(t))}catch(t){l(t)}}function a(t){try{r(i.throw(t))}catch(t){l(t)}}function r(t){var e;t.done?o(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(n,a)}r((i=i.apply(t,e||[])).next())}))},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const l=s(292),n=o(s(961)),a=s(223);class r extends n.default{constructor(t,e){var s,i,o,l,n;super(t,e),this.optionId=0;const a=t.getAttribute("data-hs-select"),r=a?JSON.parse(a):{},d=Object.assign(Object.assign({},r),e);this.value=(null==d?void 0:d.value)||this.el.value||null,this.placeholder=(null==d?void 0:d.placeholder)||"Select...",this.hasSearch=(null==d?void 0:d.hasSearch)||!1,this.minSearchLength=null!==(s=null==d?void 0:d.minSearchLength)&&void 0!==s?s:0,this.preventSearchFocus=(null==d?void 0:d.preventSearchFocus)||!1,this.mode=(null==d?void 0:d.mode)||"default",this.viewport=void 0!==(null==d?void 0:d.viewport)?document.querySelector(null==d?void 0:d.viewport):null,this.isOpened=Boolean(null==d?void 0:d.isOpened)||!1,this.isMultiple=this.el.hasAttribute("multiple")||!1,this.isDisabled=this.el.hasAttribute("disabled")||!1,this.selectedItems=[],this.apiUrl=(null==d?void 0:d.apiUrl)||null,this.apiQuery=(null==d?void 0:d.apiQuery)||null,this.apiOptions=(null==d?void 0:d.apiOptions)||null,this.apiSearchQueryKey=(null==d?void 0:d.apiSearchQueryKey)||null,this.apiDataPart=(null==d?void 0:d.apiDataPart)||null,this.apiLoadMore=!0===(null==d?void 0:d.apiLoadMore)?{perPage:10,scrollThreshold:100}:"object"==typeof(null==d?void 0:d.apiLoadMore)&&null!==(null==d?void 0:d.apiLoadMore)&&{perPage:d.apiLoadMore.perPage||10,scrollThreshold:d.apiLoadMore.scrollThreshold||100},this.apiFieldsMap=(null==d?void 0:d.apiFieldsMap)||null,this.apiIconTag=(null==d?void 0:d.apiIconTag)||null,this.apiSelectedValues=(null==d?void 0:d.apiSelectedValues)||null,this.currentPage=0,this.isLoading=!1,this.hasMore=!0,this.wrapperClasses=(null==d?void 0:d.wrapperClasses)||null,this.toggleTag=(null==d?void 0:d.toggleTag)||null,this.toggleClasses=(null==d?void 0:d.toggleClasses)||null,this.toggleCountText=void 0===typeof(null==d?void 0:d.toggleCountText)?null:d.toggleCountText,this.toggleCountTextPlacement=(null==d?void 0:d.toggleCountTextPlacement)||"postfix",this.toggleCountTextMinItems=(null==d?void 0:d.toggleCountTextMinItems)||1,this.toggleCountTextMode=(null==d?void 0:d.toggleCountTextMode)||"countAfterLimit",this.toggleSeparators={items:(null===(i=null==d?void 0:d.toggleSeparators)||void 0===i?void 0:i.items)||", ",betweenItemsAndCounter:(null===(o=null==d?void 0:d.toggleSeparators)||void 0===o?void 0:o.betweenItemsAndCounter)||"and"},this.tagsItemTemplate=(null==d?void 0:d.tagsItemTemplate)||null,this.tagsItemClasses=(null==d?void 0:d.tagsItemClasses)||null,this.tagsInputId=(null==d?void 0:d.tagsInputId)||null,this.tagsInputClasses=(null==d?void 0:d.tagsInputClasses)||null,this.dropdownTag=(null==d?void 0:d.dropdownTag)||null,this.dropdownClasses=(null==d?void 0:d.dropdownClasses)||null,this.dropdownDirectionClasses=(null==d?void 0:d.dropdownDirectionClasses)||null,this.dropdownSpace=(null==d?void 0:d.dropdownSpace)||10,this.dropdownPlacement=(null==d?void 0:d.dropdownPlacement)||null,this.dropdownVerticalFixedPlacement=(null==d?void 0:d.dropdownVerticalFixedPlacement)||null,this.dropdownScope=(null==d?void 0:d.dropdownScope)||"parent",this.dropdownAutoPlacement=(null==d?void 0:d.dropdownAutoPlacement)||!1,this.searchTemplate=(null==d?void 0:d.searchTemplate)||null,this.searchWrapperTemplate=(null==d?void 0:d.searchWrapperTemplate)||null,this.searchWrapperClasses=(null==d?void 0:d.searchWrapperClasses)||"bg-white p-2 sticky top-0",this.searchId=(null==d?void 0:d.searchId)||null,this.searchLimit=(null==d?void 0:d.searchLimit)||1/0,this.isSearchDirectMatch=void 0===(null==d?void 0:d.isSearchDirectMatch)||(null==d?void 0:d.isSearchDirectMatch),this.searchClasses=(null==d?void 0:d.searchClasses)||"block w-[calc(100%-32px)] text-sm border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 py-2 px-3 my-2 mx-4",this.searchPlaceholder=(null==d?void 0:d.searchPlaceholder)||"Search...",this.searchNoResultTemplate=(null==d?void 0:d.searchNoResultTemplate)||"<span></span>",this.searchNoResultText=(null==d?void 0:d.searchNoResultText)||"No results found",this.searchNoResultClasses=(null==d?void 0:d.searchNoResultClasses)||"px-4 text-sm text-gray-800 dark:text-neutral-200",this.optionAllowEmptyOption=void 0!==(null==d?void 0:d.optionAllowEmptyOption)&&(null==d?void 0:d.optionAllowEmptyOption),this.optionTemplate=(null==d?void 0:d.optionTemplate)||null,this.optionTag=(null==d?void 0:d.optionTag)||null,this.optionClasses=(null==d?void 0:d.optionClasses)||null,this.extraMarkup=(null==d?void 0:d.extraMarkup)||null,this.descriptionClasses=(null==d?void 0:d.descriptionClasses)||null,this.iconClasses=(null==d?void 0:d.iconClasses)||null,this.isAddTagOnEnter=null===(l=null==d?void 0:d.isAddTagOnEnter)||void 0===l||l,this.isSelectedOptionOnTop=null===(n=null==d?void 0:d.isSelectedOptionOnTop)||void 0===n||n,this.animationInProcess=!1,this.selectOptions=[],this.remoteOptions=[],this.tagsInputHelper=null,this.init()}wrapperClick(t){t.target.closest("[data-hs-select-dropdown]")||t.target.closest("[data-tag-value]")||this.tagsInput.focus()}toggleClick(){if(this.isDisabled)return!1;this.toggleFn()}tagsInputFocus(){this.isOpened||this.open()}tagsInputInput(){this.calculateInputWidth()}tagsInputInputSecond(t){this.apiUrl||this.searchOptions(t.target.value)}tagsInputKeydown(t){if("Enter"===t.key&&this.isAddTagOnEnter){const e=t.target.value;if(this.selectOptions.find((t=>t.val===e)))return!1;this.addSelectOption(e,e),this.buildOption(e,e),this.buildOriginalOption(e,e),this.dropdown.querySelector(`[data-value="${e}"]`).click(),this.resetTagsInputField()}}searchInput(t){const e=t.target.value;this.apiUrl?this.remoteSearch(e):this.searchOptions(e)}setValue(t){if(this.value=t,this.clearSelections(),Array.isArray(t))if("tags"===this.mode){this.unselectMultipleItems(),this.selectMultipleItems(),this.selectedItems=[];this.wrapper.querySelectorAll("[data-tag-value]").forEach((t=>t.remove())),this.setTagsItems(),this.reassignTagsInputPlaceholder(this.value.length?"":this.placeholder)}else this.toggleTextWrapper.innerHTML=this.value.length?this.stringFromValue():this.placeholder,this.unselectMultipleItems(),this.selectMultipleItems();else this.setToggleTitle(),this.toggle.querySelector("[data-icon]")&&this.setToggleIcon(),this.toggle.querySelector("[data-title]")&&this.setToggleTitle(),this.selectSingleItem()}init(){this.createCollection(window.$hsSelectCollection,this),this.build()}build(){if(this.el.style.display="none",this.el.children&&Array.from(this.el.children).filter((t=>this.optionAllowEmptyOption||!this.optionAllowEmptyOption&&t.value&&""!==t.value)).forEach((t=>{const e=t.getAttribute("data-hs-select-option");this.selectOptions=[...this.selectOptions,{title:t.textContent,val:t.value,disabled:t.disabled,options:"undefined"!==e?JSON.parse(e):null}]})),this.optionAllowEmptyOption&&!this.value&&(this.value=""),this.isMultiple){const t=Array.from(this.el.children).filter((t=>t.selected));if(t){const e=[];t.forEach((t=>{e.push(t.value)})),this.value=e}}this.buildWrapper(),"tags"===this.mode?this.buildTags():this.buildToggle(),this.buildDropdown(),this.extraMarkup&&this.buildExtraMarkup()}buildWrapper(){this.wrapper=document.createElement("div"),this.wrapper.classList.add("hs-select","relative"),"tags"===this.mode&&(this.onWrapperClickListener=t=>this.wrapperClick(t),this.wrapper.addEventListener("click",this.onWrapperClickListener)),this.wrapperClasses&&(0,l.classToClassList)(this.wrapperClasses,this.wrapper),this.el.before(this.wrapper),this.wrapper.append(this.el)}buildExtraMarkup(){const t=t=>{const e=(0,l.htmlToElement)(t);return this.wrapper.append(e),e},e=t=>{t.classList.contains("--prevent-click")||t.addEventListener("click",(t=>{t.stopPropagation(),this.isDisabled||this.toggleFn()}))};if(Array.isArray(this.extraMarkup))this.extraMarkup.forEach((s=>{const i=t(s);e(i)}));else{const s=t(this.extraMarkup);e(s)}}buildToggle(){var t,e;let s,i;this.toggleTextWrapper=document.createElement("span"),this.toggleTextWrapper.classList.add("truncate"),this.toggle=(0,l.htmlToElement)(this.toggleTag||"<div></div>"),s=this.toggle.querySelector("[data-icon]"),i=this.toggle.querySelector("[data-title]"),!this.isMultiple&&s&&this.setToggleIcon(),!this.isMultiple&&i&&this.setToggleTitle(),this.isMultiple?this.toggleTextWrapper.innerHTML=this.value.length?this.stringFromValue():this.placeholder:this.toggleTextWrapper.innerHTML=(null===(t=this.getItemByValue(this.value))||void 0===t?void 0:t.title)||this.placeholder,i||this.toggle.append(this.toggleTextWrapper),this.toggleClasses&&(0,l.classToClassList)(this.toggleClasses,this.toggle),this.isDisabled&&this.toggle.classList.add("disabled"),this.wrapper&&this.wrapper.append(this.toggle),(null===(e=this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.isOpened?this.toggle.ariaExpanded="true":this.toggle.ariaExpanded="false"),this.onToggleClickListener=()=>this.toggleClick(),this.toggle.addEventListener("click",this.onToggleClickListener)}setToggleIcon(){var t;const e=this.getItemByValue(this.value),s=this.toggle.querySelector("[data-icon]");if(s){s.innerHTML="";const i=(0,l.htmlToElement)(this.apiUrl&&this.apiIconTag?this.apiIconTag||"":(null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.icon)||"");this.value&&this.apiUrl&&this.apiIconTag&&e[this.apiFieldsMap.icon]&&(i.src=e[this.apiFieldsMap.icon]||""),s.append(i),(null==i?void 0:i.src)?s.classList.remove("hidden"):s.classList.add("hidden")}}setToggleTitle(){const t=this.toggle.querySelector("[data-title]");let e=this.placeholder;if(this.optionAllowEmptyOption&&""===this.value){const t=this.selectOptions.find((t=>""===t.val));e=(null==t?void 0:t.title)||this.placeholder}else if(this.value)if(this.apiUrl){const t=this.remoteOptions.find((t=>`${t[this.apiFieldsMap.val]}`===this.value||`${t[this.apiFieldsMap.title]}`===this.value));t&&(e=t[this.apiFieldsMap.title])}else{const t=this.selectOptions.find((t=>t.val===this.value));t&&(e=t.title)}t?(t.innerHTML=e,t.classList.add("truncate"),this.toggle.append(t)):this.toggleTextWrapper.innerHTML=e}buildTags(){this.isDisabled&&this.wrapper.classList.add("disabled"),this.buildTagsInput(),this.setTagsItems()}reassignTagsInputPlaceholder(t){this.tagsInput.placeholder=t,this.tagsInputHelper.innerHTML=t,this.calculateInputWidth()}buildTagsItem(t){var e,s,i,o,n;const a=this.getItemByValue(t);let r,d,h,p;const c=document.createElement("div");if(c.setAttribute("data-tag-value",t),this.tagsItemClasses&&(0,l.classToClassList)(this.tagsItemClasses,c),this.tagsItemTemplate&&(r=(0,l.htmlToElement)(this.tagsItemTemplate),c.append(r)),(null===(e=null==a?void 0:a.options)||void 0===e?void 0:e.icon)||this.apiIconTag){const t=(0,l.htmlToElement)(this.apiUrl&&this.apiIconTag?this.apiIconTag:null===(s=null==a?void 0:a.options)||void 0===s?void 0:s.icon);this.apiUrl&&this.apiIconTag&&a[this.apiFieldsMap.icon]&&(t.src=a[this.apiFieldsMap.icon]||""),p=r?r.querySelector("[data-icon]"):document.createElement("span"),p.append(t),r||c.append(p)}!r||!r.querySelector("[data-icon]")||(null===(i=null==a?void 0:a.options)||void 0===i?void 0:i.icon)||this.apiUrl||this.apiIconTag||a[null===(o=this.apiFieldsMap)||void 0===o?void 0:o.icon]||r.querySelector("[data-icon]").classList.add("hidden"),d=r?r.querySelector("[data-title]"):document.createElement("span"),this.apiUrl&&(null===(n=this.apiFieldsMap)||void 0===n?void 0:n.title)&&a[this.apiFieldsMap.title]?d.textContent=a[this.apiFieldsMap.title]:d.textContent=a.title||"",r||c.append(d),r?h=r.querySelector("[data-remove]"):(h=document.createElement("span"),h.textContent="X",c.append(h)),h.addEventListener("click",(()=>{this.value=this.value.filter((e=>e!==t)),this.selectedItems=this.selectedItems.filter((e=>e!==t)),this.value.length||this.reassignTagsInputPlaceholder(this.placeholder),this.unselectMultipleItems(),this.selectMultipleItems(),c.remove(),this.triggerChangeEventForNativeSelect()})),this.wrapper.append(c)}getItemByValue(t){return this.apiUrl?this.remoteOptions.find((e=>`${e[this.apiFieldsMap.val]}`===t||e[this.apiFieldsMap.title]===t)):this.selectOptions.find((e=>e.val===t))}setTagsItems(){this.value&&this.value.forEach((t=>{this.selectedItems.includes(t)||this.buildTagsItem(t),this.selectedItems=this.selectedItems.includes(t)?this.selectedItems:[...this.selectedItems,t]})),this.isOpened&&this.floatingUIInstance&&this.floatingUIInstance.update()}buildTagsInput(){this.tagsInput=document.createElement("input"),this.tagsInputId&&(this.tagsInput.id=this.tagsInputId),this.tagsInputClasses&&(0,l.classToClassList)(this.tagsInputClasses,this.tagsInput),this.onTagsInputFocusListener=()=>this.tagsInputFocus(),this.onTagsInputInputListener=()=>this.tagsInputInput(),this.onTagsInputInputSecondListener=(0,l.debounce)((t=>this.tagsInputInputSecond(t))),this.onTagsInputKeydownListener=t=>this.tagsInputKeydown(t),this.tagsInput.addEventListener("focus",this.onTagsInputFocusListener),this.tagsInput.addEventListener("input",this.onTagsInputInputListener),this.tagsInput.addEventListener("input",this.onTagsInputInputSecondListener),this.tagsInput.addEventListener("keydown",this.onTagsInputKeydownListener),this.wrapper.append(this.tagsInput),setTimeout((()=>{this.adjustInputWidth(),this.reassignTagsInputPlaceholder(this.value.length?"":this.placeholder)}))}buildDropdown(){this.dropdown=(0,l.htmlToElement)(this.dropdownTag||"<div></div>"),this.dropdown.setAttribute("data-hs-select-dropdown",""),"parent"===this.dropdownScope&&(this.dropdown.classList.add("absolute"),this.dropdownVerticalFixedPlacement||this.dropdown.classList.add("top-full")),this.dropdown.role="listbox",this.dropdown.tabIndex=-1,this.dropdown.ariaOrientation="vertical",this.isOpened||this.dropdown.classList.add("hidden"),this.dropdownClasses&&(0,l.classToClassList)(this.dropdownClasses,this.dropdown),this.wrapper&&this.wrapper.append(this.dropdown),this.dropdown&&this.hasSearch&&this.buildSearch(),this.selectOptions&&this.selectOptions.forEach(((t,e)=>this.buildOption(t.title,t.val,t.disabled,t.selected,t.options,`${e}`))),this.apiUrl&&this.optionsFromRemoteData(),"window"===this.dropdownScope&&this.buildFloatingUI(),this.dropdown&&this.apiLoadMore&&this.setupInfiniteScroll()}setupInfiniteScroll(){this.dropdown.addEventListener("scroll",this.handleScroll.bind(this))}handleScroll(){return i(this,void 0,void 0,(function*(){if(!this.dropdown||this.isLoading||!this.hasMore||!this.apiLoadMore)return;const{scrollTop:t,scrollHeight:e,clientHeight:s}=this.dropdown;e-t-s<("object"==typeof this.apiLoadMore?this.apiLoadMore.scrollThreshold:100)&&(yield this.loadMore())}))}loadMore(){return i(this,void 0,void 0,(function*(){var t,e,s,i;if(this.apiUrl&&!this.isLoading&&this.hasMore&&this.apiLoadMore){this.isLoading=!0;try{const o=new URL(this.apiUrl),l=(null===(t=this.apiFieldsMap)||void 0===t?void 0:t.page)||(null===(e=this.apiFieldsMap)||void 0===e?void 0:e.offset)||"page",n=!!(null===(s=this.apiFieldsMap)||void 0===s?void 0:s.offset),a="object"==typeof this.apiLoadMore?this.apiLoadMore.perPage:10;if(n){const t=this.currentPage*a;o.searchParams.set(l,t.toString()),this.currentPage++}else this.currentPage++,o.searchParams.set(l,this.currentPage.toString());o.searchParams.set((null===(i=this.apiFieldsMap)||void 0===i?void 0:i.limit)||"limit",a.toString());const r=yield fetch(o.toString(),this.apiOptions||{}),d=yield r.json(),h=this.apiDataPart?d[this.apiDataPart]:d.results,p=d.count||0,c=this.currentPage*a;h&&h.length>0?(this.remoteOptions=[...this.remoteOptions||[],...h],this.buildOptionsFromRemoteData(h),this.hasMore=c<p):this.hasMore=!1}catch(t){this.hasMore=!1,console.error("Error loading more options:",t)}finally{this.isLoading=!1}}}))}buildFloatingUI(){if("undefined"!=typeof FloatingUIDOM&&FloatingUIDOM.computePosition){document.body.appendChild(this.dropdown);const t="tags"===this.mode?this.wrapper:this.toggle,e=[FloatingUIDOM.offset([0,5])];this.dropdownAutoPlacement&&"function"==typeof FloatingUIDOM.flip&&e.push(FloatingUIDOM.flip({fallbackPlacements:["bottom-start","bottom-end","top-start","top-end"]}));const s={placement:a.POSITIONS[this.dropdownPlacement]||"bottom",strategy:"fixed",middleware:e},i=()=>{FloatingUIDOM.computePosition(t,this.dropdown,s).then((({x:t,y:e,placement:s})=>{Object.assign(this.dropdown.style,{position:"fixed",left:`${t}px`,top:`${e}px`,["margin"+("bottom"===s?"Top":"top"===s?"Bottom":"right"===s?"Left":"Right")]:`${this.dropdownSpace}px`}),this.dropdown.setAttribute("data-placement",s)}))};i();const o=FloatingUIDOM.autoUpdate(t,this.dropdown,i);this.floatingUIInstance={update:i,destroy:o}}else console.error("FloatingUIDOM not found! Please enable it on the page.")}updateDropdownWidth(){const t="tags"===this.mode?this.wrapper:this.toggle;this.dropdown.style.width=`${t.clientWidth}px`}buildSearch(){let t;this.searchWrapper=(0,l.htmlToElement)(this.searchWrapperTemplate||"<div></div>"),this.searchWrapperClasses&&(0,l.classToClassList)(this.searchWrapperClasses,this.searchWrapper),t=this.searchWrapper.querySelector("[data-input]");const e=(0,l.htmlToElement)(this.searchTemplate||'<input type="text">');this.search="INPUT"===e.tagName?e:e.querySelector(":scope input"),this.search.placeholder=this.searchPlaceholder,this.searchClasses&&(0,l.classToClassList)(this.searchClasses,this.search),this.searchId&&(this.search.id=this.searchId),this.onSearchInputListener=(0,l.debounce)((t=>this.searchInput(t))),this.search.addEventListener("input",this.onSearchInputListener),t?t.append(e):this.searchWrapper.append(e),this.dropdown.append(this.searchWrapper)}buildOption(t,e,s=!1,i=!1,o,n="1",a){var r;let d=null,h=null,p=null,c=null;const u=(0,l.htmlToElement)(this.optionTag||"<div></div>");if(u.setAttribute("data-value",e),u.setAttribute("data-title-value",t),u.setAttribute("tabIndex",n),u.classList.add("cursor-pointer"),u.setAttribute("data-id",a||`${this.optionId}`),a||this.optionId++,s&&u.classList.add("disabled"),i&&(this.isMultiple?this.value=[...this.value,e]:this.value=e),this.optionTemplate&&(d=(0,l.htmlToElement)(this.optionTemplate),u.append(d)),d?(h=d.querySelector("[data-title]"),h.textContent=t||""):u.textContent=t||"",o){if(o.icon){const e=(0,l.htmlToElement)(null!==(r=this.apiIconTag)&&void 0!==r?r:o.icon);if(e.classList.add("max-w-full"),this.apiUrl&&(e.setAttribute("alt",t),e.setAttribute("src",o.icon)),d)p=d.querySelector("[data-icon]"),p.append(e);else{const t=(0,l.htmlToElement)("<div></div>");this.iconClasses&&(0,l.classToClassList)(this.iconClasses,t),t.append(e),u.append(t)}}if(o.description)if(d)c=d.querySelector("[data-description]"),c&&c.append(o.description);else{const t=(0,l.htmlToElement)("<div></div>");t.textContent=o.description,this.descriptionClasses&&(0,l.classToClassList)(this.descriptionClasses,t),u.append(t)}}d&&d.querySelector("[data-icon]")&&!o&&!(null==o?void 0:o.icon)&&d.querySelector("[data-icon]").classList.add("hidden"),this.value&&(this.isMultiple?this.value.includes(e):this.value===e)&&u.classList.add("selected"),s||u.addEventListener("click",(()=>this.onSelectOption(e))),this.optionClasses&&(0,l.classToClassList)(this.optionClasses,u),this.dropdown&&this.dropdown.append(u),i&&this.setNewValue()}buildOptionFromRemoteData(t,e,s=!1,i=!1,o="1",l,n){o?this.buildOption(t,e,s,i,n,o,l):alert("ID parameter is required for generating remote options! Please check your API endpoint have it.")}buildOptionsFromRemoteData(t){t.forEach(((t,e)=>{let s=null,i="",o="";const l={id:"",val:"",title:"",icon:null,description:null,rest:{}};Object.keys(t).forEach((e=>{var n;t[this.apiFieldsMap.id]&&(s=t[this.apiFieldsMap.id]),t[this.apiFieldsMap.val]&&(o=`${t[this.apiFieldsMap.val]}`),t[this.apiFieldsMap.title]&&(i=t[this.apiFieldsMap.title],t[this.apiFieldsMap.val]||(o=i)),t[this.apiFieldsMap.icon]&&(l.icon=t[this.apiFieldsMap.icon]),t[null===(n=this.apiFieldsMap)||void 0===n?void 0:n.description]&&(l.description=t[this.apiFieldsMap.description]),l.rest[e]=t[e]}));if(!this.dropdown.querySelector(`[data-value="${o}"]`)){const t=!!this.apiSelectedValues&&(Array.isArray(this.apiSelectedValues)?this.apiSelectedValues.includes(o):this.apiSelectedValues===o);this.buildOriginalOption(i,o,s,!1,t,l),this.buildOptionFromRemoteData(i,o,!1,t,`${e}`,s,l),t&&(this.isMultiple?(this.value||(this.value=[]),Array.isArray(this.value)&&(this.value=[...this.value,o])):this.value=o)}})),this.sortElements(this.el,"option"),this.sortElements(this.dropdown,"[data-value]")}optionsFromRemoteData(){return i(this,arguments,void 0,(function*(t=""){const e=yield this.apiRequest(t);this.remoteOptions=e,e.length?this.buildOptionsFromRemoteData(this.remoteOptions):console.log("There is no data were responded!")}))}apiRequest(){return i(this,arguments,void 0,(function*(t=""){var e,s,i,o;try{let l=this.apiUrl;const n=this.apiSearchQueryKey?`${this.apiSearchQueryKey}=${t.toLowerCase()}`:null,a=this.apiQuery||"",r=this.apiOptions||{},d=new URLSearchParams(a),h=d.toString();if(this.apiLoadMore){const t=(null===(e=this.apiFieldsMap)||void 0===e?void 0:e.page)||(null===(s=this.apiFieldsMap)||void 0===s?void 0:s.offset)||"page",n=!!(null===(i=this.apiFieldsMap)||void 0===i?void 0:i.offset),a=(null===(o=this.apiFieldsMap)||void 0===o?void 0:o.limit)||"limit",r="object"==typeof this.apiLoadMore?this.apiLoadMore.perPage:10;d.delete(t),d.delete(a),l+=n?`?${t}=0`:`?${t}=1`,l+=`&${a}=${r}`}else(n||h)&&(l+=`?${n||h}`);n&&h?l+=`&${h}`:!n||h||this.apiLoadMore||(l+=`?${n}`);const p=yield fetch(l,r),c=yield p.json();return this.apiDataPart?c[this.apiDataPart]:c}catch(t){console.error(t)}}))}sortElements(t,e){const s=Array.from(t.querySelectorAll(e));this.isSelectedOptionOnTop&&s.sort(((t,e)=>{const s=t.classList.contains("selected")||t.hasAttribute("selected"),i=e.classList.contains("selected")||e.hasAttribute("selected");return s&&!i?-1:!s&&i?1:0})),s.forEach((e=>t.appendChild(e)))}remoteSearch(t){return i(this,void 0,void 0,(function*(){if(t.length<=this.minSearchLength){const t=yield this.apiRequest("");return this.remoteOptions=t,Array.from(this.dropdown.querySelectorAll("[data-value]")).forEach((t=>t.remove())),Array.from(this.el.querySelectorAll("option[value]")).forEach((t=>{t.remove()})),t.length?this.buildOptionsFromRemoteData(t):console.log("No data responded!"),!1}const e=yield this.apiRequest(t);this.remoteOptions=e;let s=e.map((t=>`${t.id}`)),i=null;const o=this.dropdown.querySelectorAll("[data-value]");this.el.querySelectorAll("[data-hs-select-option]").forEach((t=>{var e;const i=t.getAttribute("data-id");s.includes(i)||(null===(e=this.value)||void 0===e?void 0:e.includes(t.value))||this.destroyOriginalOption(t.value)})),o.forEach((t=>{var e;const i=t.getAttribute("data-id");s.includes(i)||(null===(e=this.value)||void 0===e?void 0:e.includes(t.getAttribute("data-value")))?s=s.filter((t=>t!==i)):this.destroyOption(t.getAttribute("data-value"))})),i=e.filter((t=>s.includes(`${t.id}`))),i.length?this.buildOptionsFromRemoteData(i):console.log("No data responded!")}))}destroyOption(t){const e=this.dropdown.querySelector(`[data-value="${t}"]`);if(!e)return!1;e.remove()}buildOriginalOption(t,e,s,i,o,n){const a=(0,l.htmlToElement)("<option></option>");a.setAttribute("value",e),i&&a.setAttribute("disabled","disabled"),o&&a.setAttribute("selected","selected"),s&&a.setAttribute("data-id",s),a.setAttribute("data-hs-select-option",JSON.stringify(n)),a.innerText=t,this.el.append(a)}destroyOriginalOption(t){const e=this.el.querySelector(`[value="${t}"]`);if(!e)return!1;e.remove()}buildTagsInputHelper(){this.tagsInputHelper=document.createElement("span"),this.tagsInputHelper.style.fontSize=window.getComputedStyle(this.tagsInput).fontSize,this.tagsInputHelper.style.fontFamily=window.getComputedStyle(this.tagsInput).fontFamily,this.tagsInputHelper.style.fontWeight=window.getComputedStyle(this.tagsInput).fontWeight,this.tagsInputHelper.style.letterSpacing=window.getComputedStyle(this.tagsInput).letterSpacing,this.tagsInputHelper.style.visibility="hidden",this.tagsInputHelper.style.whiteSpace="pre",this.tagsInputHelper.style.position="absolute",this.wrapper.appendChild(this.tagsInputHelper)}calculateInputWidth(){this.tagsInputHelper.textContent=this.tagsInput.value||this.tagsInput.placeholder;const t=parseInt(window.getComputedStyle(this.tagsInput).paddingLeft)+parseInt(window.getComputedStyle(this.tagsInput).paddingRight),e=parseInt(window.getComputedStyle(this.tagsInput).borderLeftWidth)+parseInt(window.getComputedStyle(this.tagsInput).borderRightWidth),s=this.tagsInputHelper.offsetWidth+t+e,i=this.wrapper.offsetWidth-(parseInt(window.getComputedStyle(this.wrapper).paddingLeft)+parseInt(window.getComputedStyle(this.wrapper).paddingRight));this.tagsInput.style.width=`${Math.min(s,i)+2}px`}adjustInputWidth(){this.buildTagsInputHelper(),this.calculateInputWidth()}onSelectOption(t){if(this.clearSelections(),this.isMultiple?(this.value=this.value.includes(t)?Array.from(this.value).filter((e=>e!==t)):[...Array.from(this.value),t],this.selectMultipleItems(),this.setNewValue()):(this.value=t,this.selectSingleItem(),this.setNewValue()),this.fireEvent("change",this.value),"tags"===this.mode){const t=this.selectedItems.filter((t=>!this.value.includes(t)));t.length&&t.forEach((t=>{this.selectedItems=this.selectedItems.filter((e=>e!==t)),this.wrapper.querySelector(`[data-tag-value="${t}"]`).remove()})),this.resetTagsInputField()}this.isMultiple||(this.toggle.querySelector("[data-icon]")&&this.setToggleIcon(),this.toggle.querySelector("[data-title]")&&this.setToggleTitle(),this.close(!0)),this.value.length||"tags"!==this.mode||this.reassignTagsInputPlaceholder(this.placeholder),this.isOpened&&"tags"===this.mode&&this.tagsInput&&this.tagsInput.focus(),this.triggerChangeEventForNativeSelect()}triggerChangeEventForNativeSelect(){const t=new Event("change",{bubbles:!0});this.el.dispatchEvent(t),(0,l.dispatch)("change.hs.select",this.el,this.value)}addSelectOption(t,e,s,i,o){this.selectOptions=[...this.selectOptions,{title:t,val:e,disabled:s,selected:i,options:o}]}removeSelectOption(t,e=!1){if(!!!this.selectOptions.some((e=>e.val===t)))return!1;this.selectOptions=this.selectOptions.filter((e=>e.val!==t)),this.value=e?this.value.filter((e=>e!==t)):t}resetTagsInputField(){this.tagsInput.value="",this.reassignTagsInputPlaceholder(""),this.searchOptions("")}clearSelections(){Array.from(this.dropdown.children).forEach((t=>{t.classList.contains("selected")&&t.classList.remove("selected")})),Array.from(this.el.children).forEach((t=>{t.selected&&(t.selected=!1)}))}setNewValue(){if("tags"===this.mode)this.setTagsItems();else if(this.optionAllowEmptyOption&&""===this.value){const t=this.selectOptions.find((t=>""===t.val));this.toggleTextWrapper.innerHTML=(null==t?void 0:t.title)||this.placeholder}else if(this.value)if(this.apiUrl){const t=this.dropdown.querySelector(`[data-value="${this.value}"]`);if(t)this.toggleTextWrapper.innerHTML=t.getAttribute("data-title-value")||this.placeholder;else{const t=this.remoteOptions.find((t=>(t[this.apiFieldsMap.val]?`${t[this.apiFieldsMap.val]}`:t[this.apiFieldsMap.title])===this.value));this.toggleTextWrapper.innerHTML=t?`${t[this.apiFieldsMap.title]}`:this.stringFromValue()}}else this.toggleTextWrapper.innerHTML=this.stringFromValue();else this.toggleTextWrapper.innerHTML=this.placeholder}stringFromValueBasic(t){var e;const s=[];let i="";if(t.forEach((t=>{this.isMultiple?this.value.includes(t.val)&&s.push(t.title):this.value===t.val&&s.push(t.title)})),void 0!==this.toggleCountText&&null!==this.toggleCountText&&s.length>=this.toggleCountTextMinItems)if("nItemsAndCount"===this.toggleCountTextMode){const t=s.slice(0,this.toggleCountTextMinItems-1),o=[t.join(this.toggleSeparators.items)],l=""+(s.length-t.length);if((null===(e=null==this?void 0:this.toggleSeparators)||void 0===e?void 0:e.betweenItemsAndCounter)&&o.push(this.toggleSeparators.betweenItemsAndCounter),this.toggleCountText)switch(this.toggleCountTextPlacement){case"postfix-no-space":o.push(`${l}${this.toggleCountText}`);break;case"prefix-no-space":o.push(`${this.toggleCountText}${l}`);break;case"prefix":o.push(`${this.toggleCountText} ${l}`);break;default:o.push(`${l} ${this.toggleCountText}`)}i=o.join(" ")}else i=`${s.length} ${this.toggleCountText}`;else i=s.join(this.toggleSeparators.items);return i}stringFromValueRemoteData(){const t=this.dropdown.querySelectorAll("[data-title-value]"),e=[];let s="";if(t.forEach((t=>{const s=t.getAttribute("data-value"),i=t.getAttribute("data-title-value");this.isMultiple?this.value.includes(s)&&e.push(i):this.value===s&&e.push(i)})),this.toggleCountText&&""!==this.toggleCountText&&e.length>=this.toggleCountTextMinItems)if("nItemsAndCount"===this.toggleCountTextMode){const t=e.slice(0,this.toggleCountTextMinItems-1);s=`${t.join(this.toggleSeparators.items)} ${this.toggleSeparators.betweenItemsAndCounter} ${e.length-t.length} ${this.toggleCountText}`}else s=`${e.length} ${this.toggleCountText}`;else s=e.join(this.toggleSeparators.items);return s}stringFromValue(){return this.apiUrl?this.stringFromValueRemoteData():this.stringFromValueBasic(this.selectOptions)}selectSingleItem(){Array.from(this.el.children).find((t=>this.value===t.value)).selected=!0;const t=Array.from(this.dropdown.children).find((t=>this.value===t.getAttribute("data-value")));t&&t.classList.add("selected")}selectMultipleItems(){Array.from(this.dropdown.children).filter((t=>this.value.includes(t.getAttribute("data-value")))).forEach((t=>t.classList.add("selected"))),Array.from(this.el.children).filter((t=>this.value.includes(t.value))).forEach((t=>t.selected=!0))}unselectMultipleItems(){Array.from(this.dropdown.children).forEach((t=>t.classList.remove("selected"))),Array.from(this.el.children).forEach((t=>t.selected=!1))}searchOptions(t){if(t.length<=this.minSearchLength){this.searchNoResult&&(this.searchNoResult.remove(),this.searchNoResult=null);return this.dropdown.querySelectorAll("[data-value]").forEach((t=>{t.classList.remove("hidden")})),!1}this.searchNoResult&&(this.searchNoResult.remove(),this.searchNoResult=null),this.searchNoResult=(0,l.htmlToElement)(this.searchNoResultTemplate),this.searchNoResult.innerText=this.searchNoResultText,(0,l.classToClassList)(this.searchNoResultClasses,this.searchNoResult);const e=this.dropdown.querySelectorAll("[data-value]");let s,i=!1;this.searchLimit&&(s=0),e.forEach((e=>{const o=e.getAttribute("data-title-value").toLocaleLowerCase();let l;if(this.isSearchDirectMatch)l=!o.includes(t.toLowerCase())||this.searchLimit&&s>=this.searchLimit;else{const e=t?t.split("").map((t=>/\w/.test(t)?`${t}[\\W_]*`:"\\W*")).join(""):"";l=!new RegExp(e,"i").test(o.trim())||this.searchLimit&&s>=this.searchLimit}l?e.classList.add("hidden"):(e.classList.remove("hidden"),i=!0,this.searchLimit&&s++)})),i||this.dropdown.append(this.searchNoResult)}eraseToggleIcon(){const t=this.toggle.querySelector("[data-icon]");t&&(t.innerHTML=null,t.classList.add("hidden"))}eraseToggleTitle(){const t=this.toggle.querySelector("[data-title]");t?t.innerHTML=this.placeholder:this.toggleTextWrapper.innerHTML=this.placeholder}toggleFn(){this.isOpened?this.close():this.open()}destroy(){this.wrapper&&this.wrapper.removeEventListener("click",this.onWrapperClickListener),this.toggle&&this.toggle.removeEventListener("click",this.onToggleClickListener),this.tagsInput&&(this.tagsInput.removeEventListener("focus",this.onTagsInputFocusListener),this.tagsInput.removeEventListener("input",this.onTagsInputInputListener),this.tagsInput.removeEventListener("input",this.onTagsInputInputSecondListener),this.tagsInput.removeEventListener("keydown",this.onTagsInputKeydownListener)),this.search&&this.search.removeEventListener("input",this.onSearchInputListener);const t=this.el.parentElement.parentElement;this.el.classList.add("hidden"),this.el.style.display="",t.prepend(this.el),t.querySelector(".hs-select").remove(),this.wrapper=null,window.$hsSelectCollection=window.$hsSelectCollection.filter((({element:t})=>t.el!==this.el))}open(){var t;const e=(null===(t=null===window||void 0===window?void 0:window.$hsSelectCollection)||void 0===t?void 0:t.find((t=>t.element.isOpened)))||null;if(e&&e.element.close(),this.animationInProcess)return!1;this.animationInProcess=!0,"window"===this.dropdownScope&&this.dropdown.classList.add("invisible"),this.dropdown.classList.remove("hidden"),"window"!==this.dropdownScope&&this.recalculateDirection(),setTimeout((()=>{var t;(null===(t=null==this?void 0:this.toggle)||void 0===t?void 0:t.ariaExpanded)&&(this.toggle.ariaExpanded="true"),this.wrapper.classList.add("active"),this.dropdown.classList.add("opened"),this.dropdown.classList.contains("w-full")&&"window"===this.dropdownScope&&this.updateDropdownWidth(),this.floatingUIInstance&&"window"===this.dropdownScope&&(this.floatingUIInstance.update(),this.dropdown.classList.remove("invisible")),this.hasSearch&&!this.preventSearchFocus&&this.search.focus(),this.animationInProcess=!1})),this.isOpened=!0}close(t=!1){var e,s,i,o;if(this.animationInProcess)return!1;this.animationInProcess=!0,(null===(e=null==this?void 0:this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.toggle.ariaExpanded="false"),this.wrapper.classList.remove("active"),this.dropdown.classList.remove("opened","bottom-full","top-full"),(null===(s=this.dropdownDirectionClasses)||void 0===s?void 0:s.bottom)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.bottom),(null===(i=this.dropdownDirectionClasses)||void 0===i?void 0:i.top)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop="",this.dropdown.style.marginBottom="",(0,l.afterTransition)(this.dropdown,(()=>{this.dropdown.classList.add("hidden"),this.hasSearch&&(this.search.value="",this.search.dispatchEvent(new Event("input",{bubbles:!0})),this.search.blur()),t&&this.toggle.focus(),this.animationInProcess=!1})),null===(o=this.dropdown.querySelector(".hs-select-option-highlighted"))||void 0===o||o.classList.remove("hs-select-option-highlighted"),this.isOpened=!1}addOption(t){let e=`${this.selectOptions.length}`;const s=t=>{const{title:s,val:i,disabled:o,selected:l,options:n}=t;!!this.selectOptions.some((t=>t.val===i))||(this.addSelectOption(s,i,o,l,n),this.buildOption(s,i,o,l,n,e),this.buildOriginalOption(s,i,null,o,l,n),l&&!this.isMultiple&&this.onSelectOption(i))};Array.isArray(t)?t.forEach((t=>{s(t)})):s(t)}removeOption(t){const e=(t,e=!1)=>{!!this.selectOptions.some((e=>e.val===t))&&(this.removeSelectOption(t,e),this.destroyOption(t),this.destroyOriginalOption(t),this.value===t&&(this.value=null,this.eraseToggleTitle(),this.eraseToggleIcon()))};Array.isArray(t)?t.forEach((t=>{e(t,this.isMultiple)})):e(t,this.isMultiple),this.setNewValue()}recalculateDirection(){var t,e,s,i;if((null==this?void 0:this.dropdownVerticalFixedPlacement)&&(this.dropdown.classList.contains("bottom-full")||this.dropdown.classList.contains("top-full")))return!1;"top"===(null==this?void 0:this.dropdownVerticalFixedPlacement)?(this.dropdown.classList.add("bottom-full"),this.dropdown.style.marginBottom=`${this.dropdownSpace}px`):"bottom"===(null==this?void 0:this.dropdownVerticalFixedPlacement)?(this.dropdown.classList.add("top-full"),this.dropdown.style.marginTop=`${this.dropdownSpace}px`):(0,l.isEnoughSpace)(this.dropdown,this.toggle||this.tagsInput,"bottom",this.dropdownSpace,this.viewport)?(this.dropdown.classList.remove("bottom-full"),(null===(t=this.dropdownDirectionClasses)||void 0===t?void 0:t.bottom)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.bottom),this.dropdown.style.marginBottom="",this.dropdown.classList.add("top-full"),(null===(e=this.dropdownDirectionClasses)||void 0===e?void 0:e.top)&&this.dropdown.classList.add(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop=`${this.dropdownSpace}px`):(this.dropdown.classList.remove("top-full"),(null===(s=this.dropdownDirectionClasses)||void 0===s?void 0:s.top)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop="",this.dropdown.classList.add("bottom-full"),(null===(i=this.dropdownDirectionClasses)||void 0===i?void 0:i.bottom)&&this.dropdown.classList.add(this.dropdownDirectionClasses.bottom),this.dropdown.style.marginBottom=`${this.dropdownSpace}px`)}static findInCollection(t){return window.$hsSelectCollection.find((e=>t instanceof r?e.element.el===t.el:"string"==typeof t?e.element.el===document.querySelector(t):e.element.el===t))||null}static getInstance(t,e){const s=window.$hsSelectCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)));return s?e?s:s.element:null}static autoInit(){window.$hsSelectCollection||(window.$hsSelectCollection=[],window.addEventListener("click",(t=>{const e=t.target;r.closeCurrentlyOpened(e)})),document.addEventListener("keydown",(t=>r.accessibility(t)))),window.$hsSelectCollection&&(window.$hsSelectCollection=window.$hsSelectCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll("[data-hs-select]:not(.--prevent-on-load-init)").forEach((t=>{if(!window.$hsSelectCollection.find((e=>{var s;return(null===(s=null==e?void 0:e.element)||void 0===s?void 0:s.el)===t}))){const e=t.getAttribute("data-hs-select"),s=e?JSON.parse(e):{};new r(t,s)}}))}static open(t){const e=r.findInCollection(t);e&&!e.element.isOpened&&e.element.open()}static close(t){const e=r.findInCollection(t);e&&e.element.isOpened&&e.element.close()}static closeCurrentlyOpened(t=null){if(!t.closest(".hs-select.active")&&!t.closest("[data-hs-select-dropdown].opened")){const t=window.$hsSelectCollection.filter((t=>t.element.isOpened))||null;t&&t.forEach((t=>{t.element.close()}))}}static accessibility(t){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e&&a.SELECT_ACCESSIBILITY_KEY_SET.includes(t.code)&&!t.metaKey)switch(t.code){case"Escape":t.preventDefault(),this.onEscape();break;case"ArrowUp":t.preventDefault(),t.stopImmediatePropagation(),this.onArrow();break;case"ArrowDown":t.preventDefault(),t.stopImmediatePropagation(),this.onArrow(!1);break;case"Tab":t.preventDefault(),t.stopImmediatePropagation(),this.onTab(t.shiftKey);break;case"Home":t.preventDefault(),t.stopImmediatePropagation(),this.onStartEnd();break;case"End":t.preventDefault(),t.stopImmediatePropagation(),this.onStartEnd(!1);break;case"Enter":t.preventDefault(),this.onEnter(t);break;case"Space":if((0,l.isFocused)(e.element.search))break;t.preventDefault(),this.onEnter(t)}}static onEscape(){const t=window.$hsSelectCollection.find((t=>t.element.isOpened));t&&t.element.close()}static onArrow(t=!0){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e){const s=e.element.dropdown;if(!s)return!1;const i=(t?Array.from(s.querySelectorAll(":scope > *:not(.hidden)")).reverse():Array.from(s.querySelectorAll(":scope > *:not(.hidden)"))).filter((t=>!t.classList.contains("disabled"))),o=s.querySelector(".hs-select-option-highlighted")||s.querySelector(".selected");o||i[0].classList.add("hs-select-option-highlighted");let l=i.findIndex((t=>t===o));l+1<i.length&&l++,i[l].focus(),o&&o.classList.remove("hs-select-option-highlighted"),i[l].classList.add("hs-select-option-highlighted")}}static onTab(t=!0){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e){const s=e.element.dropdown;if(!s)return!1;const i=(t?Array.from(s.querySelectorAll(":scope >  *:not(.hidden)")).reverse():Array.from(s.querySelectorAll(":scope >  *:not(.hidden)"))).filter((t=>!t.classList.contains("disabled"))),o=s.querySelector(".hs-select-option-highlighted")||s.querySelector(".selected");o||i[0].classList.add("hs-select-option-highlighted");let l=i.findIndex((t=>t===o));if(!(l+1<i.length))return o&&o.classList.remove("hs-select-option-highlighted"),e.element.close(),e.element.toggle.focus(),!1;l++,i[l].focus(),o&&o.classList.remove("hs-select-option-highlighted"),i[l].classList.add("hs-select-option-highlighted")}}static onStartEnd(t=!0){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e){const s=e.element.dropdown;if(!s)return!1;const i=(t?Array.from(s.querySelectorAll(":scope >  *:not(.hidden)")):Array.from(s.querySelectorAll(":scope >  *:not(.hidden)")).reverse()).filter((t=>!t.classList.contains("disabled"))),o=s.querySelector(".hs-select-option-highlighted");i.length&&(i[0].focus(),o&&o.classList.remove("hs-select-option-highlighted"),i[0].classList.add("hs-select-option-highlighted"))}}static onEnter(t){const e=t.target.previousSibling;if(window.$hsSelectCollection.find((t=>t.element.el===e))){const t=window.$hsSelectCollection.find((t=>t.element.isOpened)),s=window.$hsSelectCollection.find((t=>t.element.el===e));t.element.close(),t!==s&&s.element.open()}else{const e=window.$hsSelectCollection.find((t=>t.element.isOpened));e&&e.element.onSelectOption(t.target.dataset.value||"")}}}window.addEventListener("load",(()=>{r.autoInit()})),document.addEventListener("scroll",(()=>{if(!window.$hsSelectCollection)return!1;const t=window.$hsSelectCollection.find((t=>t.element.isOpened));t&&t.element.recalculateDirection()})),"undefined"!=typeof window&&(window.HSSelect=r),e.default=r},961:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(t,e,s){this.el=t,this.options=e,this.events=s,this.el=t,this.options=e,this.events={}}createCollection(t,e){var s;t.push({id:(null===(s=null==e?void 0:e.el)||void 0===s?void 0:s.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}}},e={};var s=function s(i){var o=e[i];if(void 0!==o)return o.exports;var l=e[i]={exports:{}};return t[i].call(l.exports,l,l.exports,s),l.exports}(442);return s})()));