!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var o=t();for(var l in o)("object"==typeof exports?exports:e)[l]=o[l]}}(self,(()=>(()=>{"use strict";var e={292:function(e,t){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0;t.stringToBoolean=e=>"true"===e;t.getClassProperty=(e,t,o="")=>(window.getComputedStyle(e).getPropertyValue(t)||o).replace(" ","");t.getClassPropertyAlt=(e,t,o="")=>{let l="";return e.classList.forEach((e=>{e.includes(t)&&(l=e)})),l.match(/:(.*)]/)?l.match(/:(.*)]/)[1]:o};const o=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=o;t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let l=o(e);"auto"!==l&&(l=parseInt(l,10),l>t&&(t=l))})),t};t.isDirectChild=(e,t)=>{const o=e.children;for(let e=0;e<o.length;e++)if(o[e]===t)return!0;return!1};t.isEnoughSpace=(e,t,o="auto",l=10,s=null)=>{const i=t.getBoundingClientRect(),r=s?s.getBoundingClientRect():null,n=window.innerHeight,c=r?i.top-r.top:i.top,a=(s?r.bottom:n)-i.bottom,h=e.clientHeight+l;return"bottom"===o?a>=h:"top"===o?c>=h:c>=h||a>=h};t.isFocused=e=>document.activeElement===e;t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement;t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],o=e.trim().slice(-1);if("{"===t&&"}"===o||"["===t&&"]"===o)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const l=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||l(e.parentElement)};t.isParentOrElementHidden=l;t.isScrollable=e=>{const t=window.getComputedStyle(e),o=t.overflowY,l=t.overflowX,s=("scroll"===o||"auto"===o)&&e.scrollHeight>e.clientHeight,i=("scroll"===l||"auto"===l)&&e.scrollWidth>e.clientWidth;return s||i};t.debounce=(e,t=200)=>{let o;return(...l)=>{clearTimeout(o),o=setTimeout((()=>{e.apply(this,l)}),t)}};t.dispatch=(e,t,o=null)=>{const l=new CustomEvent(e,{detail:{payload:o},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(l)};t.afterTransition=(e,t)=>{const o=()=>{t(),e.removeEventListener("transitionend",o,!0)},l=window.getComputedStyle(e),s=l.getPropertyValue("transition-duration");"none"!==l.getPropertyValue("transition-property")&&parseFloat(s)>0?e.addEventListener("transitionend",o,!0):t()};t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild};t.classToClassList=(e,t,o=" ",l="add")=>{e.split(o).forEach((e=>"add"===l?t.classList.add(e):t.classList.remove(e)))};const s={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=s},751:function(e,t,o){
/*
 * HSScrollspy
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var l=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=o(292),i=l(o(961));class r extends i.default{constructor(e,t={}){super(e,t),this.isScrollingDown=!1,this.lastScrollTop=0;const o=e.getAttribute("data-hs-scrollspy-options"),l=o?JSON.parse(o):{},s=Object.assign(Object.assign({},l),t);this.ignoreScrollUp=void 0!==s.ignoreScrollUp&&s.ignoreScrollUp,this.links=this.el.querySelectorAll("[href]"),this.sections=[],this.scrollableId=this.el.getAttribute("data-hs-scrollspy-scrollable-parent"),this.scrollable=this.scrollableId?document.querySelector(this.scrollableId):document,this.onLinkClickListener=[],this.init()}scrollableScroll(e){const t=this.scrollable instanceof HTMLElement?this.scrollable.scrollTop:window.scrollY;this.isScrollingDown=t>this.lastScrollTop,this.lastScrollTop=t<=0?0:t,Array.from(this.sections).forEach((t=>{if(!t.getAttribute("id"))return!1;this.update(e,t)}))}init(){this.createCollection(window.$hsScrollspyCollection,this),this.links.forEach((e=>{this.sections.push(this.scrollable.querySelector(e.getAttribute("href")))})),this.onScrollableScrollListener=e=>this.scrollableScroll(e),this.scrollable.addEventListener("scroll",this.onScrollableScrollListener),this.links.forEach((e=>{this.onLinkClickListener.push({el:e,fn:t=>this.linkClick(t,e)}),e.addEventListener("click",this.onLinkClickListener.find((t=>t.el===e)).fn)}))}determineScrollDirection(e){const t=this.el.querySelector("a.active");if(!t)return!0;const o=Array.from(this.links).indexOf(t),l=Array.from(this.links).indexOf(e);return-1===l||l>o}linkClick(e,t){e.preventDefault();const o=t.getAttribute("href");if(!o||"javascript:;"===o)return;(o?document.querySelector(o):null)&&(this.isScrollingDown=this.determineScrollDirection(t),this.scrollTo(t))}update(e,t){const o=parseInt((0,s.getClassProperty)(this.el,"--scrollspy-offset","0")),l=parseInt((0,s.getClassProperty)(t,"--scrollspy-offset"))||o,i=e.target===document?0:parseInt(String(e.target.getBoundingClientRect().top)),r=parseInt(String(t.getBoundingClientRect().top))-l-i,n=t.offsetHeight;if(this.ignoreScrollUp||this.isScrollingDown?r<=0&&r+n>0:r<=0&&r<n){this.links.forEach((e=>e.classList.remove("active")));const e=this.el.querySelector(`[href="#${t.getAttribute("id")}"]`);if(e){e.classList.add("active");const t=e.closest("[data-hs-scrollspy-group]");if(t){const e=t.querySelector("[href]");e&&e.classList.add("active")}}this.fireEvent("afterScroll",e),(0,s.dispatch)("afterScroll.hs.scrollspy",e,this.el)}}scrollTo(e){const t=e.getAttribute("href"),o=document.querySelector(t),l=parseInt((0,s.getClassProperty)(this.el,"--scrollspy-offset","0")),i=parseInt((0,s.getClassProperty)(o,"--scrollspy-offset"))||l,r=this.scrollable===document?0:this.scrollable.offsetTop,n=o.offsetTop-i-r,c=this.scrollable===document?window:this.scrollable,a=()=>{window.history.replaceState(null,null,e.getAttribute("href")),"scrollTo"in c&&c.scrollTo({top:n,left:0,behavior:"smooth"})},h=this.fireEvent("beforeScroll",this.el);(0,s.dispatch)("beforeScroll.hs.scrollspy",this.el,this.el),h instanceof Promise?h.then((()=>a())):a()}destroy(){this.el.querySelector("[href].active").classList.remove("active"),this.scrollable.removeEventListener("scroll",this.onScrollableScrollListener),this.onLinkClickListener.length&&this.onLinkClickListener.forEach((({el:e,fn:t})=>{e.removeEventListener("click",t)})),window.$hsScrollspyCollection=window.$hsScrollspyCollection.filter((({element:e})=>e.el!==this.el))}static getInstance(e,t=!1){const o=window.$hsScrollspyCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return o?t?o:o.element.el:null}static autoInit(){window.$hsScrollspyCollection||(window.$hsScrollspyCollection=[]),window.$hsScrollspyCollection&&(window.$hsScrollspyCollection=window.$hsScrollspyCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll("[data-hs-scrollspy]:not(.--prevent-on-load-init)").forEach((e=>{window.$hsScrollspyCollection.find((t=>{var o;return(null===(o=null==t?void 0:t.element)||void 0===o?void 0:o.el)===e}))||new r(e)}))}}window.addEventListener("load",(()=>{r.autoInit()})),"undefined"!=typeof window&&(window.HSScrollspy=r),t.default=r},961:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t,o){this.el=e,this.options=t,this.events=o,this.el=e,this.options=t,this.events={}}createCollection(e,t){var o;e.push({id:(null===(o=null==t?void 0:t.el)||void 0===o?void 0:o.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={};var o=function o(l){var s=t[l];if(void 0!==s)return s.exports;var i=t[l]={exports:{}};return e[l].call(i.exports,i,i.exports,o),i.exports}(751);return o})()));