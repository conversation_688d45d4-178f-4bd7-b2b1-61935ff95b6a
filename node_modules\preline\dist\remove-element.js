!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var o in n)("object"==typeof exports?exports:e)[o]=n[o]}}(self,(()=>(()=>{"use strict";var e={292:function(e,t){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0;t.stringToBoolean=e=>"true"===e;t.getClassProperty=(e,t,n="")=>(window.getComputedStyle(e).getPropertyValue(t)||n).replace(" ","");t.getClassPropertyAlt=(e,t,n="")=>{let o="";return e.classList.forEach((e=>{e.includes(t)&&(o=e)})),o.match(/:(.*)]/)?o.match(/:(.*)]/)[1]:n};const n=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=n;t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let o=n(e);"auto"!==o&&(o=parseInt(o,10),o>t&&(t=o))})),t};t.isDirectChild=(e,t)=>{const n=e.children;for(let e=0;e<n.length;e++)if(n[e]===t)return!0;return!1};t.isEnoughSpace=(e,t,n="auto",o=10,i=null)=>{const r=t.getBoundingClientRect(),s=i?i.getBoundingClientRect():null,l=window.innerHeight,a=s?r.top-s.top:r.top,c=(i?s.bottom:l)-r.bottom,d=e.clientHeight+o;return"bottom"===n?c>=d:"top"===n?a>=d:a>=d||c>=d};t.isFocused=e=>document.activeElement===e;t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement;t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],n=e.trim().slice(-1);if("{"===t&&"}"===n||"["===t&&"]"===n)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const o=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||o(e.parentElement)};t.isParentOrElementHidden=o;t.isScrollable=e=>{const t=window.getComputedStyle(e),n=t.overflowY,o=t.overflowX,i=("scroll"===n||"auto"===n)&&e.scrollHeight>e.clientHeight,r=("scroll"===o||"auto"===o)&&e.scrollWidth>e.clientWidth;return i||r};t.debounce=(e,t=200)=>{let n;return(...o)=>{clearTimeout(n),n=setTimeout((()=>{e.apply(this,o)}),t)}};t.dispatch=(e,t,n=null)=>{const o=new CustomEvent(e,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(o)};t.afterTransition=(e,t)=>{const n=()=>{t(),e.removeEventListener("transitionend",n,!0)},o=window.getComputedStyle(e),i=o.getPropertyValue("transition-duration");"none"!==o.getPropertyValue("transition-property")&&parseFloat(i)>0?e.addEventListener("transitionend",n,!0):t()};t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild};t.classToClassList=(e,t,n=" ",o="add")=>{e.split(n).forEach((e=>"add"===o?t.classList.add(e):t.classList.remove(e)))};const i={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=i},911:function(e,t,n){
/*
 * HSRemoveElement
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const i=n(292),r=o(n(961));class s extends r.default{constructor(e,t){super(e,t);const n=e.getAttribute("data-hs-remove-element-options"),o=n?JSON.parse(n):{},i=Object.assign(Object.assign({},o),t);this.removeTargetId=this.el.getAttribute("data-hs-remove-element"),this.removeTarget=document.querySelector(this.removeTargetId),this.removeTargetAnimationClass=(null==i?void 0:i.removeTargetAnimationClass)||"hs-removing",this.removeTarget&&this.init()}elementClick(){this.remove()}init(){this.createCollection(window.$hsRemoveElementCollection,this),this.onElementClickListener=()=>this.elementClick(),this.el.addEventListener("click",this.onElementClickListener)}remove(){if(!this.removeTarget)return!1;this.removeTarget.classList.add(this.removeTargetAnimationClass),(0,i.afterTransition)(this.removeTarget,(()=>setTimeout((()=>this.removeTarget.remove()))))}destroy(){this.removeTarget.classList.remove(this.removeTargetAnimationClass),this.el.removeEventListener("click",this.onElementClickListener),window.$hsRemoveElementCollection=window.$hsRemoveElementCollection.filter((({element:e})=>e.el!==this.el))}static getInstance(e,t){const n=window.$hsRemoveElementCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)||t.element.el===("string"==typeof e?document.querySelector(e):e)));return n?t?n:n.element.el:null}static autoInit(){window.$hsRemoveElementCollection||(window.$hsRemoveElementCollection=[]),window.$hsRemoveElementCollection&&(window.$hsRemoveElementCollection=window.$hsRemoveElementCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll("[data-hs-remove-element]:not(.--prevent-on-load-init)").forEach((e=>{window.$hsRemoveElementCollection.find((t=>{var n;return(null===(n=null==t?void 0:t.element)||void 0===n?void 0:n.el)===e}))||new s(e)}))}}window.addEventListener("load",(()=>{s.autoInit()})),"undefined"!=typeof window&&(window.HSRemoveElement=s),t.default=s},961:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t,n){this.el=e,this.options=t,this.events=n,this.el=e,this.options=t,this.events={}}createCollection(e,t){var n;e.push({id:(null===(n=null==t?void 0:t.el)||void 0===n?void 0:n.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={};var n=function n(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={exports:{}};return e[o].call(r.exports,r,r.exports,n),r.exports}(911);return n})()));