var e={615:(e,t,i)=>{i.d(t,{A:()=>n});class n{constructor(e,t,i){this.el=e,this.options=t,this.events=i,this.el=e,this.options=t,this.events={}}createCollection(e,t){var i;e.push({id:(null===(i=null==t?void 0:t.el)||void 0===i?void 0:i.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}},926:(e,t,i)=>{i.d(t,{JD:()=>n});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const n=(e,t,i=null)=>{const n=new CustomEvent(e,{detail:{payload:i},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(n)}}},t={};function i(n){var s=t[n];if(void 0!==s)return s.exports;var l=t[n]={exports:{}};return e[n](l,l.exports,i),l.exports}i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};i.d(n,{A:()=>r});var s=i(926),l=i(615);
/*
 * HSTreeView
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
class o extends l.A{constructor(e,t,i){super(e,t,i),this.items=[];const n=e.getAttribute("data-hs-tree-view"),s=n?JSON.parse(n):{},l=Object.assign(Object.assign({},s),t);this.controlBy=(null==l?void 0:l.controlBy)||"button",this.autoSelectChildren=(null==l?void 0:l.autoSelectChildren)||!1,this.isIndeterminate=(null==l?void 0:l.isIndeterminate)||!0,this.onElementClickListener=[],this.onControlChangeListener=[],this.init()}elementClick(e,t,i){if(e.stopPropagation(),t.classList.contains("disabled"))return!1;e.metaKey||e.shiftKey||this.unselectItem(i),this.selectItem(t,i),this.fireEvent("click",{el:t,data:i}),(0,s.JD)("click.hs.treeView",this.el,{el:t,data:i})}controlChange(e,t){this.autoSelectChildren?(this.selectItem(e,t),t.isDir&&this.selectChildren(e,t),this.toggleParent(e)):this.selectItem(e,t)}init(){this.createCollection(window.$hsTreeViewCollection,this),o.group+=1,this.initItems()}initItems(){this.el.querySelectorAll("[data-hs-tree-view-item]").forEach(((e,t)=>{var i,n;const s=JSON.parse(e.getAttribute("data-hs-tree-view-item"));e.id||(e.id=`tree-view-item-${o.group}-${t}`);const l=Object.assign(Object.assign({},s),{id:null!==(i=s.id)&&void 0!==i?i:e.id,path:this.getPath(e),isSelected:null!==(n=s.isSelected)&&void 0!==n&&n});this.items.push(l),"checkbox"===this.controlBy?this.controlByCheckbox(e,l):this.controlByButton(e,l)}))}controlByButton(e,t){this.onElementClickListener.push({el:e,fn:i=>this.elementClick(i,e,t)}),e.addEventListener("click",this.onElementClickListener.find((t=>t.el===e)).fn)}controlByCheckbox(e,t){const i=e.querySelector(`input[value="${t.value}"]`);i&&(this.onControlChangeListener.push({el:i,fn:()=>this.controlChange(e,t)}),i.addEventListener("change",this.onControlChangeListener.find((e=>e.el===i)).fn))}getItem(e){return this.items.find((t=>t.id===e))}getPath(e){var t;const i=[];let n=e.closest("[data-hs-tree-view-item]");for(;n;){const e=JSON.parse(n.getAttribute("data-hs-tree-view-item"));i.push(e.value),n=null===(t=n.parentElement)||void 0===t?void 0:t.closest("[data-hs-tree-view-item]")}return i.reverse().join("/")}unselectItem(e=null){let t=this.getSelectedItems();e&&(t=t.filter((t=>t.id!==e.id))),t.length&&t.forEach((e=>{document.querySelector(`#${e.id}`).classList.remove("selected"),this.changeItemProp(e.id,"isSelected",!1)}))}selectItem(e,t){t.isSelected?(e.classList.remove("selected"),this.changeItemProp(t.id,"isSelected",!1)):(e.classList.add("selected"),this.changeItemProp(t.id,"isSelected",!0))}selectChildren(e,t){const i=e.querySelectorAll("[data-hs-tree-view-item]");Array.from(i).filter((e=>!e.classList.contains("disabled"))).forEach((e=>{const i=e.id?this.getItem(e.id):null;if(!i)return!1;t.isSelected?(e.classList.add("selected"),this.changeItemProp(i.id,"isSelected",!0)):(e.classList.remove("selected"),this.changeItemProp(i.id,"isSelected",!1));const n=this.getItem(e.id),s=e.querySelector(`input[value="${n.value}"]`);this.isIndeterminate&&(s.indeterminate=!1),n.isSelected?s.checked=!0:s.checked=!1}))}toggleParent(e){var t,i;let n=null===(t=e.parentElement)||void 0===t?void 0:t.closest("[data-hs-tree-view-item]");for(;n;){const e=n.querySelectorAll("[data-hs-tree-view-item]:not(.disabled)"),t=JSON.parse(n.getAttribute("data-hs-tree-view-item")),s=n.querySelector(`input[value="${t.value}"]`);let l=!1,o=0;e.forEach((e=>{const t=this.getItem(e.id);t.isSelected&&(o+=1),t.isSelected||(l=!0)})),l?(n.classList.remove("selected"),this.changeItemProp(n.id,"isSelected",!1),s.checked=!1):(n.classList.add("selected"),this.changeItemProp(n.id,"isSelected",!0),s.checked=!0),this.isIndeterminate&&(o>0&&o<e.length?s.indeterminate=!0:s.indeterminate=!1),n=null===(i=n.parentElement)||void 0===i?void 0:i.closest("[data-hs-tree-view-item]")}}update(){this.items.map((e=>{const t=document.querySelector(`#${e.id}`);return e.path!==this.getPath(t)&&(e.path=this.getPath(t)),e}))}getSelectedItems(){return this.items.filter((e=>e.isSelected))}changeItemProp(e,t,i){this.items.map((n=>(n.id===e&&(n[t]=i),n)))}destroy(){this.onElementClickListener.forEach((({el:e,fn:t})=>{e.removeEventListener("click",t)})),this.onControlChangeListener.length&&this.onElementClickListener.forEach((({el:e,fn:t})=>{e.removeEventListener("change",t)})),this.unselectItem(),this.items=[],window.$hsTreeViewCollection=window.$hsTreeViewCollection.filter((({element:e})=>e.el!==this.el)),o.group-=1}static findInCollection(e){return window.$hsTreeViewCollection.find((t=>e instanceof o?t.element.el===e.el:"string"==typeof e?t.element.el===document.querySelector(e):t.element.el===e))||null}static getInstance(e,t){const i=window.$hsTreeViewCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return i?t?i:i.element.el:null}static autoInit(){window.$hsTreeViewCollection||(window.$hsTreeViewCollection=[]),window.$hsTreeViewCollection&&(window.$hsTreeViewCollection=window.$hsTreeViewCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll("[data-hs-tree-view]:not(.--prevent-on-load-init)").forEach((e=>{window.$hsTreeViewCollection.find((t=>{var i;return(null===(i=null==t?void 0:t.element)||void 0===i?void 0:i.el)===e}))||new o(e)}))}static on(e,t,i){const n=o.findInCollection(t);n&&(n.element.events[e]=i)}}o.group=0,window.addEventListener("load",(()=>{o.autoInit()})),"undefined"!=typeof window&&(window.HSTreeView=o);const r=o;var c=n.A;export{c as default};