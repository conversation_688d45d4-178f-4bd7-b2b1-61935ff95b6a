!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n=e();for(var o in n)("object"==typeof exports?exports:t)[o]=n[o]}}(self,(()=>(()=>{"use strict";var t={166:function(t,e,n){
/*
 * HSTabs
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const r=n(292),i=o(n(961)),s=n(223);class l extends i.default{constructor(t,e,n){var o,r;super(t,e,n);const i=t.getAttribute("data-hs-tabs"),l=i?JSON.parse(i):{},a=Object.assign(Object.assign({},l),e);this.eventType=null!==(o=a.eventType)&&void 0!==o?o:"click",this.preventNavigationResolution="number"==typeof a.preventNavigationResolution?a.preventNavigationResolution:s.BREAKPOINTS[a.preventNavigationResolution]||null,this.toggles=this.el.querySelectorAll("[data-hs-tab]"),this.extraToggleId=this.el.getAttribute("data-hs-tab-select"),this.extraToggle=this.extraToggleId?document.querySelector(this.extraToggleId):null,this.current=Array.from(this.toggles).find((t=>t.classList.contains("active"))),this.currentContentId=(null===(r=this.current)||void 0===r?void 0:r.getAttribute("data-hs-tab"))||null,this.currentContent=this.currentContentId?document.querySelector(this.currentContentId):null,this.prev=null,this.prevContentId=null,this.prevContent=null,this.onToggleHandler=[],this.init()}toggle(t){this.open(t)}extraToggleChange(t){this.change(t)}init(){this.createCollection(window.$hsTabsCollection,this),this.toggles.forEach((t=>{const e=e=>{"click"===this.eventType&&this.preventNavigationResolution&&document.body.clientWidth<=+this.preventNavigationResolution&&e.preventDefault(),this.toggle(t)},n=t=>{this.preventNavigationResolution&&document.body.clientWidth<=+this.preventNavigationResolution&&t.preventDefault()};this.onToggleHandler.push({el:t,fn:e,preventClickFn:n}),"click"===this.eventType?t.addEventListener("click",e):(t.addEventListener("mouseenter",e),t.addEventListener("click",n))})),this.extraToggle&&(this.onExtraToggleChangeListener=t=>this.extraToggleChange(t),this.extraToggle.addEventListener("change",this.onExtraToggleChangeListener))}open(t){var e,n,o,i,s;this.prev=this.current,this.prevContentId=this.currentContentId,this.prevContent=this.currentContent,this.current=t,this.currentContentId=t.getAttribute("data-hs-tab"),this.currentContent=this.currentContentId?document.querySelector(this.currentContentId):null,(null===(e=null==this?void 0:this.prev)||void 0===e?void 0:e.ariaSelected)&&(this.prev.ariaSelected="false"),null===(n=this.prev)||void 0===n||n.classList.remove("active"),null===(o=this.prevContent)||void 0===o||o.classList.add("hidden"),(null===(i=null==this?void 0:this.current)||void 0===i?void 0:i.ariaSelected)&&(this.current.ariaSelected="true"),this.current.classList.add("active"),null===(s=this.currentContent)||void 0===s||s.classList.remove("hidden"),this.fireEvent("change",{el:t,prev:this.prevContentId,current:this.currentContentId,tabsId:this.el.id}),(0,r.dispatch)("change.hs.tab",t,{el:t,prev:this.prevContentId,current:this.currentContentId,tabsId:this.el.id})}change(t){const e=document.querySelector(`[data-hs-tab="${t.target.value}"]`);e&&("hover"===this.eventType?e.dispatchEvent(new Event("mouseenter")):e.click())}destroy(){this.toggles.forEach((t=>{var e;const n=null===(e=this.onToggleHandler)||void 0===e?void 0:e.find((({el:e})=>e===t));n&&("click"===this.eventType?t.removeEventListener("click",n.fn):(t.removeEventListener("mouseenter",n.fn),t.removeEventListener("click",n.preventClickFn)))})),this.onToggleHandler=[],this.extraToggle&&this.extraToggle.removeEventListener("change",this.onExtraToggleChangeListener),window.$hsTabsCollection=window.$hsTabsCollection.filter((({element:t})=>t.el!==this.el))}static getInstance(t,e){const n=window.$hsTabsCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)));return n?e?n:n.element:null}static autoInit(){window.$hsTabsCollection||(window.$hsTabsCollection=[],document.addEventListener("keydown",(t=>l.accessibility(t)))),window.$hsTabsCollection&&(window.$hsTabsCollection=window.$hsTabsCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll('[role="tablist"]:not(select):not(.--prevent-on-load-init)').forEach((t=>{window.$hsTabsCollection.find((e=>{var n;return(null===(n=null==e?void 0:e.element)||void 0===n?void 0:n.el)===t}))||new l(t)}))}static open(t){const e=window.$hsTabsCollection.find((e=>Array.from(e.element.toggles).includes("string"==typeof t?document.querySelector(t):t))),n=e?Array.from(e.element.toggles).find((e=>e===("string"==typeof t?document.querySelector(t):t))):null;n&&!n.classList.contains("active")&&e.element.open(n)}static accessibility(t){var e;const n=document.querySelector("[data-hs-tab]:focus");if(n&&s.TABS_ACCESSIBILITY_KEY_SET.includes(t.code)&&!t.metaKey){const o=null===(e=n.closest('[role="tablist"]'))||void 0===e?void 0:e.getAttribute("data-hs-tabs-vertical");switch(t.preventDefault(),t.code){case"true"===o?"ArrowUp":"ArrowLeft":this.onArrow();break;case"true"===o?"ArrowDown":"ArrowRight":this.onArrow(!1);break;case"Home":this.onStartEnd();break;case"End":this.onStartEnd(!1)}}}static onArrow(t=!0){var e;const n=null===(e=document.querySelector("[data-hs-tab]:focus"))||void 0===e?void 0:e.closest('[role="tablist"]');if(!n)return;const o=window.$hsTabsCollection.find((t=>t.element.el===n));if(o){const e=t?Array.from(o.element.toggles).reverse():Array.from(o.element.toggles),n=e.find((t=>document.activeElement===t));let r=e.findIndex((t=>t===n));r=r+1<e.length?r+1:0,e[r].focus(),e[r].click()}}static onStartEnd(t=!0){var e;const n=null===(e=document.querySelector("[data-hs-tab]:focus"))||void 0===e?void 0:e.closest('[role="tablist"]');if(!n)return;const o=window.$hsTabsCollection.find((t=>t.element.el===n));if(o){const e=t?Array.from(o.element.toggles):Array.from(o.element.toggles).reverse();e.length&&(e[0].focus(),e[0].click())}}static on(t,e,n){const o=window.$hsTabsCollection.find((t=>Array.from(t.element.toggles).includes("string"==typeof e?document.querySelector(e):e)));o&&(o.element.events[t]=n)}}window.addEventListener("load",(()=>{l.autoInit()})),"undefined"!=typeof window&&(window.HSTabs=l),e.default=l},223:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BREAKPOINTS=e.COMBO_BOX_ACCESSIBILITY_KEY_SET=e.SELECT_ACCESSIBILITY_KEY_SET=e.TABS_ACCESSIBILITY_KEY_SET=e.OVERLAY_ACCESSIBILITY_KEY_SET=e.DROPDOWN_ACCESSIBILITY_KEY_SET=e.POSITIONS=void 0,e.POSITIONS={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"},e.DROPDOWN_ACCESSIBILITY_KEY_SET=["Escape","ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Home","End","Enter"],e.OVERLAY_ACCESSIBILITY_KEY_SET=["Escape","Tab"],e.TABS_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End"],e.SELECT_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter","Space","Tab"],e.COMBO_BOX_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter"],e.BREAKPOINTS={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},292:function(t,e){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(e,"__esModule",{value:!0}),e.menuSearchHistory=e.classToClassList=e.htmlToElement=e.afterTransition=e.dispatch=e.debounce=e.isScrollable=e.isParentOrElementHidden=e.isJson=e.isIpadOS=e.isIOS=e.isDirectChild=e.isFormElement=e.isFocused=e.isEnoughSpace=e.getHighestZIndex=e.getZIndex=e.getClassPropertyAlt=e.getClassProperty=e.stringToBoolean=void 0;e.stringToBoolean=t=>"true"===t;e.getClassProperty=(t,e,n="")=>(window.getComputedStyle(t).getPropertyValue(e)||n).replace(" ","");e.getClassPropertyAlt=(t,e,n="")=>{let o="";return t.classList.forEach((t=>{t.includes(e)&&(o=t)})),o.match(/:(.*)]/)?o.match(/:(.*)]/)[1]:n};const n=t=>window.getComputedStyle(t).getPropertyValue("z-index");e.getZIndex=n;e.getHighestZIndex=t=>{let e=Number.NEGATIVE_INFINITY;return t.forEach((t=>{let o=n(t);"auto"!==o&&(o=parseInt(o,10),o>e&&(e=o))})),e};e.isDirectChild=(t,e)=>{const n=t.children;for(let t=0;t<n.length;t++)if(n[t]===e)return!0;return!1};e.isEnoughSpace=(t,e,n="auto",o=10,r=null)=>{const i=e.getBoundingClientRect(),s=r?r.getBoundingClientRect():null,l=window.innerHeight,a=s?i.top-s.top:i.top,c=(r?s.bottom:l)-i.bottom,d=t.clientHeight+o;return"bottom"===n?c>=d:"top"===n?a>=d:a>=d||c>=d};e.isFocused=t=>document.activeElement===t;e.isFormElement=t=>t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement;e.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isJson=t=>{if("string"!=typeof t)return!1;const e=t.trim()[0],n=t.trim().slice(-1);if("{"===e&&"}"===n||"["===e&&"]"===n)try{return JSON.parse(t),!0}catch(t){return!1}return!1};const o=t=>{if(!t)return!1;return"none"===window.getComputedStyle(t).display||o(t.parentElement)};e.isParentOrElementHidden=o;e.isScrollable=t=>{const e=window.getComputedStyle(t),n=e.overflowY,o=e.overflowX,r=("scroll"===n||"auto"===n)&&t.scrollHeight>t.clientHeight,i=("scroll"===o||"auto"===o)&&t.scrollWidth>t.clientWidth;return r||i};e.debounce=(t,e=200)=>{let n;return(...o)=>{clearTimeout(n),n=setTimeout((()=>{t.apply(this,o)}),e)}};e.dispatch=(t,e,n=null)=>{const o=new CustomEvent(t,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(o)};e.afterTransition=(t,e)=>{const n=()=>{e(),t.removeEventListener("transitionend",n,!0)},o=window.getComputedStyle(t),r=o.getPropertyValue("transition-duration");"none"!==o.getPropertyValue("transition-property")&&parseFloat(r)>0?t.addEventListener("transitionend",n,!0):e()};e.htmlToElement=t=>{const e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild};e.classToClassList=(t,e,n=" ",o="add")=>{t.split(n).forEach((t=>"add"===o?e.classList.add(t):e.classList.remove(t)))};const r={historyIndex:-1,addHistory(t){this.historyIndex=t},existsInHistory(t){return t>this.historyIndex},clearHistory(){this.historyIndex=-1}};e.menuSearchHistory=r},961:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(t,e,n){this.el=t,this.options=e,this.events=n,this.el=t,this.options=e,this.events={}}createCollection(t,e){var n;t.push({id:(null===(n=null==e?void 0:e.el)||void 0===n?void 0:n.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}}},e={};var n=function n(o){var r=e[o];if(void 0!==r)return r.exports;var i=e[o]={exports:{}};return t[o].call(i.exports,i,i.exports,n),i.exports}(166);return n})()));