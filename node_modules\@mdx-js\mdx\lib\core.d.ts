/**
 * Create a processor to compile markdown or MDX to JavaScript.
 *
 * > **Note**: `format: 'detect'` is not allowed in `ProcessorOptions`.
 *
 * @param {Readonly<ProcessorOptions> | null | undefined} [options]
 *   Configuration (optional).
 * @return {Processor<Root, Program, Program, Program, string>}
 *   Processor.
 */
export function createProcessor(options?: Readonly<ProcessorOptions> | null | undefined): Processor<Root, Program, Program, Program, string>;
/**
 * Configuration for `createProcessor`.
 */
export type ProcessorOptions = {
    /**
     * Add a source map (object form) as the `map` field on the resulting file
     * (optional).
     */
    SourceMapGenerator?: typeof SourceMapGenerator | null | undefined;
    /**
     * Use this URL as `import.meta.url` and resolve `import` and `export … from`
     * relative to it (optional, example: `import.meta.url`).
     */
    baseUrl?: URL | string | null | undefined;
    /**
     * Whether to add extra info to error messages in generated code and use the
     * development automatic JSX runtime (`Fragment` and `jsxDEV` from
     * `/jsx-dev-runtime`) (default: `false`);
     * when using the webpack loader (`@mdx-js/loader`) or the Rollup integration
     * (`@mdx-js/rollup`) through Vite, this is automatically inferred from how
     * you configure those tools.
     */
    development?: boolean | null | undefined;
    /**
     * Casing to use for attribute names (default: `'react'`);
     * HTML casing is for example `class`, `stroke-linecap`, `xml:lang`;
     * React casing is for example `className`, `strokeLinecap`, `xmlLang`;
     * for JSX components written in MDX, the author has to be aware of which
     * framework they use and write code accordingly;
     * for AST nodes generated by this project, this option configures it
     */
    elementAttributeNameCase?: RehypeRecmaOptions["elementAttributeNameCase"];
    /**
     * format of the file (default: `'mdx'`);
     * `'md'` means treat as markdown and `'mdx'` means treat as MDX.
     */
    format?: "md" | "mdx" | null | undefined;
    /**
     * Whether to keep JSX (default: `false`);
     * the default is to compile JSX away so that the resulting file is
     * immediately runnable.
     */
    jsx?: boolean | null | undefined;
    /**
     * Place to import automatic JSX runtimes from (default: `'react'`);
     * when in the `automatic` runtime, this is used to define an import for
     * `Fragment`, `jsx`, `jsxDEV`, and `jsxs`.
     */
    jsxImportSource?: string | null | undefined;
    /**
     * JSX runtime to use (default: `'automatic'`);
     * the automatic runtime compiles to `import _jsx from
     * '$importSource/jsx-runtime'\n_jsx('p')`;
     * the classic runtime compiles to calls such as `h('p')`.
     *
     * > 👉 **Note**: support for the classic runtime is deprecated and will
     * > likely be removed in the next major version.
     */
    jsxRuntime?: "automatic" | "classic" | null | undefined;
    /**
     * List of markdown extensions, with dot (default: `['.md', '.markdown', …]`);
     * affects integrations.
     */
    mdExtensions?: ReadonlyArray<string> | null | undefined;
    /**
     * List of MDX extensions, with dot (default: `['.mdx']`);
     * affects integrations.
     */
    mdxExtensions?: ReadonlyArray<string> | null | undefined;
    /**
     * Output format to generate (default: `'program'`);
     * in most cases `'program'` should be used, it results in a whole program;
     * internally `evaluate` uses `'function-body'` to compile to
     * code that can be passed to `run`;
     * in some cases, you might want what `evaluate` does in separate steps, such
     * as when compiling on the server and running on the client.
     */
    outputFormat?: "function-body" | "program" | null | undefined;
    /**
     * Pragma for JSX, used in the classic runtime as an identifier for function
     * calls: `<x />` to `React.createElement('x')` (default:
     * `'React.createElement'`);
     * when changing this, you should also define `pragmaFrag` and
     * `pragmaImportSource` too.
     *
     * > 👉 **Note**: support for the classic runtime is deprecated and will
     * > likely be removed in the next major version.
     */
    pragma?: string | null | undefined;
    /**
     * Pragma for fragment symbol, used in the classic runtime as an identifier
     * for unnamed calls: `<>` to `React.createElement(React.Fragment)` (default:
     * `'React.Fragment'`);
     * when changing this, you should also define `pragma` and
     * `pragmaImportSource` too.
     *
     * > 👉 **Note**: support for the classic runtime is deprecated and will
     * > likely be removed in the next major version.
     */
    pragmaFrag?: string | null | undefined;
    /**
     * Where to import the identifier of `pragma` from, used in the classic
     * runtime (default: `'react'`);
     * to illustrate, when `pragma` is `'a.b'` and `pragmaImportSource` is `'c'`
     * the following will be generated: `import a from 'c'` and things such as
     * `a.b('h1', {})`.
     * when changing this, you should also define `pragma` and `pragmaFrag` too.
     *
     * > 👉 **Note**: support for the classic runtime is deprecated and will
     * > likely be removed in the next major version.
     */
    pragmaImportSource?: string | null | undefined;
    /**
     * Place to import a provider from (optional, example: `'@mdx-js/react'`);
     * normally it’s used for runtimes that support context (React, Preact), but
     * it can be used to inject components into the compiled code;
     * the module must export and identifier `useMDXComponents` which is called
     * without arguments to get an object of components (`MDXComponents` from
     * `mdx/types.js`).
     */
    providerImportSource?: string | null | undefined;
    /**
     * List of recma plugins (optional);
     * this is a new ecosystem, currently in beta, to transform esast trees
     * (JavaScript)
     */
    recmaPlugins?: PluggableList | null | undefined;
    /**
     * List of remark plugins (optional).
     */
    remarkPlugins?: PluggableList | null | undefined;
    /**
     * List of rehype plugins (optional).
     */
    rehypePlugins?: PluggableList | null | undefined;
    /**
     * Options to pass through to `remark-rehype` (optional);
     * the option `allowDangerousHtml` will always be set to `true` and the MDX
     * nodes (see `nodeTypes`) are passed through;
     * In particular, you might want to pass configuration for footnotes if your
     * content is not in English.
     */
    remarkRehypeOptions?: Readonly<RemarkRehypeOptions> | null | undefined;
    /**
     * Casing to use for property names in `style` objects (default: `'dom'`);
     * CSS casing is for example `background-color` and `-webkit-line-clamp`;
     * DOM casing is for example `backgroundColor` and `WebkitLineClamp`;
     * for JSX components written in MDX, the author has to be aware of which
     * framework they use and write code accordingly;
     * for AST nodes generated by this project, this option configures it
     */
    stylePropertyNameCase?: RehypeRecmaOptions["stylePropertyNameCase"];
    /**
     * Turn obsolete `align` properties on `td` and `th` into CSS `style`
     * properties (default: `true`).
     */
    tableCellAlignToStyle?: boolean | null | undefined;
};
import type { Root } from 'mdast';
import type { Program } from 'estree-jsx';
import type { Processor } from 'unified';
import type { SourceMapGenerator } from 'source-map';
import type { Options as RehypeRecmaOptions } from 'rehype-recma';
import type { PluggableList } from 'unified';
import type { Options as RemarkRehypeOptions } from 'remark-rehype';
//# sourceMappingURL=core.d.ts.map