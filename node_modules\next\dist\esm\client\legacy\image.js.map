{"version": 3, "sources": ["../../../src/client/legacy/image.tsx"], "names": ["React", "useRef", "useEffect", "useCallback", "useContext", "useMemo", "useState", "Head", "imageConfigDefault", "VALID_LOADERS", "useIntersection", "ImageConfigContext", "warnOnce", "normalizePathTrailingSlash", "normalizeSrc", "src", "slice", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "loadedImageURLs", "Set", "allImgs", "Map", "perfObserver", "emptyDataURL", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "VALID_LOADING_VALUES", "undefined", "imgixLoader", "config", "width", "quality", "url", "URL", "path", "params", "searchParams", "set", "getAll", "join", "get", "toString", "href", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramsString", "customLoader", "Error", "defaultLoader", "NODE_ENV", "<PERSON><PERSON><PERSON><PERSON>", "push", "length", "JSON", "stringify", "startsWith", "localPatterns", "NEXT_RUNTIME", "hasLocalMatch", "require", "domains", "remotePatterns", "parsedSrc", "err", "console", "error", "hasRemoteMatch", "hostname", "endsWith", "dangerouslyAllowSVG", "encodeURIComponent", "loaders", "VALID_LAYOUT_VALUES", "isStaticRequire", "default", "isStaticImageData", "isStaticImport", "getWidths", "layout", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "parseInt", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "map", "w", "find", "p", "generateImgAttrs", "unoptimized", "loader", "srcSet", "last", "i", "getInt", "x", "defaultImageLoader", "loaderProps", "loader<PERSON>ey", "load", "handleLoading", "img", "placeholder", "onLoadingCompleteRef", "setBlurComplete", "decode", "Promise", "resolve", "catch", "then", "parentNode", "add", "current", "naturalWidth", "naturalHeight", "parentElement", "parent", "getComputedStyle", "position", "display", "ImageElement", "imgAttributes", "heightInt", "widthInt", "qualityInt", "className", "imgStyle", "blurStyle", "isLazy", "loading", "srcString", "setIntersection", "onLoad", "onError", "isVisible", "noscriptSizes", "rest", "decoding", "data-nimg", "style", "ref", "complete", "event", "currentTarget", "noscript", "Image", "priority", "lazyRoot", "lazyBoundary", "height", "objectFit", "objectPosition", "onLoadingComplete", "blurDataURL", "all", "configContext", "c", "imageSizes", "sort", "a", "b", "customImageLoader", "obj", "_", "opts", "staticSrc", "staticImageData", "has", "blurComplete", "isIntersected", "resetIntersected", "rootRef", "rootMargin", "disabled", "wrapperStyle", "boxSizing", "overflow", "background", "opacity", "border", "margin", "padding", "sizerStyle", "hasSizer", "sizerSvgUrl", "layoutStyle", "top", "left", "bottom", "right", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "includes", "String", "isNaN", "VALID_BLUR_EXT", "urlStr", "pathname", "search", "overwrittenStyles", "Object", "keys", "key", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "observe", "type", "buffered", "assign", "backgroundSize", "backgroundPosition", "backgroundImage", "quotient", "paddingTop", "fullUrl", "e", "location", "linkProps", "imageSrcSet", "crossOrigin", "referrerPolicy", "useLayoutEffect", "previousImageSrc", "imgElementArgs", "span", "alt", "aria-hidden", "link", "rel", "as"], "mappings": "AAAA;;AAEA,OAAOA,SACLC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,QACH,QAAO;AACd,OAAOC,UAAU,wBAAuB;AACxC,SACEC,kBAAkB,EAClBC,aAAa,QACR,gCAA+B;AAKtC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,kBAAkB,QAAQ,uDAAsD;AACzF,SAASC,QAAQ,QAAQ,mCAAkC;AAC3D,SAASC,0BAA0B,QAAQ,8BAA6B;AAExE,SAASC,aAAaC,GAAW;IAC/B,OAAOA,GAAG,CAAC,EAAE,KAAK,MAAMA,IAAIC,KAAK,CAAC,KAAKD;AACzC;AAEA,MAAME,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAC/C,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAIpB,IAAIC;AACJ,MAAMC,eACJ;AAEF,IAAI,OAAOC,WAAW,aAAa;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAEA,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAqBzD,SAASC,YAAY,KAKQ;IALR,IAAA,EACnBC,MAAM,EACNlB,GAAG,EACHmB,KAAK,EACLC,OAAO,EACoB,GALR;IAMnB,qEAAqE;IACrE,MAAMC,MAAM,IAAIC,IAAI,AAAC,KAAEJ,OAAOK,IAAI,GAAGxB,aAAaC;IAClD,MAAMwB,SAASH,IAAII,YAAY;IAE/B,oEAAoE;IACpED,OAAOE,GAAG,CAAC,QAAQF,OAAOG,MAAM,CAAC,QAAQC,IAAI,CAAC,QAAQ;IACtDJ,OAAOE,GAAG,CAAC,OAAOF,OAAOK,GAAG,CAAC,UAAU;IACvCL,OAAOE,GAAG,CAAC,KAAKF,OAAOK,GAAG,CAAC,QAAQV,MAAMW,QAAQ;IAEjD,IAAIV,SAAS;QACXI,OAAOE,GAAG,CAAC,KAAKN,QAAQU,QAAQ;IAClC;IAEA,OAAOT,IAAIU,IAAI;AACjB;AAEA,SAASC,aAAa,KAIO;IAJP,IAAA,EACpBd,MAAM,EACNlB,GAAG,EACHmB,KAAK,EACsB,GAJP;IAKpB,OAAO,AAAC,KAAED,OAAOK,IAAI,GAAGxB,aAAaC,OAAK,cAAWmB;AACvD;AAEA,SAASc,iBAAiB,KAKG;IALH,IAAA,EACxBf,MAAM,EACNlB,GAAG,EACHmB,KAAK,EACLC,OAAO,EACoB,GALH;IAMxB,sFAAsF;IACtF,MAAMI,SAAS;QAAC;QAAU;QAAW,OAAOL;QAAO,OAAQC,CAAAA,WAAW,MAAK;KAAG;IAC9E,MAAMc,eAAeV,OAAOI,IAAI,CAAC,OAAO;IACxC,OAAO,AAAC,KAAEV,OAAOK,IAAI,GAAGW,eAAenC,aAAaC;AACtD;AAEA,SAASmC,aAAa,KAAyB;IAAzB,IAAA,EAAEnC,GAAG,EAAoB,GAAzB;IACpB,MAAM,IAAIoC,MACR,AAAC,qBAAkBpC,MAAI,gCACpB;AAEP;AAEA,SAASqC,cAAc,KAKM;IALN,IAAA,EACrBnB,MAAM,EACNlB,GAAG,EACHmB,KAAK,EACLC,OAAO,EACoB,GALN;IAMrB,IAAIjB,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,MAAMC,gBAAgB,EAAE;QAExB,yDAAyD;QACzD,IAAI,CAACvC,KAAKuC,cAAcC,IAAI,CAAC;QAC7B,IAAI,CAACrB,OAAOoB,cAAcC,IAAI,CAAC;QAE/B,IAAID,cAAcE,MAAM,GAAG,GAAG;YAC5B,MAAM,IAAIL,MACR,AAAC,sCAAmCG,cAAcX,IAAI,CACpD,QACA,gGAA+Fc,KAAKC,SAAS,CAC7G;gBAAE3C;gBAAKmB;gBAAOC;YAAQ;QAG5B;QAEA,IAAIpB,IAAI4C,UAAU,CAAC,OAAO;YACxB,MAAM,IAAIR,MACR,AAAC,0BAAuBpC,MAAI;QAEhC;QAEA,IAAIA,IAAI4C,UAAU,CAAC,QAAQ1B,OAAO2B,aAAa,EAAE;YAC/C,IACE1C,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJC,aAAa,EACd,GAAGC,QAAQ;gBACZ,IAAI,CAACD,cAAc7B,OAAO2B,aAAa,EAAE7C,MAAM;oBAC7C,MAAM,IAAIoC,MACR,AAAC,uBAAoBpC,MAAI,kGACtB;gBAEP;YACF;QACF;QAEA,IAAI,CAACA,IAAI4C,UAAU,CAAC,QAAS1B,CAAAA,OAAO+B,OAAO,IAAI/B,OAAOgC,cAAc,AAAD,GAAI;YACrE,IAAIC;YACJ,IAAI;gBACFA,YAAY,IAAI7B,IAAItB;YACtB,EAAE,OAAOoD,KAAK;gBACZC,QAAQC,KAAK,CAACF;gBACd,MAAM,IAAIhB,MACR,AAAC,0BAAuBpC,MAAI;YAEhC;YAEA,IACEG,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJS,cAAc,EACf,GAAGP,QAAQ;gBACZ,IAAI,CAACO,eAAerC,OAAO+B,OAAO,EAAE/B,OAAOgC,cAAc,EAAEC,YAAY;oBACrE,MAAM,IAAIf,MACR,AAAC,uBAAoBpC,MAAI,kCAAiCmD,UAAUK,QAAQ,GAAC,gEAC1E;gBAEP;YACF;QACF;IACF;IAEA,IAAIxD,IAAIyD,QAAQ,CAAC,WAAW,CAACvC,OAAOwC,mBAAmB,EAAE;QACvD,yDAAyD;QACzD,+CAA+C;QAC/C,OAAO1D;IACT;IAEA,OAAO,AAAGF,2BAA2BoB,OAAOK,IAAI,IAAE,UAAOoC,mBACvD3D,OACA,QAAKmB,QAAM,QAAKC,CAAAA,WAAW,EAAC;AAChC;AAEA,MAAMwC,UAAU,IAAInD,IAGlB;IACA;QAAC;QAAW4B;KAAc;IAC1B;QAAC;QAASpB;KAAY;IACtB;QAAC;QAAcgB;KAAiB;IAChC;QAAC;QAAUD;KAAa;IACxB;QAAC;QAAUG;KAAa;CACzB;AAED,MAAM0B,sBAAsB;IAC1B;IACA;IACA;IACA;IACA7C;CACD;AA+BD,SAAS8C,gBACP9D,GAAoC;IAEpC,OAAO,AAACA,IAAsB+D,OAAO,KAAK/C;AAC5C;AAEA,SAASgD,kBACPhE,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKgB;AAC1C;AAEA,SAASiD,eAAejE,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACd8D,CAAAA,gBAAgB9D,QACfgE,kBAAkBhE,IAAmB;AAE3C;AA8CA,SAASkE,UACP,KAAsC,EACtC/C,KAAyB,EACzBgD,MAAmB,EACnBC,KAAyB;IAHzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAKA,IAAIF,SAAUD,CAAAA,WAAW,UAAUA,WAAW,YAAW,GAAI;QAC3D,yDAAyD;QACzD,MAAMI,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAahC,IAAI,CAACmC,SAASF,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAa/B,MAAM,EAAE;YACvB,MAAMmC,gBAAgBC,KAAKC,GAAG,IAAIN,gBAAgB;YAClD,OAAO;gBACLO,QAAQT,SAASU,MAAM,CAAC,CAACC,IAAMA,KAAKZ,WAAW,CAAC,EAAE,GAAGO;gBACrDM,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQT;YAAUY,MAAM;QAAI;IACvC;IACA,IACE,OAAO/D,UAAU,YACjBgD,WAAW,UACXA,WAAW,cACX;QACA,OAAO;YAAEY,QAAQV;YAAaa,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAIxE,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACY;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACgE,GAAG,CACpC,CAACC,IAAMd,SAASe,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMd,QAAQ,CAACA,SAAS7B,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAEsC;QAAQG,MAAM;IAAI;AAC7B;AAmBA,SAASK,iBAAiB,KASR;IATQ,IAAA,EACxBrE,MAAM,EACNlB,GAAG,EACHwF,WAAW,EACXrB,MAAM,EACNhD,KAAK,EACLC,OAAO,EACPgD,KAAK,EACLqB,MAAM,EACU,GATQ;IAUxB,IAAID,aAAa;QACf,OAAO;YAAExF;YAAK0F,QAAQ1E;YAAWoD,OAAOpD;QAAU;IACpD;IAEA,MAAM,EAAE+D,MAAM,EAAEG,IAAI,EAAE,GAAGhB,UAAUhD,QAAQC,OAAOgD,QAAQC;IAC1D,MAAMuB,OAAOZ,OAAOtC,MAAM,GAAG;IAE7B,OAAO;QACL2B,OAAO,CAACA,SAASc,SAAS,MAAM,UAAUd;QAC1CsB,QAAQX,OACLI,GAAG,CACF,CAACC,GAAGQ,IACF,AAAGH,OAAO;gBAAEvE;gBAAQlB;gBAAKoB;gBAASD,OAAOiE;YAAE,KAAG,MAC5CF,CAAAA,SAAS,MAAME,IAAIQ,IAAI,CAAA,IACtBV,MAENtD,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD5B,KAAKyF,OAAO;YAAEvE;YAAQlB;YAAKoB;YAASD,OAAO4D,MAAM,CAACY,KAAK;QAAC;IAC1D;AACF;AAEA,SAASE,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOnB,SAASmB,GAAG;IACrB;IACA,OAAO9E;AACT;AAEA,SAAS+E,mBAAmBC,WAAuC;QAC/CA;IAAlB,MAAMC,YAAYD,EAAAA,sBAAAA,YAAY9E,MAAM,qBAAlB8E,oBAAoBP,MAAM,KAAI;IAChD,MAAMS,OAAOtC,QAAQ/B,GAAG,CAACoE;IACzB,IAAIC,MAAM;QACR,OAAOA,KAAKF;IACd;IACA,MAAM,IAAI5D,MACR,AAAC,2DAAwD1C,cAAckC,IAAI,CACzE,QACA,iBAAcqE;AAEpB;AAEA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASE,cACPC,GAA2B,EAC3BpG,GAAW,EACXmE,MAAmB,EACnBkC,WAA6B,EAC7BC,oBAA2E,EAC3EC,eAAqC;IAErC,IAAI,CAACH,OAAOA,IAAIpG,GAAG,KAAKW,gBAAgByF,GAAG,CAAC,kBAAkB,KAAKpG,KAAK;QACtE;IACF;IACAoG,GAAG,CAAC,kBAAkB,GAAGpG;IACzB,MAAMsF,IAAI,YAAYc,MAAMA,IAAII,MAAM,KAAKC,QAAQC,OAAO;IAC1DpB,EAAEqB,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACR,IAAIS,UAAU,EAAE;YACnB,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACAvG,gBAAgBwG,GAAG,CAAC9G;QACpB,IAAIqG,gBAAgB,QAAQ;YAC1BE,gBAAgB;QAClB;QACA,IAAID,wCAAAA,qBAAsBS,OAAO,EAAE;YACjC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGb;YACxC,mDAAmD;YACnD,sDAAsD;YACtDE,qBAAqBS,OAAO,CAAC;gBAAEC;gBAAcC;YAAc;QAC7D;QACA,IAAI9G,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;gBACrC8D;YAAJ,KAAIA,qBAAAA,IAAIc,aAAa,qBAAjBd,mBAAmBc,aAAa,EAAE;gBACpC,MAAMC,SAASC,iBAAiBhB,IAAIc,aAAa,CAACA,aAAa;gBAC/D,IAAI,CAACC,OAAOE,QAAQ,EAAE;gBACpB,sHAAsH;gBACxH,OAAO,IAAIlD,WAAW,gBAAgBgD,OAAOG,OAAO,KAAK,QAAQ;oBAC/DzH,SACE,AAAC,qBAAkBG,MAAI;gBAE3B,OAAO,IACLmE,WAAW,UACXgD,OAAOE,QAAQ,KAAK,cACpBF,OAAOE,QAAQ,KAAK,WACpBF,OAAOE,QAAQ,KAAK,YACpB;oBACAxH,SACE,AAAC,qBAAkBG,MAAI,6DAA0DmH,OAAOE,QAAQ,GAAC;gBAErG;YACF;QACF;IACF;AACF;AAEA,MAAME,eAAe;QAAC,EACpBC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVxD,MAAM,EACNyD,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,MAAM,EACN1B,WAAW,EACX2B,OAAO,EACPC,SAAS,EACT/G,MAAM,EACNsE,WAAW,EACXC,MAAM,EACNa,oBAAoB,EACpBC,eAAe,EACf2B,eAAe,EACfC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,aAAa,EACb,GAAGC,MACe;IAClBP,UAAUD,SAAS,SAASC;IAC5B,qBACE;;0BACE,KAAC5B;gBACE,GAAGmC,IAAI;gBACP,GAAGf,aAAa;gBACjBgB,UAAS;gBACTC,aAAWtE;gBACXyD,WAAWA;gBACXc,OAAO;oBAAE,GAAGb,QAAQ;oBAAE,GAAGC,SAAS;gBAAC;gBACnCa,KAAKvJ,YACH,CAACgH;oBACC,IAAIjG,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;wBACzC,IAAI8D,OAAO,CAAC6B,WAAW;4BACrB5E,QAAQC,KAAK,CAAE,6CAA4C8C;wBAC7D;oBACF;oBACA8B,gBAAgB9B;oBAChB,IAAIA,uBAAAA,IAAKwC,QAAQ,EAAE;wBACjBzC,cACEC,KACA6B,WACA9D,QACAkC,aACAC,sBACAC;oBAEJ;gBACF,GACA;oBACE2B;oBACAD;oBACA9D;oBACAkC;oBACAC;oBACAC;iBACD;gBAEH4B,QAAQ,CAACU;oBACP,MAAMzC,MAAMyC,MAAMC,aAAa;oBAC/B3C,cACEC,KACA6B,WACA9D,QACAkC,aACAC,sBACAC;oBAEF,IAAI4B,QAAQ;wBACVA,OAAOU;oBACT;gBACF;gBACAT,SAAS,CAACS;oBACR,IAAIxC,gBAAgB,QAAQ;wBAC1B,2EAA2E;wBAC3EE,gBAAgB;oBAClB;oBACA,IAAI6B,SAAS;wBACXA,QAAQS;oBACV;gBACF;;YAEAd,CAAAA,UAAU1B,gBAAgB,MAAK,mBAC/B,KAAC0C;0BACC,cAAA,KAAC3C;oBACE,GAAGmC,IAAI;oBACR,kDAAkD;oBAClDP,SAASA;oBACTQ,UAAS;oBACTC,aAAWtE;oBACXuE,OAAOb;oBACPD,WAAWA;oBAIV,GAAGrC,iBAAiB;wBACnBrE;wBACAlB,KAAKiI;wBACLzC;wBACArB;wBACAhD,OAAOuG;wBACPtG,SAASuG;wBACTvD,OAAOkE;wBACP7C;oBACF,EAAE;;;;;AAMd;AAEA,eAAe,SAASuD,MAAM,KAmBjB;IAnBiB,IAAA,EAC5BhJ,GAAG,EACHoE,KAAK,EACLoB,cAAc,KAAK,EACnByD,WAAW,KAAK,EAChBjB,OAAO,EACPkB,WAAW,IAAI,EACfC,YAAY,EACZvB,SAAS,EACTxG,OAAO,EACPD,KAAK,EACLiI,MAAM,EACNV,KAAK,EACLW,SAAS,EACTC,cAAc,EACdC,iBAAiB,EACjBlD,cAAc,OAAO,EACrBmD,WAAW,EACX,GAAGC,KACQ,GAnBiB;IAoB5B,MAAMC,gBAAgBrK,WAAWO;IACjC,MAAMsB,SAAsB5B,QAAQ;QAClC,MAAMqK,IAAIzJ,aAAawJ,iBAAiBjK;QACxC,MAAM6E,WAAW;eAAIqF,EAAEtF,WAAW;eAAKsF,EAAEC,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAM1F,cAAcsF,EAAEtF,WAAW,CAACwF,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,OAAO;YAAE,GAAGJ,CAAC;YAAErF;YAAUD;QAAY;IACvC,GAAG;QAACqF;KAAc;IAElB,IAAInB,OAA4BkB;IAChC,IAAItF,SAAmCC,QAAQ,eAAe;IAC9D,IAAI,YAAYmE,MAAM;QACpB,qDAAqD;QACrD,IAAIA,KAAKpE,MAAM,EAAEA,SAASoE,KAAKpE,MAAM;QAErC,+CAA+C;QAC/C,OAAOoE,KAAKpE,MAAM;IACpB;IAEA,IAAIsB,SAAgCM;IACpC,IAAI,YAAYwC,MAAM;QACpB,IAAIA,KAAK9C,MAAM,EAAE;YACf,MAAMuE,oBAAoBzB,KAAK9C,MAAM;YACrCA,SAAS,CAACwE;gBACR,MAAM,EAAE/I,QAAQgJ,CAAC,EAAE,GAAGC,MAAM,GAAGF;gBAC/B,gDAAgD;gBAChD,2CAA2C;gBAC3C,OAAOD,kBAAkBG;YAC3B;QACF;QACA,8CAA8C;QAC9C,OAAO5B,KAAK9C,MAAM;IACpB;IAEA,IAAI2E,YAAY;IAChB,IAAInG,eAAejE,MAAM;QACvB,MAAMqK,kBAAkBvG,gBAAgB9D,OAAOA,IAAI+D,OAAO,GAAG/D;QAE7D,IAAI,CAACqK,gBAAgBrK,GAAG,EAAE;YACxB,MAAM,IAAIoC,MACR,AAAC,gJAA6IM,KAAKC,SAAS,CAC1J0H;QAGN;QACAb,cAAcA,eAAea,gBAAgBb,WAAW;QACxDY,YAAYC,gBAAgBrK,GAAG;QAC/B,IAAI,CAACmE,UAAUA,WAAW,QAAQ;YAChCiF,SAASA,UAAUiB,gBAAgBjB,MAAM;YACzCjI,QAAQA,SAASkJ,gBAAgBlJ,KAAK;YACtC,IAAI,CAACkJ,gBAAgBjB,MAAM,IAAI,CAACiB,gBAAgBlJ,KAAK,EAAE;gBACrD,MAAM,IAAIiB,MACR,AAAC,6JAA0JM,KAAKC,SAAS,CACvK0H;YAGN;QACF;IACF;IACArK,MAAM,OAAOA,QAAQ,WAAWA,MAAMoK;IAEtC,IAAIrC,SACF,CAACkB,YAAajB,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAIhI,IAAI4C,UAAU,CAAC,YAAY5C,IAAI4C,UAAU,CAAC,UAAU;QACtD,uEAAuE;QACvE4C,cAAc;QACduC,SAAS;IACX;IACA,IAAI,OAAOnH,WAAW,eAAeN,gBAAgBgK,GAAG,CAACtK,MAAM;QAC7D+H,SAAS;IACX;IACA,IAAI7G,OAAOsE,WAAW,EAAE;QACtBA,cAAc;IAChB;IAEA,MAAM,CAAC+E,cAAchE,gBAAgB,GAAGhH,SAAS;IACjD,MAAM,CAAC2I,iBAAiBsC,eAAeC,iBAAiB,GACtD9K,gBAAkC;QAChC+K,SAASxB;QACTyB,YAAYxB,gBAAgB;QAC5ByB,UAAU,CAAC7C;IACb;IACF,MAAMM,YAAY,CAACN,UAAUyC;IAE7B,MAAMK,eAAuD;QAC3DC,WAAW;QACXxD,SAAS;QACTyD,UAAU;QACV5J,OAAO;QACPiI,QAAQ;QACR4B,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,MAAMC,aAAqD;QACzDP,WAAW;QACXxD,SAAS;QACTnG,OAAO;QACPiI,QAAQ;QACR4B,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,IAAIE,WAAW;IACf,IAAIC;IACJ,MAAMC,cAA+B;QACnCnE,UAAU;QACVoE,KAAK;QACLC,MAAM;QACNC,QAAQ;QACRC,OAAO;QAEPd,WAAW;QACXM,SAAS;QACTF,QAAQ;QACRC,QAAQ;QAER7D,SAAS;QACTnG,OAAO;QACPiI,QAAQ;QACRyC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,WAAW;QAEX3C;QACAC;IACF;IAEA,IAAI5B,WAAW7B,OAAO1E;IACtB,IAAIsG,YAAY5B,OAAOuD;IACvB,MAAMzB,aAAa9B,OAAOzE;IAE1B,IAAIjB,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,CAACtC,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3C0H,WAAWA,YAAY;YACvBD,YAAYA,aAAa;YACzBjC,cAAc;QAChB,OAAO;YACL,IAAI,CAAC3B,oBAAoBoI,QAAQ,CAAC9H,SAAS;gBACzC,MAAM,IAAI/B,MACR,AAAC,qBAAkBpC,MAAI,gDAA6CmE,SAAO,wBAAqBN,oBAAoBsB,GAAG,CACrH+G,QACAtK,IAAI,CAAC,OAAK;YAEhB;YAEA,IACE,AAAC,OAAO8F,aAAa,eAAeyE,MAAMzE,aACzC,OAAOD,cAAc,eAAe0E,MAAM1E,YAC3C;gBACA,MAAM,IAAIrF,MACR,AAAC,qBAAkBpC,MAAI;YAE3B;YACA,IAAImE,WAAW,UAAWhD,CAAAA,SAASiI,MAAK,GAAI;gBAC1CvJ,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YACA,IAAI,CAACe,qBAAqBkL,QAAQ,CAACjE,UAAU;gBAC3C,MAAM,IAAI5F,MACR,AAAC,qBAAkBpC,MAAI,iDAA8CgI,UAAQ,wBAAqBjH,qBAAqBoE,GAAG,CACxH+G,QACAtK,IAAI,CAAC,OAAK;YAEhB;YACA,IAAIqH,YAAYjB,YAAY,QAAQ;gBAClC,MAAM,IAAI5F,MACR,AAAC,qBAAkBpC,MAAI;YAE3B;YACA,IAAIoE,SAASD,WAAW,UAAUA,WAAW,cAAc;gBACzDtE,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YACA,IAAIqG,gBAAgB,QAAQ;gBAC1B,IAAIlC,WAAW,UAAU,AAACuD,CAAAA,YAAY,CAAA,IAAMD,CAAAA,aAAa,CAAA,IAAK,MAAM;oBAClE5H,SACE,AAAC,qBAAkBG,MAAI;gBAE3B;gBACA,IAAI,CAACwJ,aAAa;oBAChB,MAAM4C,iBAAiB;wBAAC;wBAAQ;wBAAO;wBAAQ;qBAAO,CAAC,iCAAiC;;oBAExF,MAAM,IAAIhK,MACR,AAAC,qBAAkBpC,MAAI,mUAGgEoM,eAAexK,IAAI,CACxG,OACA;gBAIN;YACF;YACA,IAAI,SAAS2G,MAAM;gBACjB1I,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YAEA,IAAI,CAACwF,eAAeC,WAAWM,oBAAoB;gBACjD,MAAMsG,SAAS5G,OAAO;oBACpBvE;oBACAlB;oBACAmB,OAAOuG,YAAY;oBACnBtG,SAASuG,cAAc;gBACzB;gBACA,IAAItG;gBACJ,IAAI;oBACFA,MAAM,IAAIC,IAAI+K;gBAChB,EAAE,OAAOjJ,KAAK,CAAC;gBACf,IAAIiJ,WAAWrM,OAAQqB,OAAOA,IAAIiL,QAAQ,KAAKtM,OAAO,CAACqB,IAAIkL,MAAM,EAAG;oBAClE1M,SACE,AAAC,qBAAkBG,MAAI,4HACpB;gBAEP;YACF;YAEA,IAAI0I,OAAO;gBACT,IAAI8D,oBAAoBC,OAAOC,IAAI,CAAChE,OAAO1D,MAAM,CAC/C,CAAC2H,MAAQA,OAAOnB;gBAElB,IAAIgB,kBAAkB/J,MAAM,EAAE;oBAC5B5C,SACE,AAAC,oBAAiBG,MAAI,iGAA8FwM,kBAAkB5K,IAAI,CACxI;gBAGN;YACF;YAEA,IACE,OAAOhB,WAAW,eAClB,CAACF,gBACDE,OAAOgM,mBAAmB,EAC1B;gBACAlM,eAAe,IAAIkM,oBAAoB,CAACC;oBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;4BAE3BD;wBADf,0EAA0E;wBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgB9M,GAAG,KAAI;wBACtC,MAAMkN,WAAW1M,QAAQqB,GAAG,CAACmL;wBAC7B,IACEE,YACA,CAACA,SAASjE,QAAQ,IAClBiE,SAAS7G,WAAW,KAAK,UACzB,CAAC6G,SAASlN,GAAG,CAAC4C,UAAU,CAAC,YACzB,CAACsK,SAASlN,GAAG,CAAC4C,UAAU,CAAC,UACzB;4BACA,iDAAiD;4BACjD/C,SACE,AAAC,qBAAkBqN,SAASlN,GAAG,GAAC,8HAC7B;wBAEP;oBACF;gBACF;gBACA,IAAI;oBACFU,aAAayM,OAAO,CAAC;wBACnBC,MAAM;wBACNC,UAAU;oBACZ;gBACF,EAAE,OAAOjK,KAAK;oBACZ,oCAAoC;oBACpCC,QAAQC,KAAK,CAACF;gBAChB;YACF;QACF;IACF;IACA,MAAMyE,WAAW4E,OAAOa,MAAM,CAAC,CAAC,GAAG5E,OAAO8C;IAC1C,MAAM1D,YACJzB,gBAAgB,UAAU,CAACkE,eACvB;QACEgD,gBAAgBlE,aAAa;QAC7BmE,oBAAoBlE,kBAAkB;QACtCtE,QAAQ;QACRyI,iBAAiB,AAAC,UAAOjE,cAAY;IACvC,IACA,CAAC;IACP,IAAIrF,WAAW,QAAQ;QACrB,sCAAsC;QACtC0G,aAAavD,OAAO,GAAG;QACvBuD,aAAaxD,QAAQ,GAAG;QACxBwD,aAAaY,GAAG,GAAG;QACnBZ,aAAaa,IAAI,GAAG;QACpBb,aAAac,MAAM,GAAG;QACtBd,aAAae,KAAK,GAAG;IACvB,OAAO,IACL,OAAOlE,aAAa,eACpB,OAAOD,cAAc,aACrB;QACA,iDAAiD;QACjD,MAAMiG,WAAWjG,YAAYC;QAC7B,MAAMiG,aAAaxB,MAAMuB,YAAY,SAAS,AAAC,KAAEA,WAAW,MAAI;QAChE,IAAIvJ,WAAW,cAAc;YAC3B,qEAAqE;YACrE0G,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBiE,WAAW;YACXD,WAAWsC,UAAU,GAAGA;QAC1B,OAAO,IAAIxJ,WAAW,aAAa;YACjC,oEAAoE;YACpE0G,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBwD,aAAaiB,QAAQ,GAAG;YACxBR,WAAW;YACXD,WAAWS,QAAQ,GAAG;YACtBP,cAAc,AAAC,uGAAoG7D,WAAS,qBAAkBD,YAAU;QAC1J,OAAO,IAAItD,WAAW,SAAS;YAC7B,gEAAgE;YAChE0G,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBwD,aAAa1J,KAAK,GAAGuG;YACrBmD,aAAazB,MAAM,GAAG3B;QACxB;IACF,OAAO;QACL,wBAAwB;QACxB,IAAItH,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;YACzC,MAAM,IAAIF,MACR,AAAC,qBAAkBpC,MAAI;QAE3B;IACF;IAEA,IAAIwH,gBAAmC;QACrCxH,KAAKW;QACL+E,QAAQ1E;QACRoD,OAAOpD;IACT;IAEA,IAAIqH,WAAW;QACbb,gBAAgBjC,iBAAiB;YAC/BrE;YACAlB;YACAwF;YACArB;YACAhD,OAAOuG;YACPtG,SAASuG;YACTvD;YACAqB;QACF;IACF;IAEA,IAAIwC,YAAoBjI;IAExB,IAAIG,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAO1B,WAAW,aAAa;YACjC,IAAIgN;YACJ,IAAI;gBACFA,UAAU,IAAItM,IAAIkG,cAAcxH,GAAG;YACrC,EAAE,OAAO6N,GAAG;gBACVD,UAAU,IAAItM,IAAIkG,cAAcxH,GAAG,EAAEY,OAAOkN,QAAQ,CAAC/L,IAAI;YAC3D;YACAvB,QAAQkB,GAAG,CAACkM,QAAQ7L,IAAI,EAAE;gBAAE/B;gBAAKiJ;gBAAU5C;YAAY;QACzD;IACF;IAEA,MAAM0H,YAGF;QACFC,aAAaxG,cAAc9B,MAAM;QACjCkE,YAAYpC,cAAcpD,KAAK;QAC/B6J,aAAa1F,KAAK0F,WAAW;QAC7BC,gBAAgB3F,KAAK2F,cAAc;IACrC;IAEA,MAAMC,kBACJ,OAAOvN,WAAW,cAAc3B,MAAME,SAAS,GAAGF,MAAMkP,eAAe;IACzE,MAAM7H,uBAAuBpH,OAAOqK;IAEpC,MAAM6E,mBAAmBlP,OAA8Bc;IACvDb,UAAU;QACRmH,qBAAqBS,OAAO,GAAGwC;IACjC,GAAG;QAACA;KAAkB;IAEtB4E,gBAAgB;QACd,IAAIC,iBAAiBrH,OAAO,KAAK/G,KAAK;YACpCyK;YACA2D,iBAAiBrH,OAAO,GAAG/G;QAC7B;IACF,GAAG;QAACyK;QAAkBzK;KAAI;IAE1B,MAAMqO,iBAAiB;QACrBtG;QACAP;QACAC;QACAC;QACAC;QACAxD;QACAyD;QACAC;QACAC;QACAE;QACA9G;QACAsE;QACAa;QACAZ;QACAwC;QACA3B;QACAC;QACA2B;QACAG;QACAC,eAAelE;QACf,GAAGmE,IAAI;IACT;IACA,qBACE;;0BACE,MAAC+F;gBAAK5F,OAAOmC;;oBACVS,yBACC,KAACgD;wBAAK5F,OAAO2C;kCACVE,4BACC,KAACnF;4BACCsC,OAAO;gCACLpB,SAAS;gCACTwE,UAAU;gCACV3K,OAAO;gCACPiI,QAAQ;gCACR4B,YAAY;gCACZC,SAAS;gCACTC,QAAQ;gCACRC,QAAQ;gCACRC,SAAS;4BACX;4BACAmD,KAAI;4BACJC,eAAa;4BACbxO,KAAKuL;6BAEL;yBAEJ;kCACJ,KAAChE;wBAAc,GAAG8G,cAAc;;;;YAEjCpF,WACC,sEAAsE;YACtE,qEAAqE;YACrE,6DAA6D;YAC7D,EAAE;YACF,8EAA8E;0BAC9E,KAACzJ;0BACC,cAAA,KAACiP;oBAOCC,KAAI;oBACJC,IAAG;oBACH5M,MAAMyF,cAAc9B,MAAM,GAAG1E,YAAYwG,cAAcxH,GAAG;oBACzD,GAAG+N,SAAS;mBARX,YACAvG,cAAcxH,GAAG,GACjBwH,cAAc9B,MAAM,GACpB8B,cAAcpD,KAAK;iBAQvB;;;AAGV"}