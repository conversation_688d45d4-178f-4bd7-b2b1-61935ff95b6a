var e={189:(e,t,i)=>{i.d(t,{LO:()=>o});const o={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},615:(e,t,i)=>{i.d(t,{A:()=>o});class o{constructor(e,t,i){this.el=e,this.options=t,this.events=i,this.el=e,this.options=t,this.events={}}createCollection(e,t){var i;e.push({id:(null===(i=null==t?void 0:t.el)||void 0===i?void 0:i.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}},926:(e,t,i)=>{i.d(t,{JD:()=>a,Lc:()=>n,PK:()=>o,gj:()=>l,sH:()=>r,wC:()=>s,yd:()=>d});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const o=e=>"true"===e,l=(e,t,i="")=>(window.getComputedStyle(e).getPropertyValue(t)||i).replace(" ",""),n=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let i=(e=>window.getComputedStyle(e).getPropertyValue("z-index"))(e);"auto"!==i&&(i=parseInt(i,10),i>t&&(t=i))})),t},s=(e,t)=>{const i=e.children;for(let e=0;e<i.length;e++)if(i[e]===t)return!0;return!1},r=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||r(e.parentElement)},a=(e,t,i=null)=>{const o=new CustomEvent(e,{detail:{payload:i},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(o)},d=(e,t)=>{const i=()=>{t(),e.removeEventListener("transitionend",i,!0)},o=window.getComputedStyle(e),l=o.getPropertyValue("transition-duration");"none"!==o.getPropertyValue("transition-property")&&parseFloat(l)>0?e.addEventListener("transitionend",i,!0):t()}}},t={};function i(o){var l=t[o];if(void 0!==l)return l.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,i),n.exports}i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o={};i.d(o,{A:()=>d});var l=i(926),n=i(189),s=i(615);
/*
 * HSOverlay
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
class r extends s.A{constructor(e,t,i){var o,s,r,a,d,c;super(e,t,i),this.initialZIndex=0,this.toggleButtons=Array.from(document.querySelectorAll(`[data-hs-overlay="#${this.el.id}"]`));const h=this.collectToggleParameters(this.toggleButtons),u=e.getAttribute("data-hs-overlay-options"),y=u?JSON.parse(u):{},p=Object.assign(Object.assign(Object.assign({},y),h),t);this.hiddenClass=(null==p?void 0:p.hiddenClass)||"hidden",this.emulateScrollbarSpace=(null==p?void 0:p.emulateScrollbarSpace)||!1,this.isClosePrev=null===(o=null==p?void 0:p.isClosePrev)||void 0===o||o,this.backdropClasses=null!==(s=null==p?void 0:p.backdropClasses)&&void 0!==s?s:"hs-overlay-backdrop transition duration fixed inset-0 bg-gray-900/50 dark:bg-neutral-900/80",this.backdropParent="string"==typeof p.backdropParent?document.querySelector(p.backdropParent):document.body,this.backdropExtraClasses=null!==(r=null==p?void 0:p.backdropExtraClasses)&&void 0!==r?r:"",this.moveOverlayToBody=(null==p?void 0:p.moveOverlayToBody)||null,this.openNextOverlay=!1,this.autoHide=null,this.initContainer=(null===(a=this.el)||void 0===a?void 0:a.parentElement)||null,this.isCloseWhenClickInside=(0,l.PK)((0,l.gj)(this.el,"--close-when-click-inside","false")||"false"),this.isTabAccessibilityLimited=(0,l.PK)((0,l.gj)(this.el,"--tab-accessibility-limited","true")||"true"),this.isLayoutAffect=(0,l.PK)((0,l.gj)(this.el,"--is-layout-affect","false")||"false"),this.hasAutofocus=(0,l.PK)((0,l.gj)(this.el,"--has-autofocus","true")||"true"),this.hasDynamicZIndex=(0,l.PK)((0,l.gj)(this.el,"--has-dynamic-z-index","false")||"false"),this.hasAbilityToCloseOnBackdropClick=(0,l.PK)(this.el.getAttribute("data-hs-overlay-keyboard")||"true");const m=(0,l.gj)(this.el,"--auto-close"),v=(0,l.gj)(this.el,"--auto-close-equality-type"),f=(0,l.gj)(this.el,"--opened");this.autoClose=!isNaN(+m)&&isFinite(+m)?+m:n.LO[m]||null,this.autoCloseEqualityType=null!==(d=v)&&void 0!==d?d:null,this.openedBreakpoint=(!isNaN(+f)&&isFinite(+f)?+f:n.LO[f])||null,this.animationTarget=(null===(c=null==this?void 0:this.el)||void 0===c?void 0:c.querySelector(".hs-overlay-animation-target"))||this.el,this.initialZIndex=parseInt(getComputedStyle(this.el).zIndex,10),this.onElementClickListener=[],this.init()}elementClick(){const e=()=>{const e={el:this.el,isOpened:!!this.el.classList.contains("open")};this.fireEvent("toggleClicked",e),(0,l.JD)("toggleClicked.hs.overlay",this.el,e)};this.el.classList.contains("opened")?this.close(!1,e):this.open(e)}overlayClick(e){e.target.id&&`#${e.target.id}`===this.el.id&&this.isCloseWhenClickInside&&this.hasAbilityToCloseOnBackdropClick&&this.close()}backdropClick(){this.close()}init(){if(this.createCollection(window.$hsOverlayCollection,this),this.isLayoutAffect&&this.openedBreakpoint){const e=r.getInstance(this.el,!0);r.setOpened(this.openedBreakpoint,e)}this.onOverlayClickListener=e=>this.overlayClick(e),this.el.addEventListener("click",this.onOverlayClickListener),this.toggleButtons.length&&this.buildToggleButtons()}getElementsByZIndex(){return window.$hsOverlayCollection.filter((e=>e.element.initialZIndex===this.initialZIndex))}buildToggleButtons(){this.toggleButtons.forEach((e=>{this.el.classList.contains("opened")?e.ariaExpanded="true":e.ariaExpanded="false",this.onElementClickListener.push({el:e,fn:()=>this.elementClick()}),e.addEventListener("click",this.onElementClickListener.find((t=>t.el===e)).fn)}))}hideAuto(){const e=parseInt((0,l.gj)(this.el,"--auto-hide","0"));e&&(this.autoHide=setTimeout((()=>{this.close()}),e))}checkTimer(){this.autoHide&&(clearTimeout(this.autoHide),this.autoHide=null)}buildBackdrop(){const e=this.el.classList.value.split(" "),t=parseInt(window.getComputedStyle(this.el).getPropertyValue("z-index")),i=this.el.getAttribute("data-hs-overlay-backdrop-container")||!1;this.backdrop=document.createElement("div");let o=`${this.backdropClasses} ${this.backdropExtraClasses}`;const n="static"!==(0,l.gj)(this.el,"--overlay-backdrop","true"),s="false"===(0,l.gj)(this.el,"--overlay-backdrop","true");this.backdrop.id=`${this.el.id}-backdrop`,"style"in this.backdrop&&(this.backdrop.style.zIndex=""+(t-1));for(const t of e)(t.startsWith("hs-overlay-backdrop-open:")||t.includes(":hs-overlay-backdrop-open:"))&&(o+=` ${t}`);s||(i&&(this.backdrop=document.querySelector(i).cloneNode(!0),this.backdrop.classList.remove("hidden"),o=`${this.backdrop.classList.toString()}`,this.backdrop.classList.value=""),n&&(this.onBackdropClickListener=()=>this.backdropClick(),this.backdrop.addEventListener("click",this.onBackdropClickListener,!0)),this.backdrop.setAttribute("data-hs-overlay-backdrop-template",""),this.backdropParent.appendChild(this.backdrop),setTimeout((()=>{this.backdrop.classList.value=o})))}destroyBackdrop(){const e=document.querySelector(`#${this.el.id}-backdrop`);e&&(this.openNextOverlay&&(e.style.transitionDuration=1.8*parseFloat(window.getComputedStyle(e).transitionDuration.replace(/[^\d.-]/g,""))+"s"),e.classList.add("opacity-0"),(0,l.yd)(e,(()=>{e.remove()})))}focusElement(){const e=this.el.querySelector("[autofocus]");if(!e)return!1;e.focus()}getScrollbarSize(){let e=document.createElement("div");e.style.overflow="scroll",e.style.width="100px",e.style.height="100px",document.body.appendChild(e);let t=e.offsetWidth-e.clientWidth;return document.body.removeChild(e),t}collectToggleParameters(e){let t={};return e.forEach((e=>{const i=e.getAttribute("data-hs-overlay-options"),o=i?JSON.parse(i):{};t=Object.assign(Object.assign({},t),o)})),t}isElementVisible(){const e=window.getComputedStyle(this.el);if("none"===e.display||"hidden"===e.visibility||"0"===e.opacity)return!1;const t=this.el.getBoundingClientRect();if(0===t.width||0===t.height)return!1;let i=this.el.parentElement;for(;i;){const e=window.getComputedStyle(i);if("none"===e.display||"hidden"===e.visibility||"0"===e.opacity)return!1;i=i.parentElement}return!0}open(e=null){this.hasDynamicZIndex&&(r.currentZIndex<this.initialZIndex&&(r.currentZIndex=this.initialZIndex),r.currentZIndex++,this.el.style.zIndex=`${r.currentZIndex}`);const t=document.querySelectorAll(".hs-overlay.open"),i=window.$hsOverlayCollection.find((e=>Array.from(t).includes(e.element.el)&&!e.element.isLayoutAffect)),o=document.querySelectorAll(`[data-hs-overlay="#${this.el.id}"]`),n="true"!==(0,l.gj)(this.el,"--body-scroll","false");if(this.isClosePrev&&i)return this.openNextOverlay=!0,i.element.close().then((()=>{this.open(),this.openNextOverlay=!1}));n&&(document.body.style.overflow="hidden",this.emulateScrollbarSpace&&(document.body.style.paddingRight=`${this.getScrollbarSize()}px`)),this.buildBackdrop(),this.checkTimer(),this.hideAuto(),o.forEach((e=>{e.ariaExpanded&&(e.ariaExpanded="true")})),this.el.classList.remove(this.hiddenClass),this.el.setAttribute("aria-overlay","true"),this.el.setAttribute("tabindex","-1"),setTimeout((()=>{if(this.el.classList.contains("opened"))return!1;this.el.classList.add("open","opened"),this.isLayoutAffect&&document.body.classList.add("hs-overlay-body-open"),this.fireEvent("open",this.el),(0,l.JD)("open.hs.overlay",this.el,this.el),this.hasAutofocus&&this.focusElement(),"function"==typeof e&&e(),this.isElementVisible()&&r.openedItemsQty++}),50)}close(e=!1,t=null){this.isElementVisible()&&(r.openedItemsQty=r.openedItemsQty<=0?0:r.openedItemsQty-1),0===r.openedItemsQty&&this.isLayoutAffect&&document.body.classList.remove("hs-overlay-body-open");const i=e=>{if(this.el.classList.contains("open"))return!1;document.querySelectorAll(`[data-hs-overlay="#${this.el.id}"]`).forEach((e=>{e.ariaExpanded&&(e.ariaExpanded="false")})),this.el.classList.add(this.hiddenClass),this.hasDynamicZIndex&&(this.el.style.zIndex=""),this.destroyBackdrop(),this.fireEvent("close",this.el),(0,l.JD)("close.hs.overlay",this.el,this.el),document.querySelector(".hs-overlay.opened")||(document.body.style.overflow="",this.emulateScrollbarSpace&&(document.body.style.paddingRight="")),e(this.el),"function"==typeof t&&t(),0===r.openedItemsQty&&(document.body.classList.remove("hs-overlay-body-open"),this.hasDynamicZIndex&&(r.currentZIndex=0))};return new Promise((t=>{this.el.classList.remove("open","opened"),this.el.removeAttribute("aria-overlay"),this.el.removeAttribute("tabindex"),e?i(t):(0,l.yd)(this.animationTarget,(()=>i(t)))}))}destroy(){this.el.classList.remove("open","opened",this.hiddenClass),this.isLayoutAffect&&document.body.classList.remove("hs-overlay-body-open"),this.el.removeEventListener("click",this.onOverlayClickListener),this.onElementClickListener.length&&(this.onElementClickListener.forEach((({el:e,fn:t})=>{e.removeEventListener("click",t)})),this.onElementClickListener=null),this.backdrop&&this.backdrop.removeEventListener("click",this.onBackdropClickListener),this.backdrop&&(this.backdrop.remove(),this.backdrop=null),window.$hsOverlayCollection=window.$hsOverlayCollection.filter((({element:e})=>e.el!==this.el))}static findInCollection(e){return window.$hsOverlayCollection.find((t=>e instanceof r?t.element.el===e.el:"string"==typeof e?t.element.el===document.querySelector(e):t.element.el===e))||null}static getInstance(e,t){const i="string"==typeof e?document.querySelector(e):e,o=(null==i?void 0:i.getAttribute("data-hs-overlay"))?i.getAttribute("data-hs-overlay"):e,l=window.$hsOverlayCollection.find((e=>e.element.el===("string"==typeof o?document.querySelector(o):o)||e.element.el===("string"==typeof o?document.querySelector(o):o)));return l?t?l:l.element.el:null}static autoInit(){window.$hsOverlayCollection||(window.$hsOverlayCollection=[],document.addEventListener("keydown",(e=>r.accessibility(e)))),window.$hsOverlayCollection&&(window.$hsOverlayCollection=window.$hsOverlayCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll(".hs-overlay:not(.--prevent-on-load-init)").forEach((e=>{window.$hsOverlayCollection.find((t=>{var i;return(null===(i=null==t?void 0:t.element)||void 0===i?void 0:i.el)===e}))||new r(e)}))}static open(e){const t=r.findInCollection(e);t&&t.element.el.classList.contains(t.element.hiddenClass)&&t.element.open()}static close(e){const t=r.findInCollection(e);t&&!t.element.el.classList.contains(t.element.hiddenClass)&&t.element.close()}static setOpened(e,t){document.body.clientWidth>=e?(document.body.classList.add("hs-overlay-body-open"),t.element.open()):t.element.close(!0)}static accessibility(e){var t,i;const o=document.querySelectorAll(".hs-overlay.open"),n=(0,l.Lc)(Array.from(o)),s=window.$hsOverlayCollection.filter((e=>e.element.el.classList.contains("open"))).find((e=>window.getComputedStyle(e.element.el).getPropertyValue("z-index")===`${n}`)),r=null===(i=null===(t=null==s?void 0:s.element)||void 0===t?void 0:t.el)||void 0===i?void 0:i.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),a=[];(null==r?void 0:r.length)&&r.forEach((e=>{(0,l.sH)(e)||a.push(e)}));const d=s&&!e.metaKey;if(d&&!s.element.isTabAccessibilityLimited&&"Tab"===e.code)return!1;d&&a.length&&"Tab"===e.code&&(e.preventDefault(),this.onTab(s)),d&&"Escape"===e.code&&(e.preventDefault(),this.onEscape(s))}static onEscape(e){e&&e.element.hasAbilityToCloseOnBackdropClick&&e.element.close()}static onTab(e){const t=e.element.el,i=Array.from(t.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'));if(0===i.length)return!1;const o=t.querySelector(":focus");if(o){let e=!1;for(const t of i){if(e)return void t.focus();t===o&&(e=!0)}i[0].focus()}else i[0].focus()}static on(e,t,i){const o=r.findInCollection(t);o&&(o.element.events[e]=i)}}r.openedItemsQty=0,r.currentZIndex=0;const a=()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.moveOverlayToBody)))return!1;window.$hsOverlayCollection.filter((e=>e.element.moveOverlayToBody)).forEach((e=>{const t=e.element.moveOverlayToBody,i=e.element.initContainer,o=document.querySelector("body"),n=e.element.el;if(!i&&n)return!1;document.body.clientWidth<=t&&!(0,l.wC)(o,n)?o.appendChild(n):document.body.clientWidth>t&&!i.contains(n)&&i.appendChild(n)}))};window.addEventListener("load",(()=>{r.autoInit(),a()})),window.addEventListener("resize",(()=>{(()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.autoClose)))return!1;window.$hsOverlayCollection.filter((e=>e.element.autoClose)).forEach((e=>{const{autoCloseEqualityType:t,autoClose:i}=e.element;("less-than"===t?document.body.clientWidth<=i:document.body.clientWidth>=i)?e.element.close(!0):e.element.isLayoutAffect&&document.body.classList.add("hs-overlay-body-open")}))})(),a(),(()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.autoClose)))return!1;window.$hsOverlayCollection.filter((e=>e.element.autoClose)).forEach((e=>{const{autoCloseEqualityType:t,autoClose:i}=e.element;("less-than"===t?document.body.clientWidth<=i:document.body.clientWidth>=i)&&e.element.close(!0)}))})(),(()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.el.classList.contains("opened"))))return!1;window.$hsOverlayCollection.filter((e=>e.element.el.classList.contains("opened"))).forEach((e=>{const t=parseInt(window.getComputedStyle(e.element.el).getPropertyValue("z-index")),i=document.querySelector(`#${e.element.el.id}-backdrop`);return!!i&&(t!==parseInt(window.getComputedStyle(i).getPropertyValue("z-index"))+1&&("style"in i&&(i.style.zIndex=""+(t-1)),void document.body.classList.add("hs-overlay-body-open")))}))})()})),"undefined"!=typeof window&&(window.HSOverlay=r);const d=r;var c=o.A;export{c as default};