export = loader;
/**
 * Webpack loader
 *
 * @todo once webpack supports ESM loaders, remove this wrapper.
 *
 * @this {LoaderContext<unknown>}
 *   Context.
 * @param {string} code
 *   Code.
 * @returns {undefined}
 *   Nothing.
 */
declare function loader(this: LoaderContext<unknown>, code: string): undefined;
declare namespace loader {
    export { Options };
}
import type { LoaderContext } from 'webpack';
type Options = Options_;
import type { Options as Options_ } from './lib/index.js' with { 'resolution-mode': 'import' };
//# sourceMappingURL=index.d.cts.map