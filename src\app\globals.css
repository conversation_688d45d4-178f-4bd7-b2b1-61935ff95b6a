@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Open Sans', system-ui, sans-serif;
    background-color: #fafafa;
    color: #334155;
  }
}

@layer components {
  .gradient-text {
    @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-white hover:bg-primary-50 text-primary-600 border-2 border-primary-600 px-6 py-3 rounded-xl font-semibold transition-all duration-200 hover:shadow-soft focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-outline {
    @apply bg-transparent hover:bg-primary-600 text-primary-600 hover:text-white border-2 border-primary-600 px-6 py-3 rounded-xl font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .card {
    @apply bg-white rounded-2xl shadow-soft hover:shadow-medium transition-all duration-300 p-6 border border-secondary-100;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-medium hover:shadow-large transition-all duration-300 p-8 border border-secondary-100;
  }

  .section-padding {
    @apply py-20 px-4 sm:px-6 lg:px-8;
  }

  .container-max {
    @apply max-w-7xl mx-auto;
  }
  
  .prose-verictus {
    @apply prose prose-lg prose-secondary max-w-none;
  }

  .prose-verictus h1 {
    @apply text-3xl md:text-4xl font-bold text-primary-900 mb-6 leading-tight;
  }

  .prose-verictus h2 {
    @apply text-2xl md:text-3xl font-semibold text-primary-800 mb-4 mt-8 leading-tight;
  }

  .prose-verictus h3 {
    @apply text-xl md:text-2xl font-semibold text-primary-700 mb-3 mt-6 leading-tight;
  }

  .prose-verictus p {
    @apply text-secondary-700 leading-relaxed mb-4;
  }

  .prose-verictus a {
    @apply text-primary-600 hover:text-primary-700 font-medium underline decoration-primary-300 hover:decoration-primary-500 transition-colors;
  }

  .prose-verictus ul {
    @apply list-disc list-inside space-y-2 text-secondary-700;
  }

  .prose-verictus ol {
    @apply list-decimal list-inside space-y-2 text-secondary-700;
  }

  .prose-verictus blockquote {
    @apply border-l-4 border-primary-500 pl-6 italic text-secondary-600 bg-primary-50 p-6 rounded-r-xl my-6;
  }

  .expert-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 border border-primary-200;
  }

  .section-header {
    @apply text-center max-w-3xl mx-auto mb-16;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold text-secondary-900 mb-6 leading-tight;
  }

  .section-subtitle {
    @apply text-xl text-secondary-600 leading-relaxed;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
