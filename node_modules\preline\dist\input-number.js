!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var i in n)("object"==typeof exports?exports:e)[i]=n[i]}}(self,(()=>(()=>{"use strict";var e={292:function(e,t){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0;t.stringToBoolean=e=>"true"===e;t.getClassProperty=(e,t,n="")=>(window.getComputedStyle(e).getPropertyValue(t)||n).replace(" ","");t.getClassPropertyAlt=(e,t,n="")=>{let i="";return e.classList.forEach((e=>{e.includes(t)&&(i=e)})),i.match(/:(.*)]/)?i.match(/:(.*)]/)[1]:n};const n=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=n;t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let i=n(e);"auto"!==i&&(i=parseInt(i,10),i>t&&(t=i))})),t};t.isDirectChild=(e,t)=>{const n=e.children;for(let e=0;e<n.length;e++)if(n[e]===t)return!0;return!1};t.isEnoughSpace=(e,t,n="auto",i=10,s=null)=>{const r=t.getBoundingClientRect(),l=s?s.getBoundingClientRect():null,a=window.innerHeight,o=l?r.top-l.top:r.top,u=(s?l.bottom:a)-r.bottom,c=e.clientHeight+i;return"bottom"===n?u>=c:"top"===n?o>=c:o>=c||u>=c};t.isFocused=e=>document.activeElement===e;t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement;t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],n=e.trim().slice(-1);if("{"===t&&"}"===n||"["===t&&"]"===n)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const i=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||i(e.parentElement)};t.isParentOrElementHidden=i;t.isScrollable=e=>{const t=window.getComputedStyle(e),n=t.overflowY,i=t.overflowX,s=("scroll"===n||"auto"===n)&&e.scrollHeight>e.clientHeight,r=("scroll"===i||"auto"===i)&&e.scrollWidth>e.clientWidth;return s||r};t.debounce=(e,t=200)=>{let n;return(...i)=>{clearTimeout(n),n=setTimeout((()=>{e.apply(this,i)}),t)}};t.dispatch=(e,t,n=null)=>{const i=new CustomEvent(e,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(i)};t.afterTransition=(e,t)=>{const n=()=>{t(),e.removeEventListener("transitionend",n,!0)},i=window.getComputedStyle(e),s=i.getPropertyValue("transition-duration");"none"!==i.getPropertyValue("transition-property")&&parseFloat(s)>0?e.addEventListener("transitionend",n,!0):t()};t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild};t.classToClassList=(e,t,n=" ",i="add")=>{e.split(n).forEach((e=>"add"===i?t.classList.add(e):t.classList.remove(e)))};const s={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=s},332:function(e,t,n){
/*
 * HSInputNumber
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=n(292),r=i(n(961));class l extends r.default{constructor(e,t){super(e,t),this.input=this.el.querySelector("[data-hs-input-number-input]")||null,this.increment=this.el.querySelector("[data-hs-input-number-increment]")||null,this.decrement=this.el.querySelector("[data-hs-input-number-decrement]")||null,this.input&&this.checkIsNumberAndConvert();const n=this.el.dataset.hsInputNumber,i=n?JSON.parse(n):{step:1},s=Object.assign(Object.assign({},i),t);this.minInputValue="min"in s?s.min:0,this.maxInputValue="max"in s?s.max:null,this.step="step"in s&&s.step>0?s.step:1,this.init()}inputInput(){this.changeValue()}incrementClick(){this.changeValue("increment")}decrementClick(){this.changeValue("decrement")}init(){this.createCollection(window.$hsInputNumberCollection,this),this.input&&this.increment&&this.build()}checkIsNumberAndConvert(){const e=this.input.value.trim(),t=this.cleanAndExtractNumber(e);null!==t?(this.inputValue=t,this.input.value=t.toString()):(this.inputValue=0,this.input.value="0")}cleanAndExtractNumber(e){const t=[];let n=!1;e.split("").forEach((e=>{e>="0"&&e<="9"?t.push(e):"."!==e||n||(t.push(e),n=!0)}));const i=t.join(""),s=parseFloat(i);return isNaN(s)?null:s}build(){this.input&&this.buildInput(),this.increment&&this.buildIncrement(),this.decrement&&this.buildDecrement(),this.inputValue<=this.minInputValue&&(this.inputValue=this.minInputValue,this.input.value=`${this.minInputValue}`),this.inputValue<=this.minInputValue&&this.changeValue(),this.input.hasAttribute("disabled")&&this.disableButtons()}buildInput(){this.onInputInputListener=()=>this.inputInput(),this.input.addEventListener("input",this.onInputInputListener)}buildIncrement(){this.onIncrementClickListener=()=>this.incrementClick(),this.increment.addEventListener("click",this.onIncrementClickListener)}buildDecrement(){this.onDecrementClickListener=()=>this.decrementClick(),this.decrement.addEventListener("click",this.onDecrementClickListener)}changeValue(e="none"){var t,n;const i={inputValue:this.inputValue},r=null!==(t=this.minInputValue)&&void 0!==t?t:Number.MIN_SAFE_INTEGER,l=null!==(n=this.maxInputValue)&&void 0!==n?n:Number.MAX_SAFE_INTEGER;switch(this.inputValue=isNaN(this.inputValue)?0:this.inputValue,e){case"increment":const e=this.inputValue+this.step;this.inputValue=e>=r&&e<=l?e:l,this.input.value=this.inputValue.toString();break;case"decrement":const t=this.inputValue-this.step;this.inputValue=t>=r&&t<=l?t:r,this.input.value=this.inputValue.toString();break;default:const n=isNaN(parseInt(this.input.value))?0:parseInt(this.input.value);this.inputValue=n>=l?l:n<=r?r:n,this.inputValue<=r&&(this.input.value=this.inputValue.toString())}i.inputValue=this.inputValue,this.inputValue===r?(this.el.classList.add("disabled"),this.decrement&&this.disableButtons("decrement")):(this.el.classList.remove("disabled"),this.decrement&&this.enableButtons("decrement")),this.inputValue===l?(this.el.classList.add("disabled"),this.increment&&this.disableButtons("increment")):(this.el.classList.remove("disabled"),this.increment&&this.enableButtons("increment")),this.fireEvent("change",i),(0,s.dispatch)("change.hs.inputNumber",this.el,i)}disableButtons(e="all"){"all"===e?("BUTTON"!==this.increment.tagName&&"INPUT"!==this.increment.tagName||this.increment.setAttribute("disabled","disabled"),"BUTTON"!==this.decrement.tagName&&"INPUT"!==this.decrement.tagName||this.decrement.setAttribute("disabled","disabled")):"increment"===e?"BUTTON"!==this.increment.tagName&&"INPUT"!==this.increment.tagName||this.increment.setAttribute("disabled","disabled"):"decrement"===e&&("BUTTON"!==this.decrement.tagName&&"INPUT"!==this.decrement.tagName||this.decrement.setAttribute("disabled","disabled"))}enableButtons(e="all"){"all"===e?("BUTTON"!==this.increment.tagName&&"INPUT"!==this.increment.tagName||this.increment.removeAttribute("disabled"),"BUTTON"!==this.decrement.tagName&&"INPUT"!==this.decrement.tagName||this.decrement.removeAttribute("disabled")):"increment"===e?"BUTTON"!==this.increment.tagName&&"INPUT"!==this.increment.tagName||this.increment.removeAttribute("disabled"):"decrement"===e&&("BUTTON"!==this.decrement.tagName&&"INPUT"!==this.decrement.tagName||this.decrement.removeAttribute("disabled"))}destroy(){this.el.classList.remove("disabled"),this.increment.removeAttribute("disabled"),this.decrement.removeAttribute("disabled"),this.input.removeEventListener("input",this.onInputInputListener),this.increment.removeEventListener("click",this.onIncrementClickListener),this.decrement.removeEventListener("click",this.onDecrementClickListener),window.$hsInputNumberCollection=window.$hsInputNumberCollection.filter((({element:e})=>e.el!==this.el))}static getInstance(e,t){const n=window.$hsInputNumberCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return n?t?n:n.element:null}static autoInit(){window.$hsInputNumberCollection||(window.$hsInputNumberCollection=[]),window.$hsInputNumberCollection&&(window.$hsInputNumberCollection=window.$hsInputNumberCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll("[data-hs-input-number]:not(.--prevent-on-load-init)").forEach((e=>{window.$hsInputNumberCollection.find((t=>{var n;return(null===(n=null==t?void 0:t.element)||void 0===n?void 0:n.el)===e}))||new l(e)}))}}window.addEventListener("load",(()=>{l.autoInit()})),"undefined"!=typeof window&&(window.HSInputNumber=l),t.default=l},961:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t,n){this.el=e,this.options=t,this.events=n,this.el=e,this.options=t,this.events={}}createCollection(e,t){var n;e.push({id:(null===(n=null==t?void 0:t.el)||void 0===n?void 0:n.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={};var n=function n(i){var s=t[i];if(void 0!==s)return s.exports;var r=t[i]={exports:{}};return e[i].call(r.exports,r,r.exports,n),r.exports}(332);return n})()));