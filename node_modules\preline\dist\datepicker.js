!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var s in n)("object"==typeof exports?exports:e)[s]=n[s]}}(self,(()=>{return e={128:function(e,t,n){"use strict";
/*
 * HSDatepicker
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const o=n(292),i=s(n(632)),a=n(191),l=n(292),r=s(n(442)),d=s(n(961));class c extends d.default{constructor(e,t,n){var s,l,r,d,c,u;super(e,t,n);const h=e.getAttribute("data-hs-datepicker")?JSON.parse(e.getAttribute("data-hs-datepicker")):{};this.dataOptions=Object.assign(Object.assign({},h),t);const p=void 0!==(null===(s=this.dataOptions)||void 0===s?void 0:s.removeDefaultStyles)&&(null===(l=this.dataOptions)||void 0===l?void 0:l.removeDefaultStyles);this.updatedStyles=_.mergeWith(p?{}:i.default.defaultStyles,(null===(r=this.dataOptions)||void 0===r?void 0:r.styles)||{},((e,t)=>{if("string"==typeof e&&"string"==typeof t)return`${e} ${t}`}));const m=new Date,v={styles:this.updatedStyles,dateMin:null!==(d=this.dataOptions.dateMin)&&void 0!==d?d:m.toISOString().split("T")[0],dateMax:null!==(c=this.dataOptions.dateMax)&&void 0!==c?c:"2470-12-31",mode:null!==(u=this.dataOptions.mode)&&void 0!==u?u:"default",inputMode:void 0===this.dataOptions.inputMode||this.dataOptions.inputMode},g=(e,t)=>n=>{null==e||e(n),null==t||t(n)},y=e=>{this.hasTime(e)&&this.initCustomTime(e)},f={layouts:{month:a.templates.month},onInit:g(this.dataOptions.onInit,(e=>{"custom-select"!==v.mode||this.dataOptions.inputMode||y(e)})),onShow:g(this.dataOptions.onShow,(e=>{"custom-select"===v.mode&&(this.updateCustomSelects(e),y(e))})),onHide:g(this.dataOptions.onHide,(e=>{"custom-select"===v.mode&&this.destroySelects(e.context.mainElement)})),onUpdate:g(this.dataOptions.onUpdate,(e=>{this.updateCalendar(e.context.mainElement)})),onCreateDateEls:g(this.dataOptions.onCreateDateEls,(e=>{"custom-select"===v.mode&&this.updateCustomSelects(e)})),onChangeToInput:g(this.dataOptions.onChangeToInput,(e=>{if(!e.context.inputElement)return;this.setInputValue(e.context.inputElement,e.context.selectedDates);const t={selectedDates:e.context.selectedDates,selectedTime:e.context.selectedTime,rest:e.context};this.fireEvent("change",t),(0,o.dispatch)("change.hs.datepicker",this.el,t)})),onChangeTime:g(this.dataOptions.onChangeTime,y),onClickYear:g(this.dataOptions.onClickYear,y),onClickMonth:g(this.dataOptions.onClickMonth,y),onClickArrow:g(this.dataOptions.onClickArrow,(e=>{"custom-select"===v.mode&&setTimeout((()=>{this.disableNav(),this.disableOptions(),this.updateCalendar(e.context.mainElement)}))}))},b=Object.assign(Object.assign({},v),{layouts:{default:this.processCustomTemplate(a.templates.default,"default"),multiple:this.processCustomTemplate(a.templates.multiple,"multiple"),year:this.processCustomTemplate(a.templates.year,"default")}});this.vanillaCalendar=new i.default(this.el,_.merge(f,this.dataOptions,b)),this.init()}init(){var e,t;this.createCollection(window.$hsDatepickerCollection,this),this.vanillaCalendar.init(),(null===(e=this.dataOptions)||void 0===e?void 0:e.selectedDates)&&this.setInputValue(this.vanillaCalendar.context.inputElement,this.formatDateArrayToIndividualDates(null===(t=this.dataOptions)||void 0===t?void 0:t.selectedDates))}getTimeParts(e){const[t,n]=e.split(" "),[s,o]=t.split(":");return[s,o,n]}getCurrentMonthAndYear(e){const t=e.querySelector('[data-vc="month"]'),n=e.querySelector('[data-vc="year"]');return{month:+t.getAttribute("data-vc-month"),year:+n.getAttribute("data-vc-year")}}setInputValue(e,t){var n,s,o,i,a,l,r,d;const c=null!==(o=null===(s=null===(n=this.dataOptions)||void 0===n?void 0:n.inputModeOptions)||void 0===s?void 0:s.dateSeparator)&&void 0!==o?o:".",u=null!==(l=null===(a=null===(i=this.dataOptions)||void 0===i?void 0:i.inputModeOptions)||void 0===a?void 0:a.itemsSeparator)&&void 0!==l?l:", ",h=null!==(d=null===(r=this.dataOptions)||void 0===r?void 0:r.selectionDatesMode)&&void 0!==d?d:"single";if(t.length&&t.length>1)if("multiple"===h){const n=[];t.forEach((e=>n.push(this.changeDateSeparator(e,c)))),e.value=n.join(u)}else e.value=[this.changeDateSeparator(t[0],c),this.changeDateSeparator(t[1],c)].join(u);else t.length&&1===t.length?e.value=this.changeDateSeparator(t[0],c):e.value=""}changeDateSeparator(e,t=".",n="-"){return e.split(n).join(t)}formatDateArrayToIndividualDates(e){var t,n;const s=null!==(n=null===(t=this.dataOptions)||void 0===t?void 0:t.selectionDatesMode)&&void 0!==n?n:"single";return e.flatMap((e=>{if("string"==typeof e){const t=e.match(/^(\d{4}-\d{2}-\d{2})\s*[^a-zA-Z0-9]*\s*(\d{4}-\d{2}-\d{2})$/);if(t){const[e,n,o]=t;return"multiple-ranged"===s?[n,o]:((e,t)=>{const n=new Date(e),s=new Date(t),o=[];for(;n<=s;)o.push(n.toISOString().split("T")[0]),n.setDate(n.getDate()+1);return o})(n.trim(),o.trim())}return[e]}return"number"==typeof e?[new Date(e).toISOString().split("T")[0]]:e instanceof Date?[e.toISOString().split("T")[0]]:[]}))}hasTime(e){const{mainElement:t}=e.context,n=t.querySelector("[data-hs-select].--hours"),s=t.querySelector("[data-hs-select].--minutes"),o=t.querySelector("[data-hs-select].--meridiem");return n&&s&&o}createArrowFromTemplate(e,t=!1){if(!t)return e;const n=(0,l.htmlToElement)(e);return(0,l.classToClassList)(t,n),n.outerHTML}concatObjectProperties(e,t){const n={};return new Set([...Object.keys(e||{}),...Object.keys(t||{})]).forEach((s=>{const o=e[s]||"",i=t[s]||"";n[s]=`${o} ${i}`.trim()})),n}updateTemplate(e,t,n){if(!t)return e;const s=JSON.parse(e.match(/data-hs-select='([^']+)'/)[1]),o=this.concatObjectProperties(t,n),i=_.merge(s,o);return e.replace(/data-hs-select='[^']+'/,`data-hs-select='${JSON.stringify(i)}'`)}initCustomTime(e){var t;const{mainElement:n}=e.context,s=this.getTimeParts(null!==(t=e.selectedTime)&&void 0!==t?t:"12:00 PM"),o={hours:n.querySelector("[data-hs-select].--hours"),minutes:n.querySelector("[data-hs-select].--minutes"),meridiem:n.querySelector("[data-hs-select].--meridiem")};Object.entries(o).forEach((([t,o])=>{if(!r.default.getInstance(o,!0)){const i=new r.default(o);i.setValue(s["meridiem"===t?2:"minutes"===t?1:0]),i.el.addEventListener("change.hs.select",(o=>{this.destroySelects(n);const i="hours"===t?o.detail.payload:s[0],a="minutes"===t?o.detail.payload:s[1],l="meridiem"===t?o.detail.payload:s[2];e.set({selectedTime:`${i}:${a} ${l}`},{dates:!1,year:!1,month:!1})}))}}))}initCustomMonths(e){const{mainElement:t}=e.context,n=Array.from(t.querySelectorAll(".--single-month"));n.length&&n.forEach(((n,s)=>{const o=n.querySelector("[data-hs-select].--month");if(r.default.getInstance(o,!0))return!1;const i=new r.default(o),{month:a,year:l}=this.getCurrentMonthAndYear(n);i.setValue(`${a}`),i.el.addEventListener("change.hs.select",(n=>{this.destroySelects(t),e.set({selectedMonth:+n.detail.payload-s<0?11:+n.detail.payload-s,selectedYear:+n.detail.payload-s<0?+l-1:l},{dates:!1,time:!1})}))}))}initCustomYears(e){const{mainElement:t}=e.context,n=Array.from(t.querySelectorAll(".--single-month"));n.length&&n.forEach((n=>{const s=n.querySelector("[data-hs-select].--year");if(r.default.getInstance(s,!0))return!1;const o=new r.default(s),{month:i,year:a}=this.getCurrentMonthAndYear(n);o.setValue(`${a}`),o.el.addEventListener("change.hs.select",(n=>{const{dateMax:s,displayMonthsCount:o}=this.vanillaCalendar.context,a=new Date(s).getFullYear(),l=new Date(s).getMonth();this.destroySelects(t),e.set({selectedMonth:i>l-o&&+n.detail.payload===a?l-o+1:i,selectedYear:n.detail.payload},{dates:!1,time:!1})}))}))}generateCustomTimeMarkup(){var e,t,n,s;const o=null===(e=this.updatedStyles)||void 0===e?void 0:e.customSelect,i=o?this.updateTemplate(a.templates.hours,(null==o?void 0:o.shared)||{},(null==o?void 0:o.hours)||{}):a.templates.hours,l=o?this.updateTemplate(a.templates.minutes,(null==o?void 0:o.shared)||{},(null==o?void 0:o.minutes)||{}):a.templates.minutes,r=o?this.updateTemplate(a.templates.meridiem,(null==o?void 0:o.shared)||{},(null==o?void 0:o.meridiem)||{}):a.templates.meridiem;return`<div class="--time">${null!==(s=null===(n=null===(t=null==this?void 0:this.dataOptions)||void 0===t?void 0:t.templates)||void 0===n?void 0:n.time)&&void 0!==s?s:`\n\t\t\t<div class="pt-3 flex justify-center items-center gap-x-2">\n        ${i}\n        <span class="text-gray-800 dark:text-white">:</span>\n        ${l}\n        ${r}\n      </div>\n\t\t`}</div>`}generateCustomMonthMarkup(){var e,t,n;const s=null!==(t=null===(e=null==this?void 0:this.dataOptions)||void 0===e?void 0:e.mode)&&void 0!==t?t:"default",o=null===(n=this.updatedStyles)||void 0===n?void 0:n.customSelect,i=o?this.updateTemplate(a.templates.months,(null==o?void 0:o.shared)||{},(null==o?void 0:o.months)||{}):a.templates.months;return"custom-select"===s?i:"<#Month />"}generateCustomYearMarkup(){var e,t,n,s,o,i,l;if("custom-select"===(null!==(t=null===(e=null==this?void 0:this.dataOptions)||void 0===e?void 0:e.mode)&&void 0!==t?t:"default")){const e=new Date,t=null!==(s=null===(n=null==this?void 0:this.dataOptions)||void 0===n?void 0:n.dateMin)&&void 0!==s?s:e.toISOString().split("T")[0],r=null!==(i=null===(o=null==this?void 0:this.dataOptions)||void 0===o?void 0:o.dateMax)&&void 0!==i?i:"2470-12-31",d=new Date(t),c=new Date(r),u=d.getFullYear(),h=c.getFullYear(),p=()=>{let e="";for(let t=u;t<=h;t++)e+=`<option value="${t}">${t}</option>`;return e},m=a.templates.years(p()),v=null===(l=this.updatedStyles)||void 0===l?void 0:l.customSelect;return v?this.updateTemplate(m,(null==v?void 0:v.shared)||{},(null==v?void 0:v.years)||{}):m}return"<#Year />"}generateCustomArrowPrevMarkup(){var e,t;return(null===(t=null===(e=null==this?void 0:this.dataOptions)||void 0===e?void 0:e.templates)||void 0===t?void 0:t.arrowPrev)?this.createArrowFromTemplate(this.dataOptions.templates.arrowPrev,this.updatedStyles.arrowPrev):"<#ArrowPrev [month] />"}generateCustomArrowNextMarkup(){var e,t;return(null===(t=null===(e=null==this?void 0:this.dataOptions)||void 0===e?void 0:e.templates)||void 0===t?void 0:t.arrowNext)?this.createArrowFromTemplate(this.dataOptions.templates.arrowNext,this.updatedStyles.arrowNext):"<#ArrowNext [month] />"}parseCustomTime(e){return e=e.replace(/<#CustomTime\s*\/>/g,this.generateCustomTimeMarkup())}parseCustomMonth(e){return e=e.replace(/<#CustomMonth\s*\/>/g,this.generateCustomMonthMarkup())}parseCustomYear(e){return e=e.replace(/<#CustomYear\s*\/>/g,this.generateCustomYearMarkup())}parseArrowPrev(e){return e=e.replace(/<#CustomArrowPrev\s*\/>/g,this.generateCustomArrowPrevMarkup())}parseArrowNext(e){return e=e.replace(/<#CustomArrowNext\s*\/>/g,this.generateCustomArrowNextMarkup())}processCustomTemplate(e,t){var n,s,o,i;const a="default"===t?null===(s=null===(n=null==this?void 0:this.dataOptions)||void 0===n?void 0:n.layouts)||void 0===s?void 0:s.default:null===(i=null===(o=null==this?void 0:this.dataOptions)||void 0===o?void 0:o.layouts)||void 0===i?void 0:i.multiple,l=this.parseCustomMonth(null!=a?a:e),r=this.parseCustomYear(l),d=this.parseCustomTime(r),c=this.parseArrowPrev(d);return this.parseArrowNext(c)}disableOptions(){const{mainElement:e,dateMax:t,displayMonthsCount:n}=this.vanillaCalendar.context,s=new Date(t);Array.from(e.querySelectorAll(".--single-month")).forEach(((e,t)=>{var o;const i=+(null===(o=e.querySelector('[data-vc="year"]'))||void 0===o?void 0:o.getAttribute("data-vc-year")),a=e.querySelectorAll("[data-hs-select].--month option"),l=e.querySelectorAll("[data-hs-select-dropdown] [data-value]"),r=e=>+e.getAttribute("data-value")>s.getMonth()-n+t+1&&i===s.getFullYear();Array.from(a).forEach((e=>e.toggleAttribute("disabled",r(e)))),Array.from(l).forEach((e=>e.classList.toggle("disabled",r(e))))}))}disableNav(){const{mainElement:e,dateMax:t,selectedYear:n,selectedMonth:s,displayMonthsCount:o}=this.vanillaCalendar.context,i=new Date(t).getFullYear(),a=e.querySelector('[data-vc-arrow="next"]');a.style.visibility=n===i&&s+o>11?"hidden":""}destroySelects(e){Array.from(e.querySelectorAll("[data-hs-select]")).forEach((e=>{const t=r.default.getInstance(e,!0);t&&t.element.destroy()}))}updateSelect(e,t){const n=r.default.getInstance(e,!0);n&&n.element.setValue(t)}updateCalendar(e){const t=e.querySelectorAll(".--single-month");t.length&&t.forEach((e=>{const{month:t,year:n}=this.getCurrentMonthAndYear(e);this.updateSelect(e.querySelector("[data-hs-select].--month"),`${t}`),this.updateSelect(e.querySelector("[data-hs-select].--year"),`${n}`)}))}updateCustomSelects(e){setTimeout((()=>{this.disableOptions(),this.disableNav(),this.initCustomMonths(e),this.initCustomYears(e)}))}getCurrentState(){return{selectedDates:this.vanillaCalendar.selectedDates,selectedTime:this.vanillaCalendar.selectedTime}}destroy(){this.vanillaCalendar&&(this.vanillaCalendar.destroy(),this.vanillaCalendar=null),window.$hsDatepickerCollection=window.$hsDatepickerCollection.filter((({element:e})=>e.el!==this.el))}static getInstance(e,t){const n=window.$hsDatepickerCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return n?t?n:n.element.el:null}static autoInit(){window.$hsDatepickerCollection||(window.$hsDatepickerCollection=[]),document.querySelectorAll(".hs-datepicker:not(.--prevent-on-load-init)").forEach((e=>{window.$hsDatepickerCollection.find((t=>{var n;return(null===(n=null==t?void 0:t.element)||void 0===n?void 0:n.el)===e}))||new c(e)}))}}window.addEventListener("load",(()=>{c.autoInit()})),"undefined"!=typeof window&&(window.HSDatepicker=c),t.default=c},191:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.templates=void 0,t.templates={default:'<div class="--single-month flex flex-col overflow-hidden">\n    <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3" data-vc="header">\n      <div class="col-span-1">\n        <#CustomArrowPrev />\n      </div>\n      <div class="col-span-3 flex justify-center items-center gap-x-1">\n        <#CustomMonth />\n        <span class="text-gray-800 dark:text-neutral-200">/</span>\n        <#CustomYear />\n      </div>\n      <div class="col-span-1 flex justify-end">\n        <#CustomArrowNext />\n      </div>\n    </div>\n    <div data-vc="wrapper">\n      <div data-vc="content">\n        <#Week />\n        <#Dates />\n      </div>\n    </div>\n  </div>',multiple:'<div class="relative flex flex-col overflow-hidden">\n    <div class="absolute top-2 start-2">\n      <#CustomArrowPrev />\n    </div>\n    <div class="absolute top-2 end-2">\n      <#CustomArrowNext />\n    </div>\n    <div class="sm:flex" data-vc="grid">\n      <#Multiple>\n        <div class="p-3 space-y-0.5 --single-month" data-vc="column">\n          <div class="pb-3" data-vc="header">\n            <div class="flex justify-center items-center gap-x-1" data-vc-header="content">\n              <#CustomMonth />\n              <span class="text-gray-800 dark:text-neutral-200">/</span>\n              <#CustomYear />\n            </div>\n          </div>\n          <div data-vc="wrapper">\n            <div data-vc="content">\n              <#Week />\n              <#Dates />\n            </div>\n          </div>\n        </div>\n      <#/Multiple>\n    </div>\n  </div>',year:'<div class="relative bg-white dark:bg-neutral-900" data-vc="header" role="toolbar">\n    <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3" data-vc="header">\n      <div class="col-span-1">\n        <#CustomArrowPrev />\n      </div>\n      <div class="col-span-3 flex justify-center items-center gap-x-1">\n        <#Month />\n        <span class="text-gray-800 dark:text-neutral-200">/</span>\n        <#Year />\n      </div>\n      <div class="col-span-1 flex justify-end">\n        <#CustomArrowNext />\n      </div>\n    </div>\n  </div>\n  <div data-vc="wrapper">\n    <div data-vc="content">\n      <#Years />\n    </div>\n  </div>',month:'<div class="pb-3" data-vc="header" role="toolbar">\n    <div class="flex justify-center items-center gap-x-1" data-vc-header="content">\n      <#Month />\n      <span class="text-gray-800 dark:text-neutral-200">/</span>\n      <#Year />\n    </div>\n  </div>\n  <div data-vc="wrapper">\n    <div data-vc="content">\n      <#Months />\n    </div>\n  </div>',years:e=>`<div class="relative">\n      <span class="hidden" data-vc="year"></span>\n      <select data-hs-select='{\n          "placeholder": "Select year",\n          "dropdownScope": "parent",\n          "dropdownVerticalFixedPlacement": "bottom",\n          "toggleTag": "<button type=\\"button\\"><span data-title></span></button>",\n          "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",\n          "dropdownClasses": "mt-2 z-50 w-20 max-h-60 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",\n          "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",\n          "optionTemplate": "<div class=\\"flex justify-between items-center w-full\\"><span data-title></span><span class=\\"hidden hs-selected:block\\"><svg class=\\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\\" xmlns=\\"http:.w3.org/2000/svg\\" width=\\"24\\" height=\\"24\\" viewBox=\\"0 0 24 24\\" fill=\\"none\\" stroke=\\"currentColor\\" stroke-width=\\"2\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\"><polyline points=\\"20 6 9 17 4 12\\"/></svg></span></div>"\n        }' class="hidden --year --prevent-on-load-init">\n        ${e}\n      </select>\n    </div>`,months:'<div class="relative">\n    <span class="hidden" data-vc="month"></span>\n    <select data-hs-select=\'{\n        "placeholder": "Select month",\n        "dropdownScope": "parent",\n        "dropdownVerticalFixedPlacement": "bottom",\n        "toggleTag": "<button type=\\"button\\"><span data-title></span></button>",\n        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",\n        "dropdownClasses": "mt-2 z-50 w-32 max-h-60 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",\n        "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg hs-select-disabled:opacity-50 hs-select-disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",\n        "optionTemplate": "<div class=\\"flex justify-between items-center w-full\\"><span data-title></span><span class=\\"hidden hs-selected:block\\"><svg class=\\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\\" xmlns=\\"http:.w3.org/2000/svg\\" width=\\"24\\" height=\\"24\\" viewBox=\\"0 0 24 24\\" fill=\\"none\\" stroke=\\"currentColor\\" stroke-width=\\"2\\" stroke-linecap=\\"round\\" stroke-linejoin=\\"round\\"><polyline points=\\"20 6 9 17 4 12\\"/></svg></span></div>"\n      }\' class="hidden --month --prevent-on-load-init">\n      <option value="0">January</option>\n      <option value="1">February</option>\n      <option value="2">March</option>\n      <option value="3">April</option>\n      <option value="4">May</option>\n      <option value="5">June</option>\n      <option value="6">July</option>\n      <option value="7">August</option>\n      <option value="8">September</option>\n      <option value="9">October</option>\n      <option value="10">November</option>\n      <option value="11">December</option>\n    </select>\n  </div>',hours:'<div class="relative">\n    <select class="--hours hidden" data-hs-select=\'{\n      "placeholder": "Select option...",\n      "dropdownVerticalFixedPlacement": "top",\n      "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1 px-2 pe-6 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400",\n      "dropdownClasses": "mt-2 z-50 w-full min-w-24 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",\n      "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:hs-selected:bg-gray-700 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",\n      "optionTemplate": "<div class=\\"flex justify-between items-center w-full\\"><span data-title></span></div>"\n    }\'>\n      <option value="01">01</option>\n      <option value="02">02</option>\n      <option value="03">03</option>\n      <option value="04">04</option>\n      <option value="05">05</option>\n      <option value="06">06</option>\n      <option value="07">07</option>\n      <option value="08">08</option>\n      <option value="09">09</option>\n      <option value="10">10</option>\n      <option value="11">11</option>\n      <option value="12" selected>12</option>\n    </select>\n    <div class="absolute top-1/2 end-2 -translate-y-1/2">\n      <svg class="shrink-0 size-3 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg>\n    </div>\n  </div>',minutes:'<div class="relative">\n    <select class="--minutes hidden" data-hs-select=\'{\n      "placeholder": "Select option...",\n      "dropdownVerticalFixedPlacement": "top",\n      "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1 px-2 pe-6 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400",\n      "dropdownClasses": "mt-2 z-50 w-full min-w-24 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",\n      "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:hs-selected:bg-gray-700 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",\n      "optionTemplate": "<div class=\\"flex justify-between items-center w-full\\"><span data-title></span></div>"\n    }\'>\n      <option value="00" selected>00</option>\n      <option value="01">01</option>\n      <option value="02">02</option>\n      <option value="03">03</option>\n      <option value="04">04</option>\n      <option value="05">05</option>\n      <option value="06">06</option>\n      <option value="07">07</option>\n      <option value="08">08</option>\n      <option value="09">09</option>\n      <option value="10">10</option>\n      <option value="11">11</option>\n      <option value="12">12</option>\n      <option value="13">13</option>\n      <option value="14">14</option>\n      <option value="15">15</option>\n      <option value="16">16</option>\n      <option value="17">17</option>\n      <option value="18">18</option>\n      <option value="19">19</option>\n      <option value="20">20</option>\n      <option value="21">21</option>\n      <option value="22">22</option>\n      <option value="23">23</option>\n      <option value="24">24</option>\n      <option value="25">25</option>\n      <option value="26">26</option>\n      <option value="27">27</option>\n      <option value="28">28</option>\n      <option value="29">29</option>\n      <option value="30">30</option>\n      <option value="31">31</option>\n      <option value="32">32</option>\n      <option value="33">33</option>\n      <option value="34">34</option>\n      <option value="35">35</option>\n      <option value="36">36</option>\n      <option value="37">37</option>\n      <option value="38">38</option>\n      <option value="39">39</option>\n      <option value="40">40</option>\n      <option value="41">41</option>\n      <option value="42">42</option>\n      <option value="43">43</option>\n      <option value="44">44</option>\n      <option value="45">45</option>\n      <option value="46">46</option>\n      <option value="47">47</option>\n      <option value="48">48</option>\n      <option value="49">49</option>\n      <option value="50">50</option>\n      <option value="51">51</option>\n      <option value="52">52</option>\n      <option value="53">53</option>\n      <option value="54">54</option>\n      <option value="55">55</option>\n      <option value="56">56</option>\n      <option value="57">57</option>\n      <option value="58">58</option>\n      <option value="59">59</option>\n    </select>\n    <div class="absolute top-1/2 end-2 -translate-y-1/2">\n      <svg class="shrink-0 size-3 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg>\n    </div>\n  </div>',meridiem:'<div class="relative">\n    <select class="--meridiem hidden" data-hs-select=\'{\n      "placeholder": "Select option...",\n      "dropdownVerticalFixedPlacement": "top",\n      "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1 px-2 pe-6 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400",\n      "dropdownClasses": "mt-2 z-50 w-full min-w-24 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",\n      "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:hs-selected:bg-gray-700 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",\n      "optionTemplate": "<div class=\\"flex justify-between items-center w-full\\"><span data-title></span></div>"\n    }\'>\n      <option value="PM" selected>PM</option>\n      <option value="AM">AM</option>\n    </select>\n    <div class="absolute top-1/2 end-2 -translate-y-1/2">\n      <svg class="shrink-0 size-3 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg>\n    </div>\n  </div>'}},223:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BREAKPOINTS=t.COMBO_BOX_ACCESSIBILITY_KEY_SET=t.SELECT_ACCESSIBILITY_KEY_SET=t.TABS_ACCESSIBILITY_KEY_SET=t.OVERLAY_ACCESSIBILITY_KEY_SET=t.DROPDOWN_ACCESSIBILITY_KEY_SET=t.POSITIONS=void 0,t.POSITIONS={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"},t.DROPDOWN_ACCESSIBILITY_KEY_SET=["Escape","ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Home","End","Enter"],t.OVERLAY_ACCESSIBILITY_KEY_SET=["Escape","Tab"],t.TABS_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End"],t.SELECT_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter","Space","Tab"],t.COMBO_BOX_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter"],t.BREAKPOINTS={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},292:function(e,t){"use strict";
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0,t.stringToBoolean=e=>"true"===e,t.getClassProperty=(e,t,n="")=>(window.getComputedStyle(e).getPropertyValue(t)||n).replace(" ",""),t.getClassPropertyAlt=(e,t,n="")=>{let s="";return e.classList.forEach((e=>{e.includes(t)&&(s=e)})),s.match(/:(.*)]/)?s.match(/:(.*)]/)[1]:n};const n=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=n,t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let s=n(e);"auto"!==s&&(s=parseInt(s,10),s>t&&(t=s))})),t},t.isDirectChild=(e,t)=>{const n=e.children;for(let e=0;e<n.length;e++)if(n[e]===t)return!0;return!1},t.isEnoughSpace=(e,t,n="auto",s=10,o=null)=>{const i=t.getBoundingClientRect(),a=o?o.getBoundingClientRect():null,l=window.innerHeight,r=a?i.top-a.top:i.top,d=(o?a.bottom:l)-i.bottom,c=e.clientHeight+s;return"bottom"===n?d>=c:"top"===n?r>=c:r>=c||d>=c},t.isFocused=e=>document.activeElement===e,t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement,t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform),t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform),t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],n=e.trim().slice(-1);if("{"===t&&"}"===n||"["===t&&"]"===n)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const s=e=>!!e&&("none"===window.getComputedStyle(e).display||s(e.parentElement));t.isParentOrElementHidden=s,t.isScrollable=e=>{const t=window.getComputedStyle(e),n=t.overflowY,s=t.overflowX,o=("scroll"===n||"auto"===n)&&e.scrollHeight>e.clientHeight,i=("scroll"===s||"auto"===s)&&e.scrollWidth>e.clientWidth;return o||i},t.debounce=(e,t=200)=>{let n;return(...s)=>{clearTimeout(n),n=setTimeout((()=>{e.apply(this,s)}),t)}},t.dispatch=(e,t,n=null)=>{const s=new CustomEvent(e,{detail:{payload:n},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(s)},t.afterTransition=(e,t)=>{const n=()=>{t(),e.removeEventListener("transitionend",n,!0)},s=window.getComputedStyle(e),o=s.getPropertyValue("transition-duration");"none"!==s.getPropertyValue("transition-property")&&parseFloat(o)>0?e.addEventListener("transitionend",n,!0):t()},t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild},t.classToClassList=(e,t,n=" ",s="add")=>{e.split(n).forEach((e=>"add"===s?t.classList.add(e):t.classList.remove(e)))};const o={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=o},359:function(e,t){
/*! name: vanilla-calendar-pro v3.0.4 | url: https://github.com/uvarov-frontend/vanilla-calendar-pro */
!function(e){"use strict";var t=Object.defineProperty,n=Object.defineProperties,s=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,l=(e,n,s)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[n]=s,r=(e,t)=>{for(var n in t||(t={}))i.call(t,n)&&l(e,n,t[n]);if(o)for(var n of o(t))a.call(t,n)&&l(e,n,t[n]);return e},d=(e,t,n)=>(l(e,"symbol"!=typeof t?t+"":t,n),n);const c=e=>`${e} is not found, check the first argument passed to new Calendar.`,u='The calendar has not been initialized, please initialize it using the "init()" method first.',h="You specified an incorrect language label or did not specify the required number of values ​​for «locale.weekdays» or «locale.months».",p="The value of the time property can be: false, 12 or 24.",m="For the «multiple» calendar type, the «displayMonthsCount» parameter can have a value from 2 to 12, and for all others it cannot be greater than 1.",v=(e,t,n)=>{e.context[t]=n},g=e=>{e.context.isShowInInputMode&&e.context.currentType&&(e.context.mainElement.dataset.vcCalendarHidden="",v(e,"isShowInInputMode",!1),e.context.cleanupHandlers[0]&&(e.context.cleanupHandlers.forEach((e=>e())),v(e,"cleanupHandlers",[])),e.onHide&&e.onHide(e))};function y(e){if(!e||!e.getBoundingClientRect)return{top:0,bottom:0,left:0,right:0};const t=e.getBoundingClientRect(),n=document.documentElement;return{bottom:t.bottom,right:t.right,top:t.top+window.scrollY-n.clientTop,left:t.left+window.scrollX-n.clientLeft}}function f(){return{vw:Math.max(document.documentElement.clientWidth||0,window.innerWidth||0),vh:Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)}}function b(e){const{top:t,left:n}={left:window.scrollX||document.documentElement.scrollLeft||0,top:window.scrollY||document.documentElement.scrollTop||0},{top:s,left:o}=y(e),{vh:i,vw:a}=f(),l=s-t,r=o-n;return{top:l,bottom:i-(l+e.clientHeight),left:r,right:a-(r+e.clientWidth)}}function w(e,t,n=5){const s={top:!0,bottom:!0,left:!0,right:!0},o=[];if(!t||!e)return{canShow:s,parentPositions:o};const{bottom:i,top:a}=b(e),{top:l,left:r}=y(e),{height:d,width:c}=t.getBoundingClientRect(),{vh:u,vw:h}=f(),p=h/2,m=u/2;return[{condition:l<m,position:"top"},{condition:l>m,position:"bottom"},{condition:r<p,position:"left"},{condition:r>p,position:"right"}].forEach((({condition:e,position:t})=>{e&&o.push(t)})),Object.assign(s,{top:d<=a-n,bottom:d<=i-n,left:c<=r,right:c<=h-r}),{canShow:s,parentPositions:o}}const x=(e,t)=>{var n;e.popups&&(null==(n=Object.entries(e.popups))||n.forEach((([n,s])=>((e,t,n,s)=>{var o;const i=s.querySelector(`[data-vc-date="${t}"]`),a=null==i?void 0:i.querySelector("[data-vc-date-btn]");if(!i||!a)return;if((null==n?void 0:n.modifier)&&a.classList.add(...n.modifier.trim().split(" ")),!(null==n?void 0:n.html))return;const l=document.createElement("div");l.className=e.styles.datePopup,l.dataset.vcDatePopup="",l.innerHTML=e.sanitizerHTML(n.html),a.ariaExpanded="true",a.ariaLabel=`${a.ariaLabel}, ${null==(o=null==l?void 0:l.textContent)?void 0:o.replace(/^\s+|\s+(?=\s)|\s+$/g,"").replace(/&nbsp;/g," ")}`,i.appendChild(l),requestAnimationFrame((()=>{if(!l)return;const{canShow:e}=w(i,l),t=e.bottom?i.offsetHeight:-l.offsetHeight,n=e.left&&!e.right?i.offsetWidth-l.offsetWidth/2:!e.left&&e.right?l.offsetWidth/2:0;Object.assign(l.style,{left:`${n}px`,top:`${t}px`})}))})(e,n,s,t))))},M=e=>new Date(`${e}T00:00:00`),T=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,S=e=>e.reduce(((e,t)=>{if(t instanceof Date||"number"==typeof t){const n=t instanceof Date?t:new Date(t);e.push(n.toISOString().substring(0,10))}else t.match(/^(\d{4}-\d{2}-\d{2})$/g)?e.push(t):t.replace(/(\d{4}-\d{2}-\d{2}).*?(\d{4}-\d{2}-\d{2})/g,((t,n,s)=>{const o=M(n),i=M(s),a=new Date(o.getTime());for(;a<=i;a.setDate(a.getDate()+1))e.push(T(a));return t}));return e}),[]),E=(e,t,n,s="")=>{t?e.setAttribute(n,s):e.getAttribute(n)===s&&e.removeAttribute(n)},C=(e,t,n,s,o,i,a)=>{var l,r,d,c;const u=M(e.context.displayDateMin)>M(i)||M(e.context.displayDateMax)<M(i)||(null==(l=e.context.disableDates)?void 0:l.includes(i))||!e.selectionMonthsMode&&"current"!==a||!e.selectionYearsMode&&M(i).getFullYear()!==t;E(n,u,"data-vc-date-disabled"),s&&E(s,u,"aria-disabled","true"),s&&E(s,u,"tabindex","-1"),E(n,!e.disableToday&&e.context.dateToday===i,"data-vc-date-today"),E(n,!e.disableToday&&e.context.dateToday===i,"aria-current","date"),E(n,null==(r=e.selectedWeekends)?void 0:r.includes(o),"data-vc-date-weekend");const h=(null==(d=e.selectedHolidays)?void 0:d[0])?S(e.selectedHolidays):[];if(E(n,h.includes(i),"data-vc-date-holiday"),(null==(c=e.context.selectedDates)?void 0:c.includes(i))?(n.setAttribute("data-vc-date-selected",""),s&&s.setAttribute("aria-selected","true"),e.context.selectedDates.length>1&&"multiple-ranged"===e.selectionDatesMode&&(e.context.selectedDates[0]===i&&e.context.selectedDates[e.context.selectedDates.length-1]===i?n.setAttribute("data-vc-date-selected","first-and-last"):e.context.selectedDates[0]===i?n.setAttribute("data-vc-date-selected","first"):e.context.selectedDates[e.context.selectedDates.length-1]===i&&n.setAttribute("data-vc-date-selected","last"),e.context.selectedDates[0]!==i&&e.context.selectedDates[e.context.selectedDates.length-1]!==i&&n.setAttribute("data-vc-date-selected","middle"))):n.hasAttribute("data-vc-date-selected")&&(n.removeAttribute("data-vc-date-selected"),s&&s.removeAttribute("aria-selected")),!e.context.disableDates.includes(i)&&e.enableEdgeDatesOnly&&e.context.selectedDates.length>1&&"multiple-ranged"===e.selectionDatesMode){const t=M(e.context.selectedDates[0]),s=M(e.context.selectedDates[e.context.selectedDates.length-1]),o=M(i);E(n,o>t&&o<s,"data-vc-date-selected","middle")}},D=(e,t)=>{const n=M(e),s=(n.getDay()-t+7)%7;n.setDate(n.getDate()+4-s);const o=new Date(n.getFullYear(),0,1),i=Math.ceil(((+n-+o)/864e5+1)/7);return{year:n.getFullYear(),week:i}},k=(e,t,n,s,o,i)=>{const a=M(o).getDay(),l="string"==typeof e.locale&&e.locale.length?e.locale:"en",r=document.createElement("div");let d;r.className=e.styles.date,r.dataset.vcDate=o,r.dataset.vcDateMonth=i,r.dataset.vcDateWeekDay=String(a),("current"===i||e.displayDatesOutside)&&(d=document.createElement("button"),d.className=e.styles.dateBtn,d.type="button",d.role="gridcell",d.ariaLabel=((e,t,n)=>new Date(`${e}T00:00:00.000Z`).toLocaleString(t,n))(o,l,{dateStyle:"long",timeZone:"UTC"}),d.dataset.vcDateBtn="",d.innerText=String(s),r.appendChild(d)),e.enableWeekNumbers&&((e,t,n)=>{const s=D(n,e.firstWeekday);s&&(t.dataset.vcDateWeekNumber=String(s.week))})(e,r,o),((e,t,n)=>{var s,o,i,a,l;const r=null==(s=e.disableWeekdays)?void 0:s.includes(n),d=e.disableAllDates&&!!(null==(o=e.context.enableDates)?void 0:o[0]);!r&&!d||(null==(i=e.context.enableDates)?void 0:i.includes(t))||(null==(a=e.context.disableDates)?void 0:a.includes(t))||(e.context.disableDates.push(t),null==(l=e.context.disableDates)||l.sort(((e,t)=>+new Date(e)-+new Date(t))))})(e,o,a),C(e,t,r,d,a,o,i),n.appendChild(r),e.onCreateDateEls&&e.onCreateDateEls(e,r)},I=e=>{const t=new Date(e.context.selectedYear,e.context.selectedMonth,1),n=e.context.mainElement.querySelectorAll('[data-vc="dates"]'),s=e.context.mainElement.querySelectorAll('[data-vc-week="numbers"]');n.forEach(((n,o)=>{e.selectionDatesMode||(n.dataset.vcDatesDisabled=""),n.textContent="";const i=new Date(t);i.setMonth(i.getMonth()+o);const a=i.getMonth(),l=i.getFullYear(),r=(new Date(l,a,1).getDay()-e.firstWeekday+7)%7,d=new Date(l,a+1,0).getDate();((e,t,n,s,o)=>{let i=new Date(n,s,0).getDate()-(o-1);const a=0===s?n-1:n,l=0===s?12:s<10?`0${s}`:s;for(let s=o;s>0;s--,i++)k(e,n,t,i,`${a}-${l}-${i}`,"prev")})(e,n,l,a,r),((e,t,n,s,o)=>{for(let i=1;i<=n;i++){const n=new Date(s,o,i);k(e,s,t,i,T(n),"current")}})(e,n,d,l,a),((e,t,n,s,o,i)=>{const a=i+n,l=7*Math.ceil(a/7)-a,r=o+1===12?s+1:s,d=o+1===12?"01":o+2<10?`0${o+2}`:o+2;for(let n=1;n<=l;n++){const o=n<10?`0${n}`:String(n);k(e,s,t,n,`${r}-${d}-${o}`,"next")}})(e,n,d,l,a,r),x(e,n),((e,t,n,s,o)=>{if(!e.enableWeekNumbers)return;s.textContent="";const i=document.createElement("b");i.className=e.styles.weekNumbersTitle,i.innerText="#",i.dataset.vcWeekNumbers="title",s.appendChild(i);const a=document.createElement("div");a.className=e.styles.weekNumbersContent,a.dataset.vcWeekNumbers="content",s.appendChild(a);const l=document.createElement("button");l.type="button",l.className=e.styles.weekNumber;const r=o.querySelectorAll("[data-vc-date]"),d=Math.ceil((t+n)/7);for(let t=0;t<d;t++){const n=r[0===t?6:7*t].dataset.vcDate,s=D(n,e.firstWeekday);if(!s)return;const o=l.cloneNode(!0);o.innerText=String(s.week),o.dataset.vcWeekNumber=String(s.week),o.dataset.vcWeekYear=String(s.year),o.role="rowheader",o.ariaLabel=`${s.week}`,a.appendChild(o)}})(e,r,d,s[o],n)}))},A=e=>`\n  <div class="${e.styles.header}" data-vc="header" role="toolbar" aria-label="${e.labels.navigation}">\n    <#ArrowPrev [month] />\n    <div class="${e.styles.headerContent}" data-vc-header="content">\n      <#Month />\n      <#Year />\n    </div>\n    <#ArrowNext [month] />\n  </div>\n  <div class="${e.styles.wrapper}" data-vc="wrapper">\n    <#WeekNumbers />\n    <div class="${e.styles.content}" data-vc="content">\n      <#Week />\n      <#Dates />\n      <#DateRangeTooltip />\n    </div>\n  </div>\n  <#ControlTime />\n`,L=e=>`\n  <div class="${e.styles.header}" data-vc="header" role="toolbar" aria-label="${e.labels.navigation}">\n    <div class="${e.styles.headerContent}" data-vc-header="content">\n      <#Month />\n      <#Year />\n    </div>\n  </div>\n  <div class="${e.styles.wrapper}" data-vc="wrapper">\n    <div class="${e.styles.content}" data-vc="content">\n      <#Months />\n    </div>\n  </div>\n`,O=e=>`\n  <div class="${e.styles.controls}" data-vc="controls" role="toolbar" aria-label="${e.labels.navigation}">\n    <#ArrowPrev [month] />\n    <#ArrowNext [month] />\n  </div>\n  <div class="${e.styles.grid}" data-vc="grid">\n    <#Multiple>\n      <div class="${e.styles.column}" data-vc="column" role="region">\n        <div class="${e.styles.header}" data-vc="header">\n          <div class="${e.styles.headerContent}" data-vc-header="content">\n            <#Month />\n            <#Year />\n          </div>\n        </div>\n        <div class="${e.styles.wrapper}" data-vc="wrapper">\n          <#WeekNumbers />\n          <div class="${e.styles.content}" data-vc="content">\n            <#Week />\n            <#Dates />\n          </div>\n        </div>\n      </div>\n    <#/Multiple>\n    <#DateRangeTooltip />\n  </div>\n  <#ControlTime />\n`,$=e=>`\n  <div class="${e.styles.header}" data-vc="header" role="toolbar" aria-label="${e.labels.navigation}">\n    <#ArrowPrev [year] />\n    <div class="${e.styles.headerContent}" data-vc-header="content">\n      <#Month />\n      <#Year />\n    </div>\n    <#ArrowNext [year] />\n  </div>\n  <div class="${e.styles.wrapper}" data-vc="wrapper">\n    <div class="${e.styles.content}" data-vc="content">\n      <#Years />\n    </div>\n  </div>\n`,P={ArrowNext:(e,t)=>`<button type="button" class="${e.styles.arrowNext}" data-vc-arrow="next" aria-label="${e.labels.arrowNext[t]}"></button>`,ArrowPrev:(e,t)=>`<button type="button" class="${e.styles.arrowPrev}" data-vc-arrow="prev" aria-label="${e.labels.arrowPrev[t]}"></button>`,ControlTime:e=>e.selectionTimeMode?`<div class="${e.styles.time}" data-vc="time" role="group" aria-label="${e.labels.selectingTime}"></div>`:"",Dates:e=>`<div class="${e.styles.dates}" data-vc="dates" role="grid" aria-live="assertive" aria-label="${e.labels.dates}" ${"multiple"===e.type?"aria-multiselectable":""}></div>`,DateRangeTooltip:e=>e.onCreateDateRangeTooltip?`<div class="${e.styles.dateRangeTooltip}" data-vc-date-range-tooltip="hidden"></div>`:"",Month:e=>`<button type="button" class="${e.styles.month}" data-vc="month"></button>`,Months:e=>`<div class="${e.styles.months}" data-vc="months" role="grid" aria-live="assertive" aria-label="${e.labels.months}"></div>`,Week:e=>`<div class="${e.styles.week}" data-vc="week" role="row" aria-label="${e.labels.week}"></div>`,WeekNumbers:e=>e.enableWeekNumbers?`<div class="${e.styles.weekNumbers}" data-vc-week="numbers" role="row" aria-label="${e.labels.weekNumber}"></div>`:"",Year:e=>`<button type="button" class="${e.styles.year}" data-vc="year"></button>`,Years:e=>`<div class="${e.styles.years}" data-vc="years" role="grid" aria-live="assertive" aria-label="${e.labels.years}"></div>`},Y=(e,t)=>t.replace(/[\n\t]/g,"").replace(/<#(?!\/?Multiple)(.*?)>/g,((t,n)=>{const s=(n.match(/\[(.*?)\]/)||[])[1],o=n.replace(/[/\s\n\t]|\[(.*?)\]/g,""),i=P[o],a=i?i(e,null!=s?s:null):"";return e.sanitizerHTML(a)})).replace(/[\n\t]/g,""),N=(e,t)=>{const n={default:A,month:L,year:$,multiple:O};if(Object.keys(n).forEach((t=>{const s=t;e.layouts[s].length||(e.layouts[s]=n[s](e))})),e.context.mainElement.className=e.styles.calendar,e.context.mainElement.dataset.vc="calendar",e.context.mainElement.dataset.vcType=e.context.currentType,e.context.mainElement.role="application",e.context.mainElement.tabIndex=0,e.context.mainElement.ariaLabel=e.labels.application,"multiple"!==e.context.currentType){if("multiple"===e.type&&t){const n=e.context.mainElement.querySelector('[data-vc="controls"]'),s=e.context.mainElement.querySelector('[data-vc="grid"]'),o=t.closest('[data-vc="column"]');return n&&e.context.mainElement.removeChild(n),s&&(s.dataset.vcGrid="hidden"),o&&(o.dataset.vcColumn=e.context.currentType),void(o&&(o.innerHTML=e.sanitizerHTML(Y(e,e.layouts[e.context.currentType]))))}e.context.mainElement.innerHTML=e.sanitizerHTML(Y(e,e.layouts[e.context.currentType]))}else e.context.mainElement.innerHTML=e.sanitizerHTML(((e,t)=>t.replace(new RegExp("<#Multiple>(.*?)<#\\/Multiple>","gs"),((t,n)=>{const s=Array(e.context.displayMonthsCount).fill(n).join("");return e.sanitizerHTML(s)})).replace(/[\n\t]/g,""))(e,Y(e,e.layouts[e.context.currentType])))},F=(e,t,n,s)=>{e.style.visibility=n?"hidden":"",t.style.visibility=s?"hidden":""},H=e=>{if("month"===e.context.currentType)return;const t=e.context.mainElement.querySelector('[data-vc-arrow="prev"]'),n=e.context.mainElement.querySelector('[data-vc-arrow="next"]');if(!t||!n)return;const s={default:()=>((e,t,n)=>{const s=M(T(new Date(e.context.selectedYear,e.context.selectedMonth,1))),o=new Date(s.getTime()),i=new Date(s.getTime());o.setMonth(o.getMonth()-e.monthsToSwitch),i.setMonth(i.getMonth()+e.monthsToSwitch);const a=M(e.context.dateMin),l=M(e.context.dateMax);e.selectionYearsMode||(a.setFullYear(s.getFullYear()),l.setFullYear(s.getFullYear()));const r=!e.selectionMonthsMode||o.getFullYear()<a.getFullYear()||o.getFullYear()===a.getFullYear()&&o.getMonth()<a.getMonth(),d=!e.selectionMonthsMode||i.getFullYear()>l.getFullYear()||i.getFullYear()===l.getFullYear()&&i.getMonth()>l.getMonth()-(e.context.displayMonthsCount-1);F(t,n,r,d)})(e,t,n),year:()=>((e,t,n)=>{const s=M(e.context.dateMin),o=M(e.context.dateMax),i=!!(s.getFullYear()&&e.context.displayYear-7<=s.getFullYear()),a=!!(o.getFullYear()&&e.context.displayYear+7>=o.getFullYear());F(t,n,i,a)})(e,t,n)};s["multiple"===e.context.currentType?"default":e.context.currentType]()},q=e=>{const t=e.context.mainElement.querySelectorAll('[data-vc="month"]'),n=e.context.mainElement.querySelectorAll('[data-vc="year"]'),s=new Date(e.context.selectedYear,e.context.selectedMonth,1);[t,n].forEach((t=>null==t?void 0:t.forEach(((t,n)=>((e,t,n,s,o)=>{const i=new Date(s.setFullYear(e.context.selectedYear,e.context.selectedMonth+n)).getFullYear(),a=new Date(s.setMonth(e.context.selectedMonth+n)).getMonth(),l=e.context.locale.months.long[a],r=t.closest('[data-vc="column"]');r&&(r.ariaLabel=`${l} ${i}`);const d={month:{id:a,label:l},year:{id:i,label:i}};t.innerText=String(d[o].label),t.dataset[`vc${o.charAt(0).toUpperCase()+o.slice(1)}`]=String(d[o].id),t.ariaLabel=`${e.labels[o]} ${d[o].label}`;const c={month:e.selectionMonthsMode,year:e.selectionYearsMode},u=!1===c[o]||"only-arrows"===c[o];u&&(t.tabIndex=-1),t.disabled=u})(e,t,n,s,t.dataset.vc)))))},_=(e,t,n,s,o)=>{var i;const a={month:"[data-vc-months-month]",year:"[data-vc-years-year]"},l={month:{selected:"data-vc-months-month-selected",aria:"aria-selected",value:"vcMonthsMonth",selectedProperty:"selectedMonth"},year:{selected:"data-vc-years-year-selected",aria:"aria-selected",value:"vcYearsYear",selectedProperty:"selectedYear"}};o&&(null==(i=e.context.mainElement.querySelectorAll(a[n]))||i.forEach((e=>{e.removeAttribute(l[n].selected),e.removeAttribute(l[n].aria)})),v(e,l[n].selectedProperty,Number(t.dataset[l[n].value])),q(e),"year"===n&&H(e)),s&&(t.setAttribute(l[n].selected,""),t.setAttribute(l[n].aria,"true"))},W=(e,t)=>{var n;if("multiple"!==e.type)return{currentValue:null,columnID:0};const s=e.context.mainElement.querySelectorAll('[data-vc="column"]'),o=Array.from(s).findIndex((e=>e.closest(`[data-vc-column="${t}"]`)));return{currentValue:o>=0?Number(null==(n=s[o].querySelector(`[data-vc="${t}"]`))?void 0:n.getAttribute(`data-vc-${t}`)):null,columnID:Math.max(o,0)}},R=(e,t,n,s,o,i,a)=>{const l=t.cloneNode(!1);return l.className=e.styles.monthsMonth,l.innerText=s,l.ariaLabel=o,l.role="gridcell",l.dataset.vcMonthsMonth=`${a}`,i&&(l.ariaDisabled="true"),i&&(l.tabIndex=-1),l.disabled=i,_(e,l,"month",n===a,!1),l},j=(e,t)=>{var n,s;const o=null==(n=null==t?void 0:t.closest('[data-vc="header"]'))?void 0:n.querySelector('[data-vc="year"]'),i=o?Number(o.dataset.vcYear):e.context.selectedYear,a=(null==t?void 0:t.dataset.vcMonth)?Number(t.dataset.vcMonth):e.context.selectedMonth;v(e,"currentType","month"),N(e,t),q(e);const l=e.context.mainElement.querySelector('[data-vc="months"]');if(!e.selectionMonthsMode||!l)return;const r=e.monthsToSwitch>1?e.context.locale.months.long.map(((t,n)=>a-e.monthsToSwitch*n)).concat(e.context.locale.months.long.map(((t,n)=>a+e.monthsToSwitch*n))).filter((e=>e>=0&&e<=12)):Array.from(Array(12).keys()),d=document.createElement("button");d.type="button";for(let t=0;t<12;t++){const n=M(e.context.dateMin),s=M(e.context.dateMax),o=e.context.displayMonthsCount-1,{columnID:c}=W(e,"month"),u=i<=n.getFullYear()&&t<n.getMonth()+c||i>=s.getFullYear()&&t>s.getMonth()-o+c||i>s.getFullYear()||t!==a&&!r.includes(t),h=R(e,d,a,e.context.locale.months.short[t],e.context.locale.months.long[t],u,t);l.appendChild(h),e.onCreateMonthEls&&e.onCreateMonthEls(e,h)}null==(s=e.context.mainElement.querySelector("[data-vc-months-month]:not([disabled])"))||s.focus()},U=(e,t,n,s,o)=>`\n  <label class="${t}" data-vc-time-input="${e}">\n    <input type="text" name="${e}" maxlength="2" aria-label="${n[`input${e.charAt(0).toUpperCase()+e.slice(1)}`]}" value="${s}" ${o?"disabled":""}>\n  </label>\n`,V=(e,t,n,s,o,i,a)=>`\n  <label class="${t}" data-vc-time-range="${e}">\n    <input type="range" name="${e}" min="${s}" max="${o}" step="${i}" aria-label="${n[`range${e.charAt(0).toUpperCase()+e.slice(1)}`]}" value="${a}">\n  </label>\n`,B=(e,t,n,s)=>{({hour:()=>v(e,"selectedHours",n),minute:()=>v(e,"selectedMinutes",n)})[s](),v(e,"selectedTime",`${e.context.selectedHours}:${e.context.selectedMinutes}${e.context.selectedKeeping?` ${e.context.selectedKeeping}`:""}`),e.onChangeTime&&e.onChangeTime(e,t,!1),e.inputMode&&e.context.inputElement&&e.context.mainElement&&e.onChangeToInput&&e.onChangeToInput(e,t)},K=(e,t)=>{var n;return(null==(n={0:{AM:"00",PM:"12"},1:{AM:"01",PM:"13"},2:{AM:"02",PM:"14"},3:{AM:"03",PM:"15"},4:{AM:"04",PM:"16"},5:{AM:"05",PM:"17"},6:{AM:"06",PM:"18"},7:{AM:"07",PM:"19"},8:{AM:"08",PM:"20"},9:{AM:"09",PM:"21"},10:{AM:"10",PM:"22"},11:{AM:"11",PM:"23"},12:{AM:"00",PM:"12"}}[Number(e)])?void 0:n[t])||String(e)},z=e=>({0:"12",13:"01",14:"02",15:"03",16:"04",17:"05",18:"06",19:"07",20:"08",21:"09",22:"10",23:"11"}[Number(e)]||String(e)),J=(e,t,n,s)=>{e.value=n,t.value=s},Z=(e,t,n,s,o,i,a)=>{const l={hour:(l,r,d)=>{if(!e.selectionTimeMode)return;const c={12:()=>{if(!e.context.selectedKeeping)return;const c=Number(K(r,e.context.selectedKeeping));if(!(c<=i&&c>=a))return J(n,t,e.context.selectedHours,e.context.selectedHours),void(e.onChangeTime&&e.onChangeTime(e,d,!0));J(n,t,z(r),K(r,e.context.selectedKeeping)),l>12&&((e,t,n)=>{t&&n&&(v(e,"selectedKeeping",n),t.innerText=n)})(e,s,"PM"),B(e,d,z(r),o)},24:()=>{if(!(l<=i&&l>=a))return J(n,t,e.context.selectedHours,e.context.selectedHours),void(e.onChangeTime&&e.onChangeTime(e,d,!0));J(n,t,r,r),B(e,d,r,o)}};c[e.selectionTimeMode]()},minute:(s,l,r)=>{if(!(s<=i&&s>=a))return n.value=e.context.selectedMinutes,void(e.onChangeTime&&e.onChangeTime(e,r,!0));n.value=l,t.value=l,B(e,r,l,o)}},r=e=>{const t=Number(n.value),s=n.value.padStart(2,"0");l[o]&&l[o](t,s,e)};return n.addEventListener("change",r),()=>{n.removeEventListener("change",r)}},Q=(e,t,n,s,o)=>{const i=i=>{const a=Number(t.value),l=t.value.padStart(2,"0"),r="hour"===o,d=24===e.selectionTimeMode,c=a>0&&a<12;r&&!d&&((e,t,n)=>{t&&(v(e,"selectedKeeping",n),t.innerText=n)})(e,s,0===a||c?"AM":"PM"),((e,t,n,s,o)=>{t.value=o,B(e,n,o,s)})(e,n,i,o,!r||d||c?l:z(t.value))};return t.addEventListener("input",i),()=>{t.removeEventListener("input",i)}},G=e=>e.setAttribute("data-vc-input-focus",""),X=e=>e.removeAttribute("data-vc-input-focus"),ee=(e,t)=>{const n=t.querySelector('[data-vc-time-range="hour"] input[name="hour"]'),s=t.querySelector('[data-vc-time-range="minute"] input[name="minute"]'),o=t.querySelector('[data-vc-time-input="hour"] input[name="hour"]'),i=t.querySelector('[data-vc-time-input="minute"] input[name="minute"]'),a=t.querySelector('[data-vc-time="keeping"]');if(!(n&&s&&o&&i))return;const l=e=>{e.target===n&&G(o),e.target===s&&G(i)},r=e=>{e.target===n&&X(o),e.target===s&&X(i)};return t.addEventListener("mouseover",l),t.addEventListener("mouseout",r),Z(e,n,o,a,"hour",e.timeMaxHour,e.timeMinHour),Z(e,s,i,a,"minute",e.timeMaxMinute,e.timeMinMinute),Q(e,n,o,a,"hour"),Q(e,s,i,a,"minute"),a&&((e,t,n,s,o)=>{const i=i=>{const a="AM"===e.context.selectedKeeping?"PM":"AM",l=K(e.context.selectedHours,a);Number(l)<=s&&Number(l)>=o?(v(e,"selectedKeeping",a),n.value=l,B(e,i,e.context.selectedHours,"hour"),t.ariaLabel=`${e.labels.btnKeeping} ${e.context.selectedKeeping}`,t.innerText=e.context.selectedKeeping):e.onChangeTime&&e.onChangeTime(e,i,!0)};t.addEventListener("click",i)})(e,a,n,e.timeMaxHour,e.timeMinHour),()=>{t.removeEventListener("mouseover",l),t.removeEventListener("mouseout",r)}},te=e=>{const t=e.selectedWeekends?[...e.selectedWeekends]:[],n=[...e.context.locale.weekdays.long].reduce(((n,s,o)=>[...n,{id:o,titleShort:e.context.locale.weekdays.short[o],titleLong:s,isWeekend:t.includes(o)}]),[]),s=[...n.slice(e.firstWeekday),...n.slice(0,e.firstWeekday)];e.context.mainElement.querySelectorAll('[data-vc="week"]').forEach((t=>{const n=e.onClickWeekDay?document.createElement("button"):document.createElement("b");e.onClickWeekDay&&(n.type="button"),s.forEach((s=>{const o=n.cloneNode(!0);o.innerText=s.titleShort,o.className=e.styles.weekDay,o.role="columnheader",o.ariaLabel=s.titleLong,o.dataset.vcWeekDay=String(s.id),s.isWeekend&&(o.dataset.vcWeekDayOff=""),t.appendChild(o)}))}))},ne=(e,t,n,s,o)=>{const i=t.cloneNode(!1);return i.className=e.styles.yearsYear,i.innerText=String(o),i.ariaLabel=String(o),i.role="gridcell",i.dataset.vcYearsYear=`${o}`,s&&(i.ariaDisabled="true"),s&&(i.tabIndex=-1),i.disabled=s,_(e,i,"year",n===o,!1),i},se=(e,t)=>{var n;const s=(null==t?void 0:t.dataset.vcYear)?Number(t.dataset.vcYear):e.context.selectedYear;v(e,"currentType","year"),N(e,t),q(e),H(e);const o=e.context.mainElement.querySelector('[data-vc="years"]');if(!e.selectionYearsMode||!o)return;const i="multiple"!==e.type||e.context.selectedYear===s?0:1,a=document.createElement("button");a.type="button";for(let t=e.context.displayYear-7;t<e.context.displayYear+8;t++){const n=t<M(e.context.dateMin).getFullYear()+i||t>M(e.context.dateMax).getFullYear(),l=ne(e,a,s,n,t);o.appendChild(l),e.onCreateYearEls&&e.onCreateYearEls(e,l)}null==(n=e.context.mainElement.querySelector("[data-vc-years-year]:not([disabled])"))||n.focus()},oe={value:!1,set:()=>oe.value=!0,check:()=>oe.value},ie=(e,t)=>e.dataset.vcTheme=t,ae=(e,t)=>{if(ie(e.context.mainElement,t.matches?"dark":"light"),"system"!==e.selectedTheme||oe.check())return;const n=e=>{const t=document.querySelectorAll('[data-vc="calendar"]');null==t||t.forEach((t=>ie(t,e.matches?"dark":"light")))};t.addEventListener?t.addEventListener("change",n):t.addListener(n),oe.set()},le=(e,t)=>{const n=e.themeAttrDetect.length?document.querySelector(e.themeAttrDetect):null,s=e.themeAttrDetect.replace(/^.*\[(.+)\]/g,((e,t)=>t));if(!n||"system"===n.getAttribute(s))return void ae(e,t);const o=n.getAttribute(s);o?(ie(e.context.mainElement,o),((e,t,n)=>{new MutationObserver((e=>{for(let s=0;s<e.length;s++)if(e[s].attributeName===t){n();break}})).observe(e,{attributes:!0})})(n,s,(()=>{const t=n.getAttribute(s);t&&ie(e.context.mainElement,t)}))):ae(e,t)},re=e=>e.charAt(0).toUpperCase()+e.slice(1).replace(/\./,""),de=e=>{var t,n,s,o,i,a,l,d;if(!(e.context.locale.weekdays.short[6]&&e.context.locale.weekdays.long[6]&&e.context.locale.months.short[11]&&e.context.locale.months.long[11]))if("string"==typeof e.locale){if("string"==typeof e.locale&&!e.locale.length)throw new Error(h);Array.from({length:7},((t,n)=>((e,t,n)=>{const s=new Date(`1978-01-0${t+1}T00:00:00.000Z`),o=s.toLocaleString(n,{weekday:"short",timeZone:"UTC"}),i=s.toLocaleString(n,{weekday:"long",timeZone:"UTC"});e.context.locale.weekdays.short.push(re(o)),e.context.locale.weekdays.long.push(re(i))})(e,n,e.locale))),Array.from({length:12},((t,n)=>((e,t,n)=>{const s=new Date(`1978-${String(t+1).padStart(2,"0")}-01T00:00:00.000Z`),o=s.toLocaleString(n,{month:"short",timeZone:"UTC"}),i=s.toLocaleString(n,{month:"long",timeZone:"UTC"});e.context.locale.months.short.push(re(o)),e.context.locale.months.long.push(re(i))})(e,n,e.locale)))}else{if(!((null==(n=null==(t=e.locale)?void 0:t.weekdays)?void 0:n.short[6])&&(null==(o=null==(s=e.locale)?void 0:s.weekdays)?void 0:o.long[6])&&(null==(a=null==(i=e.locale)?void 0:i.months)?void 0:a.short[11])&&(null==(d=null==(l=e.locale)?void 0:l.months)?void 0:d.long[11])))throw new Error(h);v(e,"locale",r({},e.locale))}},ce=e=>{const t={default:()=>{te(e),I(e)},multiple:()=>{te(e),I(e)},month:()=>j(e),year:()=>se(e)};(e=>{"not all"!==window.matchMedia("(prefers-color-scheme)").media?"system"===e.selectedTheme?le(e,window.matchMedia("(prefers-color-scheme: dark)")):ie(e.context.mainElement,e.selectedTheme):ie(e.context.mainElement,"light")})(e),de(e),N(e),q(e),H(e),(e=>{const t=e.context.mainElement.querySelector('[data-vc="time"]');if(!e.selectionTimeMode||!t)return;const[n,s]=[e.timeMinHour,e.timeMaxHour],[o,i]=[e.timeMinMinute,e.timeMaxMinute],a=e.context.selectedKeeping?K(e.context.selectedHours,e.context.selectedKeeping):e.context.selectedHours,l="range"===e.timeControls;var r;t.innerHTML=e.sanitizerHTML(`\n    <div class="${e.styles.timeContent}" data-vc-time="content">\n      ${U("hour",e.styles.timeHour,e.labels,e.context.selectedHours,l)}\n      ${U("minute",e.styles.timeMinute,e.labels,e.context.selectedMinutes,l)}\n      ${12===e.selectionTimeMode?(r=e.context.selectedKeeping,`<button type="button" class="${e.styles.timeKeeping}" aria-label="${e.labels.btnKeeping} ${r}" data-vc-time="keeping" ${l?"disabled":""}>${r}</button>`):""}\n    </div>\n    <div class="${e.styles.timeRanges}" data-vc-time="ranges">\n      ${V("hour",e.styles.timeRange,e.labels,n,s,e.timeStepHour,a)}\n      ${V("minute",e.styles.timeRange,e.labels,o,i,e.timeStepMinute,e.context.selectedMinutes)}\n    </div>\n  `),ee(e,t)})(e),t[e.context.currentType]()},ue=e=>{const t=t=>{var n;const s=t.target;if(!["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(t.key)||"button"!==s.localName)return;const o=Array.from(e.context.mainElement.querySelectorAll('[data-vc="calendar"] button')),i=o.indexOf(s);if(-1===i)return;const a=(l=o[i]).hasAttribute("data-vc-date-btn")?7:l.hasAttribute("data-vc-months-month")?4:l.hasAttribute("data-vc-years-year")?5:1;var l;const r=(0,{ArrowUp:()=>Math.max(0,i-a),ArrowDown:()=>Math.min(o.length-1,i+a),ArrowLeft:()=>Math.max(0,i-1),ArrowRight:()=>Math.min(o.length-1,i+1)}[t.key])();null==(n=o[r])||n.focus()};return e.context.mainElement.addEventListener("keydown",t),()=>e.context.mainElement.removeEventListener("keydown",t)},he=(e,t)=>{const n=M(T(new Date(e.context.selectedYear,e.context.selectedMonth,1)));({prev:()=>n.setMonth(n.getMonth()-e.monthsToSwitch),next:()=>n.setMonth(n.getMonth()+e.monthsToSwitch)})[t](),v(e,"selectedMonth",n.getMonth()),v(e,"selectedYear",n.getFullYear()),q(e),H(e),I(e)},pe=e=>void 0===e.enableDateToggle||("function"==typeof e.enableDateToggle?e.enableDateToggle(e):e.enableDateToggle),me=(e,t,n)=>{const s=t.dataset.vcDate,o=t.closest("[data-vc-date][data-vc-date-selected]"),i=pe(e);if(o&&!i)return;const a=o?e.context.selectedDates.filter((e=>e!==s)):n?[...e.context.selectedDates,s]:[s];v(e,"selectedDates",a)},ve=(e,t,n)=>{if(!t)return;if(!n)return t.dataset.vcDateRangeTooltip="hidden",void(t.textContent="");const s=e.context.mainElement.getBoundingClientRect(),o=n.getBoundingClientRect();t.style.left=o.left-s.left+o.width/2+"px",t.style.top=o.bottom-s.top-o.height+"px",t.dataset.vcDateRangeTooltip="visible",t.innerHTML=e.sanitizerHTML(e.onCreateDateRangeTooltip(e,n,t,o,s))},ge={self:null,lastDateEl:null,isHovering:!1,rangeMin:void 0,rangeMax:void 0,tooltipEl:null,timeoutId:null},ye=(e,t,n)=>{var s,o,i;if(!(null==(o=null==(s=ge.self)?void 0:s.context)?void 0:o.selectedDates[0]))return;const a=T(e);(null==(i=ge.self.context.disableDates)?void 0:i.includes(a))||(ge.self.context.mainElement.querySelectorAll(`[data-vc-date="${a}"]`).forEach((e=>e.dataset.vcDateHover="")),t.forEach((e=>e.dataset.vcDateHover="first")),n.forEach((e=>{"first"===e.dataset.vcDateHover?e.dataset.vcDateHover="first-and-last":e.dataset.vcDateHover="last"})))},fe=()=>{var e,t;(null==(t=null==(e=ge.self)?void 0:e.context)?void 0:t.mainElement)&&ge.self.context.mainElement.querySelectorAll("[data-vc-date-hover]").forEach((e=>e.removeAttribute("data-vc-date-hover")))},be=e=>t=>{ge.isHovering||(ge.isHovering=!0,requestAnimationFrame((()=>{e(t),ge.isHovering=!1})))},we=be((e=>{var t,n;if(!e.target||!(null==(n=null==(t=ge.self)?void 0:t.context)?void 0:n.selectedDates[0]))return;if(!e.target.closest('[data-vc="dates"]'))return ge.lastDateEl=null,ve(ge.self,ge.tooltipEl,null),void fe();const s=e.target.closest("[data-vc-date]");if(!s||ge.lastDateEl===s)return;ge.lastDateEl=s,ve(ge.self,ge.tooltipEl,s),fe();const o=s.dataset.vcDate,i=M(ge.self.context.selectedDates[0]),a=M(o),l=ge.self.context.mainElement.querySelectorAll(`[data-vc-date="${ge.self.context.selectedDates[0]}"]`),r=ge.self.context.mainElement.querySelectorAll(`[data-vc-date="${o}"]`),[d,c]=i<a?[l,r]:[r,l],[u,h]=i<a?[i,a]:[a,i];for(let e=new Date(u);e<=h;e.setDate(e.getDate()+1))ye(e,d,c)})),xe=be((e=>{const t=e.target.closest("[data-vc-date-selected]");if(!t&&ge.lastDateEl)return ge.lastDateEl=null,void ve(ge.self,ge.tooltipEl,null);t&&ge.lastDateEl!==t&&(ge.lastDateEl=t,ve(ge.self,ge.tooltipEl,t))})),Me=e=>{ge.self&&"Escape"===e.key&&(ge.lastDateEl=null,v(ge.self,"selectedDates",[]),ge.self.context.mainElement.removeEventListener("mousemove",we),ge.self.context.mainElement.removeEventListener("keydown",Me),ve(ge.self,ge.tooltipEl,null),fe())},Te=()=>{null!==ge.timeoutId&&clearTimeout(ge.timeoutId),ge.timeoutId=setTimeout((()=>{ge.lastDateEl=null,ve(ge.self,ge.tooltipEl,null),fe()}),50)},Se=(e,t)=>{ge.self=e,ge.lastDateEl=t,fe(),e.disableDatesGaps&&(ge.rangeMin=ge.rangeMin?ge.rangeMin:e.context.displayDateMin,ge.rangeMax=ge.rangeMax?ge.rangeMax:e.context.displayDateMax),e.onCreateDateRangeTooltip&&(ge.tooltipEl=e.context.mainElement.querySelector("[data-vc-date-range-tooltip]"));const n=null==t?void 0:t.dataset.vcDate;if(n){const t=1===e.context.selectedDates.length&&e.context.selectedDates[0].includes(n),s=t&&!pe(e)?[n,n]:t&&pe(e)?[]:e.context.selectedDates.length>1?[n]:[...e.context.selectedDates,n];v(e,"selectedDates",s),e.context.selectedDates.length>1&&e.context.selectedDates.sort(((e,t)=>+new Date(e)-+new Date(t)))}({set:()=>(e.disableDatesGaps&&(()=>{var e,t,n,s;if(!(null==(n=null==(t=null==(e=ge.self)?void 0:e.context)?void 0:t.selectedDates)?void 0:n[0])||!(null==(s=ge.self.context.disableDates)?void 0:s[0]))return;const o=M(ge.self.context.selectedDates[0]),[i,a]=ge.self.context.disableDates.map((e=>M(e))).reduce((([e,t],n)=>[o>=n?n:e,o<n&&null===t?n:t]),[null,null]);i&&v(ge.self,"displayDateMin",T(new Date(i.setDate(i.getDate()+1)))),a&&v(ge.self,"displayDateMax",T(new Date(a.setDate(a.getDate()-1)))),ge.self.disableDatesPast&&!ge.self.disableAllDates&&M(ge.self.context.displayDateMin)<M(ge.self.context.dateToday)&&v(ge.self,"displayDateMin",ge.self.context.dateToday)})(),ve(ge.self,ge.tooltipEl,t),ge.self.context.mainElement.removeEventListener("mousemove",xe),ge.self.context.mainElement.removeEventListener("mouseleave",Te),ge.self.context.mainElement.removeEventListener("keydown",Me),ge.self.context.mainElement.addEventListener("mousemove",we),ge.self.context.mainElement.addEventListener("mouseleave",Te),ge.self.context.mainElement.addEventListener("keydown",Me),()=>{ge.self.context.mainElement.removeEventListener("mousemove",we),ge.self.context.mainElement.removeEventListener("mouseleave",Te),ge.self.context.mainElement.removeEventListener("keydown",Me)}),reset:()=>{const[n,s]=[e.context.selectedDates[0],e.context.selectedDates[e.context.selectedDates.length-1]],o=e.context.selectedDates[0]!==e.context.selectedDates[e.context.selectedDates.length-1],i=S([`${n}:${s}`]).filter((t=>!e.context.disableDates.includes(t))),a=o?e.enableEdgeDatesOnly?[n,s]:i:[e.context.selectedDates[0],e.context.selectedDates[0]];if(v(e,"selectedDates",a),e.disableDatesGaps&&(v(e,"displayDateMin",ge.rangeMin),v(e,"displayDateMax",ge.rangeMax)),ge.self.context.mainElement.removeEventListener("mousemove",we),ge.self.context.mainElement.removeEventListener("mouseleave",Te),ge.self.context.mainElement.removeEventListener("keydown",Me),e.onCreateDateRangeTooltip)return e.context.selectedDates[0]||(ge.self.context.mainElement.removeEventListener("mousemove",xe),ge.self.context.mainElement.removeEventListener("mouseleave",Te),ve(ge.self,ge.tooltipEl,null)),e.context.selectedDates[0]&&(ge.self.context.mainElement.addEventListener("mousemove",xe),ge.self.context.mainElement.addEventListener("mouseleave",Te),ve(ge.self,ge.tooltipEl,t)),()=>{ge.self.context.mainElement.removeEventListener("mousemove",xe),ge.self.context.mainElement.removeEventListener("mouseleave",Te)}}})[1===e.context.selectedDates.length?"set":"reset"]()},Ee=e=>{e.context.mainElement.querySelectorAll("[data-vc-date]").forEach((t=>{const n=t.querySelector("[data-vc-date-btn]"),s=t.dataset.vcDate,o=M(s).getDay();C(e,e.context.selectedYear,t,n,o,s,"current")}))},Ce=["month","year"],De=(e,t,n)=>{const{currentValue:s,columnID:o}=W(e,t);return"month"===e.context.currentType&&o>=0?n-o:"year"===e.context.currentType&&e.context.selectedYear!==s?n-1:n},ke=(e,t,n,s)=>{var o;const i={year:()=>{if("multiple"===e.type)return((e,t)=>{const n=De(e,"year",Number(t.dataset.vcYearsYear)),s=M(e.context.dateMin),o=M(e.context.dateMax),i=e.context.displayMonthsCount-1,{columnID:a}=W(e,"year"),l=e.context.selectedMonth<s.getMonth()&&n<=s.getFullYear(),r=e.context.selectedMonth>o.getMonth()-i+a&&n>=o.getFullYear(),d=n<s.getFullYear(),c=n>o.getFullYear(),u=l||d?s.getFullYear():r||c?o.getFullYear():n,h=l||d?s.getMonth():r||c?o.getMonth()-i+a:e.context.selectedMonth;v(e,"selectedYear",u),v(e,"selectedMonth",h)})(e,s);v(e,"selectedYear",Number(s.dataset.vcYearsYear))},month:()=>{if("multiple"===e.type)return((e,t)=>{const n=t.closest('[data-vc-column="month"]').querySelector('[data-vc="year"]'),s=De(e,"month",Number(t.dataset.vcMonthsMonth)),o=Number(n.dataset.vcYear),i=M(e.context.dateMin),a=M(e.context.dateMax),l=s<i.getMonth()&&o<=i.getFullYear(),r=s>a.getMonth()&&o>=a.getFullYear();v(e,"selectedYear",o),v(e,"selectedMonth",l?i.getMonth():r?a.getMonth():s)})(e,s);v(e,"selectedMonth",Number(s.dataset.vcMonthsMonth))}};i[n](),{year:()=>{var n;return null==(n=e.onClickYear)?void 0:n.call(e,e,t)},month:()=>{var n;return null==(n=e.onClickMonth)?void 0:n.call(e,e,t)}}[n](),e.context.currentType!==e.type?(v(e,"currentType",e.type),ce(e),null==(o=e.context.mainElement.querySelector(`[data-vc="${n}"]`))||o.focus()):_(e,s,n,!0,!0)},Ie=(e,t)=>{const n={month:e.selectionMonthsMode,year:e.selectionYearsMode};Ce.forEach((s=>{n[s]&&t.target&&((e,t,n)=>{var s;const o=t.target,i=o.closest(`[data-vc="${n}"]`),a={year:()=>se(e,o),month:()=>j(e,o)};if(i&&e.onClickTitle&&e.onClickTitle(e,t),i&&e.context.currentType!==n)return a[n]();const l=o.closest(`[data-vc-${n}s-${n}]`);if(l)return ke(e,t,n,l);const r=o.closest('[data-vc="grid"]'),d=o.closest('[data-vc="column"]');(e.context.currentType===n&&i||"multiple"===e.type&&e.context.currentType===n&&r&&!d)&&(v(e,"currentType",e.type),ce(e),null==(s=e.context.mainElement.querySelector(`[data-vc="${n}"]`))||s.focus())})(e,t,s)}))},Ae=e=>{const t=t=>{((e,t)=>{const n=t.target.closest("[data-vc-arrow]");if(n){if(["default","multiple"].includes(e.context.currentType))he(e,n.dataset.vcArrow);else if("year"===e.context.currentType&&void 0!==e.context.displayYear){const s={prev:-15,next:15}[n.dataset.vcArrow];v(e,"displayYear",e.context.displayYear+s),se(e,t.target)}e.onClickArrow&&e.onClickArrow(e,t)}})(e,t),((e,t)=>{if(!e.onClickWeekDay)return;const n=t.target.closest("[data-vc-week-day]"),s=t.target.closest('[data-vc="column"]'),o=s?s.querySelectorAll("[data-vc-date-week-day]"):e.context.mainElement.querySelectorAll("[data-vc-date-week-day]");if(!n||!o[0])return;const i=Number(n.dataset.vcWeekDay),a=Array.from(o).filter((e=>Number(e.dataset.vcDateWeekDay)===i));e.onClickWeekDay(e,i,a,t)})(e,t),((e,t)=>{if(!e.enableWeekNumbers||!e.onClickWeekNumber)return;const n=t.target.closest("[data-vc-week-number]"),s=e.context.mainElement.querySelectorAll("[data-vc-date-week-number]");if(!n||!s[0])return;const o=Number(n.innerText),i=Number(n.dataset.vcWeekYear),a=Array.from(s).filter((e=>Number(e.dataset.vcDateWeekNumber)===o));e.onClickWeekNumber(e,o,i,a,t)})(e,t),((e,t)=>{var n;const s=t.target,o=s.closest("[data-vc-date-btn]");if(!e.selectionDatesMode||!["single","multiple","multiple-ranged"].includes(e.selectionDatesMode)||!o)return;const i=o.closest("[data-vc-date]");({single:()=>me(e,i,!1),multiple:()=>me(e,i,!0),"multiple-ranged":()=>Se(e,i)})[e.selectionDatesMode](),null==(n=e.context.selectedDates)||n.sort(((e,t)=>+new Date(e)-+new Date(t))),e.onClickDate&&e.onClickDate(e,t),e.inputMode&&e.context.inputElement&&e.context.mainElement&&e.onChangeToInput&&e.onChangeToInput(e,t);const a=s.closest('[data-vc-date-month="prev"]'),l=s.closest('[data-vc-date-month="next"]');({prev:()=>e.enableMonthChangeOnDayClick?he(e,"prev"):Ee(e),next:()=>e.enableMonthChangeOnDayClick?he(e,"next"):Ee(e),current:()=>Ee(e)})[a?"prev":l?"next":"current"]()})(e,t),Ie(e,t)};return e.context.mainElement.addEventListener("click",t),()=>e.context.mainElement.removeEventListener("click",t)},Le=(e,t)=>"today"===e?(()=>{const e=new Date;return new Date(e.getTime()-6e4*e.getTimezoneOffset()).toISOString().substring(0,10)})():e instanceof Date||"number"==typeof e||"string"==typeof e?S([e])[0]:t,Oe=(e,t,n)=>{v(e,"selectedMonth",t),v(e,"selectedYear",n),v(e,"displayYear",n)},$e=e=>{v(e,"currentType",e.type),(e=>{if("multiple"===e.type&&(e.displayMonthsCount<=1||e.displayMonthsCount>12))throw new Error(m);if("multiple"!==e.type&&e.displayMonthsCount>1)throw new Error(m);v(e,"displayMonthsCount",e.displayMonthsCount?e.displayMonthsCount:"multiple"===e.type?2:1)})(e),(e=>{var t,n,s;const o=Le(e.dateMin,e.dateMin),i=Le(e.dateMax,e.dateMax),a=Le(e.displayDateMin,o),l=Le(e.displayDateMax,i);v(e,"dateToday",Le(e.dateToday,e.dateToday)),v(e,"displayDateMin",a?M(o)>=M(a)?o:a:o),v(e,"displayDateMax",l?M(i)<=M(l)?i:l:i);const r=e.disableDatesPast&&!e.disableAllDates&&M(a)<M(e.context.dateToday);v(e,"displayDateMin",r||e.disableAllDates?e.context.dateToday:a),v(e,"displayDateMax",e.disableAllDates?e.context.dateToday:l),v(e,"disableDates",e.disableDates[0]&&!e.disableAllDates?S(e.disableDates):e.disableAllDates?[e.context.displayDateMin]:[]),e.context.disableDates.length>1&&e.context.disableDates.sort(((e,t)=>+new Date(e)-+new Date(t))),v(e,"enableDates",e.enableDates[0]?S(e.enableDates):[]),(null==(t=e.context.enableDates)?void 0:t[0])&&(null==(n=e.context.disableDates)?void 0:n[0])&&v(e,"disableDates",e.context.disableDates.filter((t=>!e.context.enableDates.includes(t)))),e.context.enableDates.length>1&&e.context.enableDates.sort(((e,t)=>+new Date(e)-+new Date(t))),(null==(s=e.context.enableDates)?void 0:s[0])&&e.disableAllDates&&(v(e,"displayDateMin",e.context.enableDates[0]),v(e,"displayDateMax",e.context.enableDates[e.context.enableDates.length-1])),v(e,"dateMin",e.displayDisabledDates?o:e.context.displayDateMin),v(e,"dateMax",e.displayDisabledDates?i:e.context.displayDateMax)})(e),(e=>{var t;if(e.enableJumpToSelectedDate&&(null==(t=e.selectedDates)?void 0:t[0])&&void 0===e.selectedMonth&&void 0===e.selectedYear){const t=M(S(e.selectedDates)[0]);return void Oe(e,t.getMonth(),t.getFullYear())}const n=void 0!==e.selectedMonth&&Number(e.selectedMonth)>=0&&Number(e.selectedMonth)<12,s=void 0!==e.selectedYear&&Number(e.selectedYear)>=0&&Number(e.selectedYear)<=9999;Oe(e,n?Number(e.selectedMonth):M(e.context.dateToday).getMonth(),s?Number(e.selectedYear):M(e.context.dateToday).getFullYear())})(e),(e=>{var t;v(e,"selectedDates",(null==(t=e.selectedDates)?void 0:t[0])?S(e.selectedDates):[])})(e),(e=>{var t,n,s;if(!e.selectionTimeMode)return;if(![12,24].includes(e.selectionTimeMode))throw new Error(p);const o=12===e.selectionTimeMode,i=o?/^(0[1-9]|1[0-2]):([0-5][0-9]) ?(AM|PM)?$/i:/^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;let[a,l,r]=null!=(s=null==(n=null==(t=e.selectedTime)?void 0:t.match(i))?void 0:n.slice(1))?s:[];a?o&&!r&&(r="AM"):(a=o?z(String(e.timeMinHour)):String(e.timeMinHour),l=String(e.timeMinMinute),r=o?Number(z(String(e.timeMinHour)))>=12?"PM":"AM":null),v(e,"selectedHours",a.padStart(2,"0")),v(e,"selectedMinutes",l.padStart(2,"0")),v(e,"selectedKeeping",r),v(e,"selectedTime",`${e.context.selectedHours}:${e.context.selectedMinutes}${r?` ${r}`:""}`)})(e)},Pe=(e,{year:t,month:n,dates:s,time:o,locale:i},a=!0)=>{var l;const r={year:e.selectedYear,month:e.selectedMonth,dates:e.selectedDates,time:e.selectedTime};e.selectedYear=t?r.year:e.context.selectedYear,e.selectedMonth=n?r.month:e.context.selectedMonth,e.selectedTime=o?r.time:e.context.selectedTime,e.selectedDates="only-first"===s&&(null==(l=e.context.selectedDates)?void 0:l[0])?[e.context.selectedDates[0]]:!0===s?r.dates:e.context.selectedDates,i&&v(e,"locale",{months:{short:[],long:[]},weekdays:{short:[],long:[]}}),$e(e),a&&ce(e),e.selectedYear=r.year,e.selectedMonth=r.month,e.selectedDates=r.dates,e.selectedTime=r.time,"multiple-ranged"===e.selectionDatesMode&&s&&Se(e,null)},Ye=e=>{v(e,"inputElement",e.context.mainElement);const t=()=>{e.context.inputModeInit?queueMicrotask((()=>qe(e))):(e=>{const t=document.createElement("div");t.className=e.styles.calendar,t.dataset.vc="calendar",t.dataset.vcInput="",t.dataset.vcCalendarHidden="",v(e,"inputModeInit",!0),v(e,"isShowInInputMode",!1),v(e,"mainElement",t),document.body.appendChild(e.context.mainElement),Pe(e,{year:!0,month:!0,dates:!0,time:!0,locale:!0}),queueMicrotask((()=>qe(e))),e.onInit&&e.onInit(e),ue(e),Ae(e)})(e)};return e.context.inputElement.addEventListener("click",t),e.context.inputElement.addEventListener("focus",t),()=>{e.context.inputElement.removeEventListener("click",t),e.context.inputElement.removeEventListener("focus",t)}},Ne=(e,t)=>{if(!e.context.isInit)throw new Error(u);Pe(e,r(r({},{year:!0,month:!0,dates:!0,time:!0,locale:!0}),t),!(e.inputMode&&!e.context.inputModeInit)),e.onUpdate&&e.onUpdate(e)},Fe=(e,t)=>{const n=Object.keys(t);for(let s=0;s<n.length;s++){const o=n[s];"object"!=typeof e[o]||"object"!=typeof t[o]||t[o]instanceof Date||Array.isArray(t[o])?void 0!==t[o]&&(e[o]=t[o]):Fe(e[o],t[o])}},He=(e,t,n)=>{if(!e)return;const s="auto"===n?function(e,t){const n="left";if(!t||!e)return n;const{canShow:s,parentPositions:o}=w(e,t),i=s.left&&s.right;return(i&&s.bottom?"center":i&&s.top?["top","center"]:Array.isArray(o)?["bottom"===o[0]?"top":"bottom",...o.slice(1)]:o)||n}(e,t):n,o={top:-t.offsetHeight,bottom:e.offsetHeight,left:0,center:e.offsetWidth/2-t.offsetWidth/2,right:e.offsetWidth-t.offsetWidth},i=Array.isArray(s)?s[0]:"bottom",a=Array.isArray(s)?s[1]:s;t.dataset.vcPosition=i;const{top:l,left:r}=y(e),d=l+o[i];let c=r+o[a];const{vw:u}=f();if(c+t.clientWidth>u){const e=window.innerWidth-document.body.clientWidth;c=u-t.clientWidth-e}else c<0&&(c=0);Object.assign(t.style,{left:`${c}px`,top:`${d}px`})},qe=e=>{if(e.context.isShowInInputMode)return;if(!e.context.currentType)return void e.context.mainElement.click();v(e,"cleanupHandlers",[]),v(e,"isShowInInputMode",!0),He(e.context.inputElement,e.context.mainElement,e.positionToInput),e.context.mainElement.removeAttribute("data-vc-calendar-hidden");const t=()=>{He(e.context.inputElement,e.context.mainElement,e.positionToInput)};window.addEventListener("resize",t),e.context.cleanupHandlers.push((()=>window.removeEventListener("resize",t)));const n=t=>{"Escape"===t.key&&g(e)};document.addEventListener("keydown",n),e.context.cleanupHandlers.push((()=>document.removeEventListener("keydown",n)));const s=t=>{t.target===e.context.inputElement||e.context.mainElement.contains(t.target)||g(e)};document.addEventListener("click",s,{capture:!0}),e.context.cleanupHandlers.push((()=>document.removeEventListener("click",s,{capture:!0}))),e.onShow&&e.onShow(e)},_e={application:"Calendar",navigation:"Calendar Navigation",arrowNext:{month:"Next month",year:"Next list of years"},arrowPrev:{month:"Previous month",year:"Previous list of years"},month:"Select month, current selected month:",months:"List of months",year:"Select year, current selected year:",years:"List of years",week:"Days of the week",weekNumber:"Numbers of weeks in a year",dates:"Dates in the current month",selectingTime:"Selecting a time ",inputHour:"Hours",inputMinute:"Minutes",rangeHour:"Slider for selecting hours",rangeMinute:"Slider for selecting minutes",btnKeeping:"Switch AM/PM, current position:"},We={calendar:"vc",controls:"vc-controls",grid:"vc-grid",column:"vc-column",header:"vc-header",headerContent:"vc-header__content",month:"vc-month",year:"vc-year",arrowPrev:"vc-arrow vc-arrow_prev",arrowNext:"vc-arrow vc-arrow_next",wrapper:"vc-wrapper",content:"vc-content",months:"vc-months",monthsMonth:"vc-months__month",years:"vc-years",yearsYear:"vc-years__year",week:"vc-week",weekDay:"vc-week__day",weekNumbers:"vc-week-numbers",weekNumbersTitle:"vc-week-numbers__title",weekNumbersContent:"vc-week-numbers__content",weekNumber:"vc-week-number",dates:"vc-dates",date:"vc-date",dateBtn:"vc-date__btn",datePopup:"vc-date__popup",dateRangeTooltip:"vc-date-range-tooltip",time:"vc-time",timeContent:"vc-time__content",timeHour:"vc-time__hour",timeMinute:"vc-time__minute",timeKeeping:"vc-time__keeping",timeRanges:"vc-time__ranges",timeRange:"vc-time__range"};class Re{constructor(){d(this,"type","default"),d(this,"inputMode",!1),d(this,"positionToInput","left"),d(this,"firstWeekday",1),d(this,"monthsToSwitch",1),d(this,"themeAttrDetect","html[data-theme]"),d(this,"locale","en"),d(this,"dateToday","today"),d(this,"dateMin","1970-01-01"),d(this,"dateMax","2470-12-31"),d(this,"displayDateMin"),d(this,"displayDateMax"),d(this,"displayDatesOutside",!0),d(this,"displayDisabledDates",!1),d(this,"displayMonthsCount"),d(this,"disableDates",[]),d(this,"disableAllDates",!1),d(this,"disableDatesPast",!1),d(this,"disableDatesGaps",!1),d(this,"disableWeekdays",[]),d(this,"disableToday",!1),d(this,"enableDates",[]),d(this,"enableEdgeDatesOnly",!0),d(this,"enableDateToggle",!0),d(this,"enableWeekNumbers",!1),d(this,"enableMonthChangeOnDayClick",!0),d(this,"enableJumpToSelectedDate",!1),d(this,"selectionDatesMode","single"),d(this,"selectionMonthsMode",!0),d(this,"selectionYearsMode",!0),d(this,"selectionTimeMode",!1),d(this,"selectedDates",[]),d(this,"selectedMonth"),d(this,"selectedYear"),d(this,"selectedHolidays",[]),d(this,"selectedWeekends",[0,6]),d(this,"selectedTime"),d(this,"selectedTheme","system"),d(this,"timeMinHour",0),d(this,"timeMaxHour",23),d(this,"timeMinMinute",0),d(this,"timeMaxMinute",59),d(this,"timeControls","all"),d(this,"timeStepHour",1),d(this,"timeStepMinute",1),d(this,"sanitizerHTML",(e=>e)),d(this,"onClickDate"),d(this,"onClickWeekDay"),d(this,"onClickWeekNumber"),d(this,"onClickTitle"),d(this,"onClickMonth"),d(this,"onClickYear"),d(this,"onClickArrow"),d(this,"onChangeTime"),d(this,"onChangeToInput"),d(this,"onCreateDateRangeTooltip"),d(this,"onCreateDateEls"),d(this,"onCreateMonthEls"),d(this,"onCreateYearEls"),d(this,"onInit"),d(this,"onUpdate"),d(this,"onDestroy"),d(this,"onShow"),d(this,"onHide"),d(this,"popups",{}),d(this,"labels",r({},_e)),d(this,"layouts",{default:"",multiple:"",month:"",year:""}),d(this,"styles",r({},We))}}const je=class e extends Re{constructor(t,o){var i,a;super(),d(this,"init",(()=>{return v(e=this,"originalElement",e.context.mainElement.cloneNode(!0)),v(e,"isInit",!0),e.inputMode?Ye(e):($e(e),ce(e),e.onInit&&e.onInit(e),ue(e),Ae(e));var e})),d(this,"update",(e=>Ne(this,e))),d(this,"destroy",(()=>(e=>{var t,n,s,o,i;if(!e.context.isInit)throw new Error(u);e.inputMode?(null==(t=e.context.mainElement.parentElement)||t.removeChild(e.context.mainElement),null==(s=null==(n=e.context.inputElement)?void 0:n.replaceWith)||s.call(n,e.context.originalElement),v(e,"inputElement",void 0)):null==(i=(o=e.context.mainElement).replaceWith)||i.call(o,e.context.originalElement),v(e,"mainElement",e.context.originalElement),e.onDestroy&&e.onDestroy(e)})(this))),d(this,"show",(()=>qe(this))),d(this,"hide",(()=>g(this))),d(this,"set",((e,t)=>((e,t,n)=>{Fe(e,t),e.context.isInit&&Ne(e,n)})(this,e,t))),d(this,"context"),this.context=(a=r({},this.context),n(a,s({locale:{months:{short:[],long:[]},weekdays:{short:[],long:[]}}}))),v(this,"mainElement","string"==typeof t?null!=(i=e.memoizedElements.get(t))?i:this.queryAndMemoize(t):t),o&&Fe(this,o)}queryAndMemoize(t){const n=document.querySelector(t);if(!n)throw new Error(c(t));return e.memoizedElements.set(t,n),n}};d(je,"memoizedElements",new Map);let Ue=je;e.Calendar=Ue,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(t)},442:function(e,t,n){"use strict";
/*
 * HSSelect
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */var s=this&&this.__awaiter||function(e,t,n,s){return new(n||(n=Promise))((function(o,i){function a(e){try{r(s.next(e))}catch(e){i(e)}}function l(e){try{r(s.throw(e))}catch(e){i(e)}}function r(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,l)}r((s=s.apply(e,t||[])).next())}))},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const i=n(292),a=o(n(961)),l=n(223);class r extends a.default{constructor(e,t){var n,s,o,i,a;super(e,t),this.optionId=0;const l=e.getAttribute("data-hs-select"),r=l?JSON.parse(l):{},d=Object.assign(Object.assign({},r),t);this.value=(null==d?void 0:d.value)||this.el.value||null,this.placeholder=(null==d?void 0:d.placeholder)||"Select...",this.hasSearch=(null==d?void 0:d.hasSearch)||!1,this.minSearchLength=null!==(n=null==d?void 0:d.minSearchLength)&&void 0!==n?n:0,this.preventSearchFocus=(null==d?void 0:d.preventSearchFocus)||!1,this.mode=(null==d?void 0:d.mode)||"default",this.viewport=void 0!==(null==d?void 0:d.viewport)?document.querySelector(null==d?void 0:d.viewport):null,this.isOpened=Boolean(null==d?void 0:d.isOpened)||!1,this.isMultiple=this.el.hasAttribute("multiple")||!1,this.isDisabled=this.el.hasAttribute("disabled")||!1,this.selectedItems=[],this.apiUrl=(null==d?void 0:d.apiUrl)||null,this.apiQuery=(null==d?void 0:d.apiQuery)||null,this.apiOptions=(null==d?void 0:d.apiOptions)||null,this.apiSearchQueryKey=(null==d?void 0:d.apiSearchQueryKey)||null,this.apiDataPart=(null==d?void 0:d.apiDataPart)||null,this.apiLoadMore=!0===(null==d?void 0:d.apiLoadMore)?{perPage:10,scrollThreshold:100}:"object"==typeof(null==d?void 0:d.apiLoadMore)&&null!==(null==d?void 0:d.apiLoadMore)&&{perPage:d.apiLoadMore.perPage||10,scrollThreshold:d.apiLoadMore.scrollThreshold||100},this.apiFieldsMap=(null==d?void 0:d.apiFieldsMap)||null,this.apiIconTag=(null==d?void 0:d.apiIconTag)||null,this.apiSelectedValues=(null==d?void 0:d.apiSelectedValues)||null,this.currentPage=0,this.isLoading=!1,this.hasMore=!0,this.wrapperClasses=(null==d?void 0:d.wrapperClasses)||null,this.toggleTag=(null==d?void 0:d.toggleTag)||null,this.toggleClasses=(null==d?void 0:d.toggleClasses)||null,this.toggleCountText=void 0===typeof(null==d?void 0:d.toggleCountText)?null:d.toggleCountText,this.toggleCountTextPlacement=(null==d?void 0:d.toggleCountTextPlacement)||"postfix",this.toggleCountTextMinItems=(null==d?void 0:d.toggleCountTextMinItems)||1,this.toggleCountTextMode=(null==d?void 0:d.toggleCountTextMode)||"countAfterLimit",this.toggleSeparators={items:(null===(s=null==d?void 0:d.toggleSeparators)||void 0===s?void 0:s.items)||", ",betweenItemsAndCounter:(null===(o=null==d?void 0:d.toggleSeparators)||void 0===o?void 0:o.betweenItemsAndCounter)||"and"},this.tagsItemTemplate=(null==d?void 0:d.tagsItemTemplate)||null,this.tagsItemClasses=(null==d?void 0:d.tagsItemClasses)||null,this.tagsInputId=(null==d?void 0:d.tagsInputId)||null,this.tagsInputClasses=(null==d?void 0:d.tagsInputClasses)||null,this.dropdownTag=(null==d?void 0:d.dropdownTag)||null,this.dropdownClasses=(null==d?void 0:d.dropdownClasses)||null,this.dropdownDirectionClasses=(null==d?void 0:d.dropdownDirectionClasses)||null,this.dropdownSpace=(null==d?void 0:d.dropdownSpace)||10,this.dropdownPlacement=(null==d?void 0:d.dropdownPlacement)||null,this.dropdownVerticalFixedPlacement=(null==d?void 0:d.dropdownVerticalFixedPlacement)||null,this.dropdownScope=(null==d?void 0:d.dropdownScope)||"parent",this.dropdownAutoPlacement=(null==d?void 0:d.dropdownAutoPlacement)||!1,this.searchTemplate=(null==d?void 0:d.searchTemplate)||null,this.searchWrapperTemplate=(null==d?void 0:d.searchWrapperTemplate)||null,this.searchWrapperClasses=(null==d?void 0:d.searchWrapperClasses)||"bg-white p-2 sticky top-0",this.searchId=(null==d?void 0:d.searchId)||null,this.searchLimit=(null==d?void 0:d.searchLimit)||1/0,this.isSearchDirectMatch=void 0===(null==d?void 0:d.isSearchDirectMatch)||(null==d?void 0:d.isSearchDirectMatch),this.searchClasses=(null==d?void 0:d.searchClasses)||"block w-[calc(100%-32px)] text-sm border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 py-2 px-3 my-2 mx-4",this.searchPlaceholder=(null==d?void 0:d.searchPlaceholder)||"Search...",this.searchNoResultTemplate=(null==d?void 0:d.searchNoResultTemplate)||"<span></span>",this.searchNoResultText=(null==d?void 0:d.searchNoResultText)||"No results found",this.searchNoResultClasses=(null==d?void 0:d.searchNoResultClasses)||"px-4 text-sm text-gray-800 dark:text-neutral-200",this.optionAllowEmptyOption=void 0!==(null==d?void 0:d.optionAllowEmptyOption)&&(null==d?void 0:d.optionAllowEmptyOption),this.optionTemplate=(null==d?void 0:d.optionTemplate)||null,this.optionTag=(null==d?void 0:d.optionTag)||null,this.optionClasses=(null==d?void 0:d.optionClasses)||null,this.extraMarkup=(null==d?void 0:d.extraMarkup)||null,this.descriptionClasses=(null==d?void 0:d.descriptionClasses)||null,this.iconClasses=(null==d?void 0:d.iconClasses)||null,this.isAddTagOnEnter=null===(i=null==d?void 0:d.isAddTagOnEnter)||void 0===i||i,this.isSelectedOptionOnTop=null===(a=null==d?void 0:d.isSelectedOptionOnTop)||void 0===a||a,this.animationInProcess=!1,this.selectOptions=[],this.remoteOptions=[],this.tagsInputHelper=null,this.init()}wrapperClick(e){e.target.closest("[data-hs-select-dropdown]")||e.target.closest("[data-tag-value]")||this.tagsInput.focus()}toggleClick(){if(this.isDisabled)return!1;this.toggleFn()}tagsInputFocus(){this.isOpened||this.open()}tagsInputInput(){this.calculateInputWidth()}tagsInputInputSecond(e){this.apiUrl||this.searchOptions(e.target.value)}tagsInputKeydown(e){if("Enter"===e.key&&this.isAddTagOnEnter){const t=e.target.value;if(this.selectOptions.find((e=>e.val===t)))return!1;this.addSelectOption(t,t),this.buildOption(t,t),this.buildOriginalOption(t,t),this.dropdown.querySelector(`[data-value="${t}"]`).click(),this.resetTagsInputField()}}searchInput(e){const t=e.target.value;this.apiUrl?this.remoteSearch(t):this.searchOptions(t)}setValue(e){this.value=e,this.clearSelections(),Array.isArray(e)?"tags"===this.mode?(this.unselectMultipleItems(),this.selectMultipleItems(),this.selectedItems=[],this.wrapper.querySelectorAll("[data-tag-value]").forEach((e=>e.remove())),this.setTagsItems(),this.reassignTagsInputPlaceholder(this.value.length?"":this.placeholder)):(this.toggleTextWrapper.innerHTML=this.value.length?this.stringFromValue():this.placeholder,this.unselectMultipleItems(),this.selectMultipleItems()):(this.setToggleTitle(),this.toggle.querySelector("[data-icon]")&&this.setToggleIcon(),this.toggle.querySelector("[data-title]")&&this.setToggleTitle(),this.selectSingleItem())}init(){this.createCollection(window.$hsSelectCollection,this),this.build()}build(){if(this.el.style.display="none",this.el.children&&Array.from(this.el.children).filter((e=>this.optionAllowEmptyOption||!this.optionAllowEmptyOption&&e.value&&""!==e.value)).forEach((e=>{const t=e.getAttribute("data-hs-select-option");this.selectOptions=[...this.selectOptions,{title:e.textContent,val:e.value,disabled:e.disabled,options:"undefined"!==t?JSON.parse(t):null}]})),this.optionAllowEmptyOption&&!this.value&&(this.value=""),this.isMultiple){const e=Array.from(this.el.children).filter((e=>e.selected));if(e){const t=[];e.forEach((e=>{t.push(e.value)})),this.value=t}}this.buildWrapper(),"tags"===this.mode?this.buildTags():this.buildToggle(),this.buildDropdown(),this.extraMarkup&&this.buildExtraMarkup()}buildWrapper(){this.wrapper=document.createElement("div"),this.wrapper.classList.add("hs-select","relative"),"tags"===this.mode&&(this.onWrapperClickListener=e=>this.wrapperClick(e),this.wrapper.addEventListener("click",this.onWrapperClickListener)),this.wrapperClasses&&(0,i.classToClassList)(this.wrapperClasses,this.wrapper),this.el.before(this.wrapper),this.wrapper.append(this.el)}buildExtraMarkup(){const e=e=>{const t=(0,i.htmlToElement)(e);return this.wrapper.append(t),t},t=e=>{e.classList.contains("--prevent-click")||e.addEventListener("click",(e=>{e.stopPropagation(),this.isDisabled||this.toggleFn()}))};if(Array.isArray(this.extraMarkup))this.extraMarkup.forEach((n=>{const s=e(n);t(s)}));else{const n=e(this.extraMarkup);t(n)}}buildToggle(){var e,t;let n,s;this.toggleTextWrapper=document.createElement("span"),this.toggleTextWrapper.classList.add("truncate"),this.toggle=(0,i.htmlToElement)(this.toggleTag||"<div></div>"),n=this.toggle.querySelector("[data-icon]"),s=this.toggle.querySelector("[data-title]"),!this.isMultiple&&n&&this.setToggleIcon(),!this.isMultiple&&s&&this.setToggleTitle(),this.isMultiple?this.toggleTextWrapper.innerHTML=this.value.length?this.stringFromValue():this.placeholder:this.toggleTextWrapper.innerHTML=(null===(e=this.getItemByValue(this.value))||void 0===e?void 0:e.title)||this.placeholder,s||this.toggle.append(this.toggleTextWrapper),this.toggleClasses&&(0,i.classToClassList)(this.toggleClasses,this.toggle),this.isDisabled&&this.toggle.classList.add("disabled"),this.wrapper&&this.wrapper.append(this.toggle),(null===(t=this.toggle)||void 0===t?void 0:t.ariaExpanded)&&(this.isOpened?this.toggle.ariaExpanded="true":this.toggle.ariaExpanded="false"),this.onToggleClickListener=()=>this.toggleClick(),this.toggle.addEventListener("click",this.onToggleClickListener)}setToggleIcon(){var e;const t=this.getItemByValue(this.value),n=this.toggle.querySelector("[data-icon]");if(n){n.innerHTML="";const s=(0,i.htmlToElement)(this.apiUrl&&this.apiIconTag?this.apiIconTag||"":(null===(e=null==t?void 0:t.options)||void 0===e?void 0:e.icon)||"");this.value&&this.apiUrl&&this.apiIconTag&&t[this.apiFieldsMap.icon]&&(s.src=t[this.apiFieldsMap.icon]||""),n.append(s),(null==s?void 0:s.src)?n.classList.remove("hidden"):n.classList.add("hidden")}}setToggleTitle(){const e=this.toggle.querySelector("[data-title]");let t=this.placeholder;if(this.optionAllowEmptyOption&&""===this.value){const e=this.selectOptions.find((e=>""===e.val));t=(null==e?void 0:e.title)||this.placeholder}else if(this.value)if(this.apiUrl){const e=this.remoteOptions.find((e=>`${e[this.apiFieldsMap.val]}`===this.value||`${e[this.apiFieldsMap.title]}`===this.value));e&&(t=e[this.apiFieldsMap.title])}else{const e=this.selectOptions.find((e=>e.val===this.value));e&&(t=e.title)}e?(e.innerHTML=t,e.classList.add("truncate"),this.toggle.append(e)):this.toggleTextWrapper.innerHTML=t}buildTags(){this.isDisabled&&this.wrapper.classList.add("disabled"),this.buildTagsInput(),this.setTagsItems()}reassignTagsInputPlaceholder(e){this.tagsInput.placeholder=e,this.tagsInputHelper.innerHTML=e,this.calculateInputWidth()}buildTagsItem(e){var t,n,s,o,a;const l=this.getItemByValue(e);let r,d,c,u;const h=document.createElement("div");if(h.setAttribute("data-tag-value",e),this.tagsItemClasses&&(0,i.classToClassList)(this.tagsItemClasses,h),this.tagsItemTemplate&&(r=(0,i.htmlToElement)(this.tagsItemTemplate),h.append(r)),(null===(t=null==l?void 0:l.options)||void 0===t?void 0:t.icon)||this.apiIconTag){const e=(0,i.htmlToElement)(this.apiUrl&&this.apiIconTag?this.apiIconTag:null===(n=null==l?void 0:l.options)||void 0===n?void 0:n.icon);this.apiUrl&&this.apiIconTag&&l[this.apiFieldsMap.icon]&&(e.src=l[this.apiFieldsMap.icon]||""),u=r?r.querySelector("[data-icon]"):document.createElement("span"),u.append(e),r||h.append(u)}!r||!r.querySelector("[data-icon]")||(null===(s=null==l?void 0:l.options)||void 0===s?void 0:s.icon)||this.apiUrl||this.apiIconTag||l[null===(o=this.apiFieldsMap)||void 0===o?void 0:o.icon]||r.querySelector("[data-icon]").classList.add("hidden"),d=r?r.querySelector("[data-title]"):document.createElement("span"),this.apiUrl&&(null===(a=this.apiFieldsMap)||void 0===a?void 0:a.title)&&l[this.apiFieldsMap.title]?d.textContent=l[this.apiFieldsMap.title]:d.textContent=l.title||"",r||h.append(d),r?c=r.querySelector("[data-remove]"):(c=document.createElement("span"),c.textContent="X",h.append(c)),c.addEventListener("click",(()=>{this.value=this.value.filter((t=>t!==e)),this.selectedItems=this.selectedItems.filter((t=>t!==e)),this.value.length||this.reassignTagsInputPlaceholder(this.placeholder),this.unselectMultipleItems(),this.selectMultipleItems(),h.remove(),this.triggerChangeEventForNativeSelect()})),this.wrapper.append(h)}getItemByValue(e){return this.apiUrl?this.remoteOptions.find((t=>`${t[this.apiFieldsMap.val]}`===e||t[this.apiFieldsMap.title]===e)):this.selectOptions.find((t=>t.val===e))}setTagsItems(){this.value&&this.value.forEach((e=>{this.selectedItems.includes(e)||this.buildTagsItem(e),this.selectedItems=this.selectedItems.includes(e)?this.selectedItems:[...this.selectedItems,e]})),this.isOpened&&this.floatingUIInstance&&this.floatingUIInstance.update()}buildTagsInput(){this.tagsInput=document.createElement("input"),this.tagsInputId&&(this.tagsInput.id=this.tagsInputId),this.tagsInputClasses&&(0,i.classToClassList)(this.tagsInputClasses,this.tagsInput),this.onTagsInputFocusListener=()=>this.tagsInputFocus(),this.onTagsInputInputListener=()=>this.tagsInputInput(),this.onTagsInputInputSecondListener=(0,i.debounce)((e=>this.tagsInputInputSecond(e))),this.onTagsInputKeydownListener=e=>this.tagsInputKeydown(e),this.tagsInput.addEventListener("focus",this.onTagsInputFocusListener),this.tagsInput.addEventListener("input",this.onTagsInputInputListener),this.tagsInput.addEventListener("input",this.onTagsInputInputSecondListener),this.tagsInput.addEventListener("keydown",this.onTagsInputKeydownListener),this.wrapper.append(this.tagsInput),setTimeout((()=>{this.adjustInputWidth(),this.reassignTagsInputPlaceholder(this.value.length?"":this.placeholder)}))}buildDropdown(){this.dropdown=(0,i.htmlToElement)(this.dropdownTag||"<div></div>"),this.dropdown.setAttribute("data-hs-select-dropdown",""),"parent"===this.dropdownScope&&(this.dropdown.classList.add("absolute"),this.dropdownVerticalFixedPlacement||this.dropdown.classList.add("top-full")),this.dropdown.role="listbox",this.dropdown.tabIndex=-1,this.dropdown.ariaOrientation="vertical",this.isOpened||this.dropdown.classList.add("hidden"),this.dropdownClasses&&(0,i.classToClassList)(this.dropdownClasses,this.dropdown),this.wrapper&&this.wrapper.append(this.dropdown),this.dropdown&&this.hasSearch&&this.buildSearch(),this.selectOptions&&this.selectOptions.forEach(((e,t)=>this.buildOption(e.title,e.val,e.disabled,e.selected,e.options,`${t}`))),this.apiUrl&&this.optionsFromRemoteData(),"window"===this.dropdownScope&&this.buildFloatingUI(),this.dropdown&&this.apiLoadMore&&this.setupInfiniteScroll()}setupInfiniteScroll(){this.dropdown.addEventListener("scroll",this.handleScroll.bind(this))}handleScroll(){return s(this,void 0,void 0,(function*(){if(!this.dropdown||this.isLoading||!this.hasMore||!this.apiLoadMore)return;const{scrollTop:e,scrollHeight:t,clientHeight:n}=this.dropdown;t-e-n<("object"==typeof this.apiLoadMore?this.apiLoadMore.scrollThreshold:100)&&(yield this.loadMore())}))}loadMore(){return s(this,void 0,void 0,(function*(){var e,t,n,s;if(this.apiUrl&&!this.isLoading&&this.hasMore&&this.apiLoadMore){this.isLoading=!0;try{const o=new URL(this.apiUrl),i=(null===(e=this.apiFieldsMap)||void 0===e?void 0:e.page)||(null===(t=this.apiFieldsMap)||void 0===t?void 0:t.offset)||"page",a=!!(null===(n=this.apiFieldsMap)||void 0===n?void 0:n.offset),l="object"==typeof this.apiLoadMore?this.apiLoadMore.perPage:10;if(a){const e=this.currentPage*l;o.searchParams.set(i,e.toString()),this.currentPage++}else this.currentPage++,o.searchParams.set(i,this.currentPage.toString());o.searchParams.set((null===(s=this.apiFieldsMap)||void 0===s?void 0:s.limit)||"limit",l.toString());const r=yield fetch(o.toString(),this.apiOptions||{}),d=yield r.json(),c=this.apiDataPart?d[this.apiDataPart]:d.results,u=d.count||0,h=this.currentPage*l;c&&c.length>0?(this.remoteOptions=[...this.remoteOptions||[],...c],this.buildOptionsFromRemoteData(c),this.hasMore=h<u):this.hasMore=!1}catch(e){this.hasMore=!1,console.error("Error loading more options:",e)}finally{this.isLoading=!1}}}))}buildFloatingUI(){if("undefined"!=typeof FloatingUIDOM&&FloatingUIDOM.computePosition){document.body.appendChild(this.dropdown);const e="tags"===this.mode?this.wrapper:this.toggle,t=[FloatingUIDOM.offset([0,5])];this.dropdownAutoPlacement&&"function"==typeof FloatingUIDOM.flip&&t.push(FloatingUIDOM.flip({fallbackPlacements:["bottom-start","bottom-end","top-start","top-end"]}));const n={placement:l.POSITIONS[this.dropdownPlacement]||"bottom",strategy:"fixed",middleware:t},s=()=>{FloatingUIDOM.computePosition(e,this.dropdown,n).then((({x:e,y:t,placement:n})=>{Object.assign(this.dropdown.style,{position:"fixed",left:`${e}px`,top:`${t}px`,["margin"+("bottom"===n?"Top":"top"===n?"Bottom":"right"===n?"Left":"Right")]:`${this.dropdownSpace}px`}),this.dropdown.setAttribute("data-placement",n)}))};s();const o=FloatingUIDOM.autoUpdate(e,this.dropdown,s);this.floatingUIInstance={update:s,destroy:o}}else console.error("FloatingUIDOM not found! Please enable it on the page.")}updateDropdownWidth(){const e="tags"===this.mode?this.wrapper:this.toggle;this.dropdown.style.width=`${e.clientWidth}px`}buildSearch(){let e;this.searchWrapper=(0,i.htmlToElement)(this.searchWrapperTemplate||"<div></div>"),this.searchWrapperClasses&&(0,i.classToClassList)(this.searchWrapperClasses,this.searchWrapper),e=this.searchWrapper.querySelector("[data-input]");const t=(0,i.htmlToElement)(this.searchTemplate||'<input type="text">');this.search="INPUT"===t.tagName?t:t.querySelector(":scope input"),this.search.placeholder=this.searchPlaceholder,this.searchClasses&&(0,i.classToClassList)(this.searchClasses,this.search),this.searchId&&(this.search.id=this.searchId),this.onSearchInputListener=(0,i.debounce)((e=>this.searchInput(e))),this.search.addEventListener("input",this.onSearchInputListener),e?e.append(t):this.searchWrapper.append(t),this.dropdown.append(this.searchWrapper)}buildOption(e,t,n=!1,s=!1,o,a="1",l){var r;let d=null,c=null,u=null,h=null;const p=(0,i.htmlToElement)(this.optionTag||"<div></div>");if(p.setAttribute("data-value",t),p.setAttribute("data-title-value",e),p.setAttribute("tabIndex",a),p.classList.add("cursor-pointer"),p.setAttribute("data-id",l||`${this.optionId}`),l||this.optionId++,n&&p.classList.add("disabled"),s&&(this.isMultiple?this.value=[...this.value,t]:this.value=t),this.optionTemplate&&(d=(0,i.htmlToElement)(this.optionTemplate),p.append(d)),d?(c=d.querySelector("[data-title]"),c.textContent=e||""):p.textContent=e||"",o){if(o.icon){const t=(0,i.htmlToElement)(null!==(r=this.apiIconTag)&&void 0!==r?r:o.icon);if(t.classList.add("max-w-full"),this.apiUrl&&(t.setAttribute("alt",e),t.setAttribute("src",o.icon)),d)u=d.querySelector("[data-icon]"),u.append(t);else{const e=(0,i.htmlToElement)("<div></div>");this.iconClasses&&(0,i.classToClassList)(this.iconClasses,e),e.append(t),p.append(e)}}if(o.description)if(d)h=d.querySelector("[data-description]"),h&&h.append(o.description);else{const e=(0,i.htmlToElement)("<div></div>");e.textContent=o.description,this.descriptionClasses&&(0,i.classToClassList)(this.descriptionClasses,e),p.append(e)}}d&&d.querySelector("[data-icon]")&&!o&&!(null==o?void 0:o.icon)&&d.querySelector("[data-icon]").classList.add("hidden"),this.value&&(this.isMultiple?this.value.includes(t):this.value===t)&&p.classList.add("selected"),n||p.addEventListener("click",(()=>this.onSelectOption(t))),this.optionClasses&&(0,i.classToClassList)(this.optionClasses,p),this.dropdown&&this.dropdown.append(p),s&&this.setNewValue()}buildOptionFromRemoteData(e,t,n=!1,s=!1,o="1",i,a){o?this.buildOption(e,t,n,s,a,o,i):alert("ID parameter is required for generating remote options! Please check your API endpoint have it.")}buildOptionsFromRemoteData(e){e.forEach(((e,t)=>{let n=null,s="",o="";const i={id:"",val:"",title:"",icon:null,description:null,rest:{}};if(Object.keys(e).forEach((t=>{var a;e[this.apiFieldsMap.id]&&(n=e[this.apiFieldsMap.id]),e[this.apiFieldsMap.val]&&(o=`${e[this.apiFieldsMap.val]}`),e[this.apiFieldsMap.title]&&(s=e[this.apiFieldsMap.title],e[this.apiFieldsMap.val]||(o=s)),e[this.apiFieldsMap.icon]&&(i.icon=e[this.apiFieldsMap.icon]),e[null===(a=this.apiFieldsMap)||void 0===a?void 0:a.description]&&(i.description=e[this.apiFieldsMap.description]),i.rest[t]=e[t]})),!this.dropdown.querySelector(`[data-value="${o}"]`)){const e=!!this.apiSelectedValues&&(Array.isArray(this.apiSelectedValues)?this.apiSelectedValues.includes(o):this.apiSelectedValues===o);this.buildOriginalOption(s,o,n,!1,e,i),this.buildOptionFromRemoteData(s,o,!1,e,`${t}`,n,i),e&&(this.isMultiple?(this.value||(this.value=[]),Array.isArray(this.value)&&(this.value=[...this.value,o])):this.value=o)}})),this.sortElements(this.el,"option"),this.sortElements(this.dropdown,"[data-value]")}optionsFromRemoteData(){return s(this,arguments,void 0,(function*(e=""){const t=yield this.apiRequest(e);this.remoteOptions=t,t.length?this.buildOptionsFromRemoteData(this.remoteOptions):console.log("There is no data were responded!")}))}apiRequest(){return s(this,arguments,void 0,(function*(e=""){var t,n,s,o;try{let i=this.apiUrl;const a=this.apiSearchQueryKey?`${this.apiSearchQueryKey}=${e.toLowerCase()}`:null,l=this.apiQuery||"",r=this.apiOptions||{},d=new URLSearchParams(l),c=d.toString();if(this.apiLoadMore){const e=(null===(t=this.apiFieldsMap)||void 0===t?void 0:t.page)||(null===(n=this.apiFieldsMap)||void 0===n?void 0:n.offset)||"page",a=!!(null===(s=this.apiFieldsMap)||void 0===s?void 0:s.offset),l=(null===(o=this.apiFieldsMap)||void 0===o?void 0:o.limit)||"limit",r="object"==typeof this.apiLoadMore?this.apiLoadMore.perPage:10;d.delete(e),d.delete(l),i+=a?`?${e}=0`:`?${e}=1`,i+=`&${l}=${r}`}else(a||c)&&(i+=`?${a||c}`);a&&c?i+=`&${c}`:!a||c||this.apiLoadMore||(i+=`?${a}`);const u=yield fetch(i,r),h=yield u.json();return this.apiDataPart?h[this.apiDataPart]:h}catch(e){console.error(e)}}))}sortElements(e,t){const n=Array.from(e.querySelectorAll(t));this.isSelectedOptionOnTop&&n.sort(((e,t)=>{const n=e.classList.contains("selected")||e.hasAttribute("selected"),s=t.classList.contains("selected")||t.hasAttribute("selected");return n&&!s?-1:!n&&s?1:0})),n.forEach((t=>e.appendChild(t)))}remoteSearch(e){return s(this,void 0,void 0,(function*(){if(e.length<=this.minSearchLength){const e=yield this.apiRequest("");return this.remoteOptions=e,Array.from(this.dropdown.querySelectorAll("[data-value]")).forEach((e=>e.remove())),Array.from(this.el.querySelectorAll("option[value]")).forEach((e=>{e.remove()})),e.length?this.buildOptionsFromRemoteData(e):console.log("No data responded!"),!1}const t=yield this.apiRequest(e);this.remoteOptions=t;let n=t.map((e=>`${e.id}`)),s=null;const o=this.dropdown.querySelectorAll("[data-value]");this.el.querySelectorAll("[data-hs-select-option]").forEach((e=>{var t;const s=e.getAttribute("data-id");n.includes(s)||(null===(t=this.value)||void 0===t?void 0:t.includes(e.value))||this.destroyOriginalOption(e.value)})),o.forEach((e=>{var t;const s=e.getAttribute("data-id");n.includes(s)||(null===(t=this.value)||void 0===t?void 0:t.includes(e.getAttribute("data-value")))?n=n.filter((e=>e!==s)):this.destroyOption(e.getAttribute("data-value"))})),s=t.filter((e=>n.includes(`${e.id}`))),s.length?this.buildOptionsFromRemoteData(s):console.log("No data responded!")}))}destroyOption(e){const t=this.dropdown.querySelector(`[data-value="${e}"]`);if(!t)return!1;t.remove()}buildOriginalOption(e,t,n,s,o,a){const l=(0,i.htmlToElement)("<option></option>");l.setAttribute("value",t),s&&l.setAttribute("disabled","disabled"),o&&l.setAttribute("selected","selected"),n&&l.setAttribute("data-id",n),l.setAttribute("data-hs-select-option",JSON.stringify(a)),l.innerText=e,this.el.append(l)}destroyOriginalOption(e){const t=this.el.querySelector(`[value="${e}"]`);if(!t)return!1;t.remove()}buildTagsInputHelper(){this.tagsInputHelper=document.createElement("span"),this.tagsInputHelper.style.fontSize=window.getComputedStyle(this.tagsInput).fontSize,this.tagsInputHelper.style.fontFamily=window.getComputedStyle(this.tagsInput).fontFamily,this.tagsInputHelper.style.fontWeight=window.getComputedStyle(this.tagsInput).fontWeight,this.tagsInputHelper.style.letterSpacing=window.getComputedStyle(this.tagsInput).letterSpacing,this.tagsInputHelper.style.visibility="hidden",this.tagsInputHelper.style.whiteSpace="pre",this.tagsInputHelper.style.position="absolute",this.wrapper.appendChild(this.tagsInputHelper)}calculateInputWidth(){this.tagsInputHelper.textContent=this.tagsInput.value||this.tagsInput.placeholder;const e=parseInt(window.getComputedStyle(this.tagsInput).paddingLeft)+parseInt(window.getComputedStyle(this.tagsInput).paddingRight),t=parseInt(window.getComputedStyle(this.tagsInput).borderLeftWidth)+parseInt(window.getComputedStyle(this.tagsInput).borderRightWidth),n=this.tagsInputHelper.offsetWidth+e+t,s=this.wrapper.offsetWidth-(parseInt(window.getComputedStyle(this.wrapper).paddingLeft)+parseInt(window.getComputedStyle(this.wrapper).paddingRight));this.tagsInput.style.width=`${Math.min(n,s)+2}px`}adjustInputWidth(){this.buildTagsInputHelper(),this.calculateInputWidth()}onSelectOption(e){if(this.clearSelections(),this.isMultiple?(this.value=this.value.includes(e)?Array.from(this.value).filter((t=>t!==e)):[...Array.from(this.value),e],this.selectMultipleItems(),this.setNewValue()):(this.value=e,this.selectSingleItem(),this.setNewValue()),this.fireEvent("change",this.value),"tags"===this.mode){const e=this.selectedItems.filter((e=>!this.value.includes(e)));e.length&&e.forEach((e=>{this.selectedItems=this.selectedItems.filter((t=>t!==e)),this.wrapper.querySelector(`[data-tag-value="${e}"]`).remove()})),this.resetTagsInputField()}this.isMultiple||(this.toggle.querySelector("[data-icon]")&&this.setToggleIcon(),this.toggle.querySelector("[data-title]")&&this.setToggleTitle(),this.close(!0)),this.value.length||"tags"!==this.mode||this.reassignTagsInputPlaceholder(this.placeholder),this.isOpened&&"tags"===this.mode&&this.tagsInput&&this.tagsInput.focus(),this.triggerChangeEventForNativeSelect()}triggerChangeEventForNativeSelect(){const e=new Event("change",{bubbles:!0});this.el.dispatchEvent(e),(0,i.dispatch)("change.hs.select",this.el,this.value)}addSelectOption(e,t,n,s,o){this.selectOptions=[...this.selectOptions,{title:e,val:t,disabled:n,selected:s,options:o}]}removeSelectOption(e,t=!1){if(!this.selectOptions.some((t=>t.val===e)))return!1;this.selectOptions=this.selectOptions.filter((t=>t.val!==e)),this.value=t?this.value.filter((t=>t!==e)):e}resetTagsInputField(){this.tagsInput.value="",this.reassignTagsInputPlaceholder(""),this.searchOptions("")}clearSelections(){Array.from(this.dropdown.children).forEach((e=>{e.classList.contains("selected")&&e.classList.remove("selected")})),Array.from(this.el.children).forEach((e=>{e.selected&&(e.selected=!1)}))}setNewValue(){if("tags"===this.mode)this.setTagsItems();else if(this.optionAllowEmptyOption&&""===this.value){const e=this.selectOptions.find((e=>""===e.val));this.toggleTextWrapper.innerHTML=(null==e?void 0:e.title)||this.placeholder}else if(this.value)if(this.apiUrl){const e=this.dropdown.querySelector(`[data-value="${this.value}"]`);if(e)this.toggleTextWrapper.innerHTML=e.getAttribute("data-title-value")||this.placeholder;else{const e=this.remoteOptions.find((e=>(e[this.apiFieldsMap.val]?`${e[this.apiFieldsMap.val]}`:e[this.apiFieldsMap.title])===this.value));this.toggleTextWrapper.innerHTML=e?`${e[this.apiFieldsMap.title]}`:this.stringFromValue()}}else this.toggleTextWrapper.innerHTML=this.stringFromValue();else this.toggleTextWrapper.innerHTML=this.placeholder}stringFromValueBasic(e){var t;const n=[];let s="";if(e.forEach((e=>{this.isMultiple?this.value.includes(e.val)&&n.push(e.title):this.value===e.val&&n.push(e.title)})),void 0!==this.toggleCountText&&null!==this.toggleCountText&&n.length>=this.toggleCountTextMinItems)if("nItemsAndCount"===this.toggleCountTextMode){const e=n.slice(0,this.toggleCountTextMinItems-1),o=[e.join(this.toggleSeparators.items)],i=""+(n.length-e.length);if((null===(t=null==this?void 0:this.toggleSeparators)||void 0===t?void 0:t.betweenItemsAndCounter)&&o.push(this.toggleSeparators.betweenItemsAndCounter),this.toggleCountText)switch(this.toggleCountTextPlacement){case"postfix-no-space":o.push(`${i}${this.toggleCountText}`);break;case"prefix-no-space":o.push(`${this.toggleCountText}${i}`);break;case"prefix":o.push(`${this.toggleCountText} ${i}`);break;default:o.push(`${i} ${this.toggleCountText}`)}s=o.join(" ")}else s=`${n.length} ${this.toggleCountText}`;else s=n.join(this.toggleSeparators.items);return s}stringFromValueRemoteData(){const e=this.dropdown.querySelectorAll("[data-title-value]"),t=[];let n="";if(e.forEach((e=>{const n=e.getAttribute("data-value"),s=e.getAttribute("data-title-value");this.isMultiple?this.value.includes(n)&&t.push(s):this.value===n&&t.push(s)})),this.toggleCountText&&""!==this.toggleCountText&&t.length>=this.toggleCountTextMinItems)if("nItemsAndCount"===this.toggleCountTextMode){const e=t.slice(0,this.toggleCountTextMinItems-1);n=`${e.join(this.toggleSeparators.items)} ${this.toggleSeparators.betweenItemsAndCounter} ${t.length-e.length} ${this.toggleCountText}`}else n=`${t.length} ${this.toggleCountText}`;else n=t.join(this.toggleSeparators.items);return n}stringFromValue(){return this.apiUrl?this.stringFromValueRemoteData():this.stringFromValueBasic(this.selectOptions)}selectSingleItem(){Array.from(this.el.children).find((e=>this.value===e.value)).selected=!0;const e=Array.from(this.dropdown.children).find((e=>this.value===e.getAttribute("data-value")));e&&e.classList.add("selected")}selectMultipleItems(){Array.from(this.dropdown.children).filter((e=>this.value.includes(e.getAttribute("data-value")))).forEach((e=>e.classList.add("selected"))),Array.from(this.el.children).filter((e=>this.value.includes(e.value))).forEach((e=>e.selected=!0))}unselectMultipleItems(){Array.from(this.dropdown.children).forEach((e=>e.classList.remove("selected"))),Array.from(this.el.children).forEach((e=>e.selected=!1))}searchOptions(e){if(e.length<=this.minSearchLength)return this.searchNoResult&&(this.searchNoResult.remove(),this.searchNoResult=null),this.dropdown.querySelectorAll("[data-value]").forEach((e=>{e.classList.remove("hidden")})),!1;this.searchNoResult&&(this.searchNoResult.remove(),this.searchNoResult=null),this.searchNoResult=(0,i.htmlToElement)(this.searchNoResultTemplate),this.searchNoResult.innerText=this.searchNoResultText,(0,i.classToClassList)(this.searchNoResultClasses,this.searchNoResult);const t=this.dropdown.querySelectorAll("[data-value]");let n,s=!1;this.searchLimit&&(n=0),t.forEach((t=>{const o=t.getAttribute("data-title-value").toLocaleLowerCase();let i;if(this.isSearchDirectMatch)i=!o.includes(e.toLowerCase())||this.searchLimit&&n>=this.searchLimit;else{const t=e?e.split("").map((e=>/\w/.test(e)?`${e}[\\W_]*`:"\\W*")).join(""):"";i=!new RegExp(t,"i").test(o.trim())||this.searchLimit&&n>=this.searchLimit}i?t.classList.add("hidden"):(t.classList.remove("hidden"),s=!0,this.searchLimit&&n++)})),s||this.dropdown.append(this.searchNoResult)}eraseToggleIcon(){const e=this.toggle.querySelector("[data-icon]");e&&(e.innerHTML=null,e.classList.add("hidden"))}eraseToggleTitle(){const e=this.toggle.querySelector("[data-title]");e?e.innerHTML=this.placeholder:this.toggleTextWrapper.innerHTML=this.placeholder}toggleFn(){this.isOpened?this.close():this.open()}destroy(){this.wrapper&&this.wrapper.removeEventListener("click",this.onWrapperClickListener),this.toggle&&this.toggle.removeEventListener("click",this.onToggleClickListener),this.tagsInput&&(this.tagsInput.removeEventListener("focus",this.onTagsInputFocusListener),this.tagsInput.removeEventListener("input",this.onTagsInputInputListener),this.tagsInput.removeEventListener("input",this.onTagsInputInputSecondListener),this.tagsInput.removeEventListener("keydown",this.onTagsInputKeydownListener)),this.search&&this.search.removeEventListener("input",this.onSearchInputListener);const e=this.el.parentElement.parentElement;this.el.classList.add("hidden"),this.el.style.display="",e.prepend(this.el),e.querySelector(".hs-select").remove(),this.wrapper=null,window.$hsSelectCollection=window.$hsSelectCollection.filter((({element:e})=>e.el!==this.el))}open(){var e;const t=(null===(e=null===window||void 0===window?void 0:window.$hsSelectCollection)||void 0===e?void 0:e.find((e=>e.element.isOpened)))||null;if(t&&t.element.close(),this.animationInProcess)return!1;this.animationInProcess=!0,"window"===this.dropdownScope&&this.dropdown.classList.add("invisible"),this.dropdown.classList.remove("hidden"),"window"!==this.dropdownScope&&this.recalculateDirection(),setTimeout((()=>{var e;(null===(e=null==this?void 0:this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.toggle.ariaExpanded="true"),this.wrapper.classList.add("active"),this.dropdown.classList.add("opened"),this.dropdown.classList.contains("w-full")&&"window"===this.dropdownScope&&this.updateDropdownWidth(),this.floatingUIInstance&&"window"===this.dropdownScope&&(this.floatingUIInstance.update(),this.dropdown.classList.remove("invisible")),this.hasSearch&&!this.preventSearchFocus&&this.search.focus(),this.animationInProcess=!1})),this.isOpened=!0}close(e=!1){var t,n,s,o;if(this.animationInProcess)return!1;this.animationInProcess=!0,(null===(t=null==this?void 0:this.toggle)||void 0===t?void 0:t.ariaExpanded)&&(this.toggle.ariaExpanded="false"),this.wrapper.classList.remove("active"),this.dropdown.classList.remove("opened","bottom-full","top-full"),(null===(n=this.dropdownDirectionClasses)||void 0===n?void 0:n.bottom)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.bottom),(null===(s=this.dropdownDirectionClasses)||void 0===s?void 0:s.top)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop="",this.dropdown.style.marginBottom="",(0,i.afterTransition)(this.dropdown,(()=>{this.dropdown.classList.add("hidden"),this.hasSearch&&(this.search.value="",this.search.dispatchEvent(new Event("input",{bubbles:!0})),this.search.blur()),e&&this.toggle.focus(),this.animationInProcess=!1})),null===(o=this.dropdown.querySelector(".hs-select-option-highlighted"))||void 0===o||o.classList.remove("hs-select-option-highlighted"),this.isOpened=!1}addOption(e){let t=`${this.selectOptions.length}`;const n=e=>{const{title:n,val:s,disabled:o,selected:i,options:a}=e;this.selectOptions.some((e=>e.val===s))||(this.addSelectOption(n,s,o,i,a),this.buildOption(n,s,o,i,a,t),this.buildOriginalOption(n,s,null,o,i,a),i&&!this.isMultiple&&this.onSelectOption(s))};Array.isArray(e)?e.forEach((e=>{n(e)})):n(e)}removeOption(e){const t=(e,t=!1)=>{this.selectOptions.some((t=>t.val===e))&&(this.removeSelectOption(e,t),this.destroyOption(e),this.destroyOriginalOption(e),this.value===e&&(this.value=null,this.eraseToggleTitle(),this.eraseToggleIcon()))};Array.isArray(e)?e.forEach((e=>{t(e,this.isMultiple)})):t(e,this.isMultiple),this.setNewValue()}recalculateDirection(){var e,t,n,s;if((null==this?void 0:this.dropdownVerticalFixedPlacement)&&(this.dropdown.classList.contains("bottom-full")||this.dropdown.classList.contains("top-full")))return!1;"top"===(null==this?void 0:this.dropdownVerticalFixedPlacement)?(this.dropdown.classList.add("bottom-full"),this.dropdown.style.marginBottom=`${this.dropdownSpace}px`):"bottom"===(null==this?void 0:this.dropdownVerticalFixedPlacement)?(this.dropdown.classList.add("top-full"),this.dropdown.style.marginTop=`${this.dropdownSpace}px`):(0,i.isEnoughSpace)(this.dropdown,this.toggle||this.tagsInput,"bottom",this.dropdownSpace,this.viewport)?(this.dropdown.classList.remove("bottom-full"),(null===(e=this.dropdownDirectionClasses)||void 0===e?void 0:e.bottom)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.bottom),this.dropdown.style.marginBottom="",this.dropdown.classList.add("top-full"),(null===(t=this.dropdownDirectionClasses)||void 0===t?void 0:t.top)&&this.dropdown.classList.add(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop=`${this.dropdownSpace}px`):(this.dropdown.classList.remove("top-full"),(null===(n=this.dropdownDirectionClasses)||void 0===n?void 0:n.top)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop="",this.dropdown.classList.add("bottom-full"),(null===(s=this.dropdownDirectionClasses)||void 0===s?void 0:s.bottom)&&this.dropdown.classList.add(this.dropdownDirectionClasses.bottom),this.dropdown.style.marginBottom=`${this.dropdownSpace}px`)}static findInCollection(e){return window.$hsSelectCollection.find((t=>e instanceof r?t.element.el===e.el:"string"==typeof e?t.element.el===document.querySelector(e):t.element.el===e))||null}static getInstance(e,t){const n=window.$hsSelectCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return n?t?n:n.element:null}static autoInit(){window.$hsSelectCollection||(window.$hsSelectCollection=[],window.addEventListener("click",(e=>{const t=e.target;r.closeCurrentlyOpened(t)})),document.addEventListener("keydown",(e=>r.accessibility(e)))),window.$hsSelectCollection&&(window.$hsSelectCollection=window.$hsSelectCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll("[data-hs-select]:not(.--prevent-on-load-init)").forEach((e=>{if(!window.$hsSelectCollection.find((t=>{var n;return(null===(n=null==t?void 0:t.element)||void 0===n?void 0:n.el)===e}))){const t=e.getAttribute("data-hs-select"),n=t?JSON.parse(t):{};new r(e,n)}}))}static open(e){const t=r.findInCollection(e);t&&!t.element.isOpened&&t.element.open()}static close(e){const t=r.findInCollection(e);t&&t.element.isOpened&&t.element.close()}static closeCurrentlyOpened(e=null){if(!e.closest(".hs-select.active")&&!e.closest("[data-hs-select-dropdown].opened")){const e=window.$hsSelectCollection.filter((e=>e.element.isOpened))||null;e&&e.forEach((e=>{e.element.close()}))}}static accessibility(e){const t=window.$hsSelectCollection.find((e=>e.element.isOpened));if(t&&l.SELECT_ACCESSIBILITY_KEY_SET.includes(e.code)&&!e.metaKey)switch(e.code){case"Escape":e.preventDefault(),this.onEscape();break;case"ArrowUp":e.preventDefault(),e.stopImmediatePropagation(),this.onArrow();break;case"ArrowDown":e.preventDefault(),e.stopImmediatePropagation(),this.onArrow(!1);break;case"Tab":e.preventDefault(),e.stopImmediatePropagation(),this.onTab(e.shiftKey);break;case"Home":e.preventDefault(),e.stopImmediatePropagation(),this.onStartEnd();break;case"End":e.preventDefault(),e.stopImmediatePropagation(),this.onStartEnd(!1);break;case"Enter":e.preventDefault(),this.onEnter(e);break;case"Space":if((0,i.isFocused)(t.element.search))break;e.preventDefault(),this.onEnter(e)}}static onEscape(){const e=window.$hsSelectCollection.find((e=>e.element.isOpened));e&&e.element.close()}static onArrow(e=!0){const t=window.$hsSelectCollection.find((e=>e.element.isOpened));if(t){const n=t.element.dropdown;if(!n)return!1;const s=(e?Array.from(n.querySelectorAll(":scope > *:not(.hidden)")).reverse():Array.from(n.querySelectorAll(":scope > *:not(.hidden)"))).filter((e=>!e.classList.contains("disabled"))),o=n.querySelector(".hs-select-option-highlighted")||n.querySelector(".selected");o||s[0].classList.add("hs-select-option-highlighted");let i=s.findIndex((e=>e===o));i+1<s.length&&i++,s[i].focus(),o&&o.classList.remove("hs-select-option-highlighted"),s[i].classList.add("hs-select-option-highlighted")}}static onTab(e=!0){const t=window.$hsSelectCollection.find((e=>e.element.isOpened));if(t){const n=t.element.dropdown;if(!n)return!1;const s=(e?Array.from(n.querySelectorAll(":scope >  *:not(.hidden)")).reverse():Array.from(n.querySelectorAll(":scope >  *:not(.hidden)"))).filter((e=>!e.classList.contains("disabled"))),o=n.querySelector(".hs-select-option-highlighted")||n.querySelector(".selected");o||s[0].classList.add("hs-select-option-highlighted");let i=s.findIndex((e=>e===o));if(!(i+1<s.length))return o&&o.classList.remove("hs-select-option-highlighted"),t.element.close(),t.element.toggle.focus(),!1;i++,s[i].focus(),o&&o.classList.remove("hs-select-option-highlighted"),s[i].classList.add("hs-select-option-highlighted")}}static onStartEnd(e=!0){const t=window.$hsSelectCollection.find((e=>e.element.isOpened));if(t){const n=t.element.dropdown;if(!n)return!1;const s=(e?Array.from(n.querySelectorAll(":scope >  *:not(.hidden)")):Array.from(n.querySelectorAll(":scope >  *:not(.hidden)")).reverse()).filter((e=>!e.classList.contains("disabled"))),o=n.querySelector(".hs-select-option-highlighted");s.length&&(s[0].focus(),o&&o.classList.remove("hs-select-option-highlighted"),s[0].classList.add("hs-select-option-highlighted"))}}static onEnter(e){const t=e.target.previousSibling;if(window.$hsSelectCollection.find((e=>e.element.el===t))){const e=window.$hsSelectCollection.find((e=>e.element.isOpened)),n=window.$hsSelectCollection.find((e=>e.element.el===t));e.element.close(),e!==n&&n.element.open()}else{const t=window.$hsSelectCollection.find((e=>e.element.isOpened));t&&t.element.onSelectOption(e.target.dataset.value||"")}}}window.addEventListener("load",(()=>{r.autoInit()})),document.addEventListener("scroll",(()=>{if(!window.$hsSelectCollection)return!1;const e=window.$hsSelectCollection.find((e=>e.element.isOpened));e&&e.element.recalculateDirection()})),"undefined"!=typeof window&&(window.HSSelect=r),t.default=r},632:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const s=n(359);class o extends s.Calendar{constructor(e,t){super(e,t);const n=this.set;this.set=(e,t)=>{n&&n.call(this,e,t),e.selectedTime&&this.onChangeTime&&this.onChangeTime(this,null,!0),e.selectedMonth&&this.onClickMonth&&this.onClickMonth(this,null),e.selectedYear&&this.onClickYear&&this.onClickYear(this,null)}}static get defaultStyles(){return{calendar:"vc",controls:"vc-controls",grid:"vc-grid",column:"vc-column",header:"vc-header",headerContent:"vc-header__content",month:"vc-month",year:"vc-year",arrowPrev:"vc-arrow vc-arrow_prev",arrowNext:"vc-arrow vc-arrow_next",wrapper:"vc-wrapper",content:"vc-content",months:"vc-months",monthsMonth:"vc-months__month",years:"vc-years",yearsYear:"vc-years__year",week:"vc-week",weekDay:"vc-week__day",weekNumbers:"vc-week-numbers",weekNumbersTitle:"vc-week-numbers__title",weekNumbersContent:"vc-week-numbers__content",weekNumber:"vc-week-number",dates:"vc-dates",date:"vc-date",dateBtn:"vc-date__btn",datePopup:"vc-date__popup",dateRangeTooltip:"vc-date-range-tooltip",time:"vc-time",timeContent:"vc-time__content",timeHour:"vc-time__hour",timeMinute:"vc-time__minute",timeKeeping:"vc-time__keeping",timeRanges:"vc-time__ranges",timeRange:"vc-time__range"}}logInfo(){console.log("This log is from CustomVanillaCalendar!",this)}}t.default=o},961:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=class{constructor(e,t,n){this.el=e,this.options=t,this.events=n,this.el=e,this.options=t,this.events={}}createCollection(e,t){var n;e.push({id:(null===(n=null==t?void 0:t.el)||void 0===n?void 0:n.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={},function n(s){var o=t[s];if(void 0!==o)return o.exports;var i=t[s]={exports:{}};return e[s].call(i.exports,i,i.exports,n),i.exports}(128);var e,t}));