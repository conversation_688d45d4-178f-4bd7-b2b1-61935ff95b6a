{"name": "@mdx-js/loader", "version": "3.1.0", "description": "Webpack loader for MDX", "license": "MIT", "keywords": ["jsx", "markdown", "mdx", "preact", "react", "remark", "vue", "webpack"], "homepage": "https://mdxjs.com", "repository": {"type": "git", "url": "https://github.com/mdx-js/mdx", "directory": "packages/loader/"}, "bugs": "https://github.com/mdx-js/mdx/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://johno.com)", "contributors": ["<PERSON> <<EMAIL>> (https://johno.com)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <matija.ma<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stg.me)", "<PERSON> <<EMAIL>>"], "type": "module", "sideEffects": false, "exports": "./index.cjs", "files": ["lib/", "index.cjs", "index.d.cts.map", "index.d.cts"], "dependencies": {"@mdx-js/mdx": "^3.0.0", "source-map": "^0.7.0"}, "peerDependencies": {"webpack": ">=5"}, "peerDependenciesMeta": {"webpack": {"optional": true}}, "devDependencies": {}, "scripts": {"test": "npm run test-coverage", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api"}, "xo": {"prettier": true, "rules": {"n/file-extension-in-import": "off"}}}