var e={615:(e,t,l)=>{l.d(t,{A:()=>o});class o{constructor(e,t,l){this.el=e,this.options=t,this.events=l,this.el=e,this.options=t,this.events={}}createCollection(e,t){var l;e.push({id:(null===(l=null==t?void 0:t.el)||void 0===l?void 0:l.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}},926:(e,t,l)=>{l.d(t,{JD:()=>s,gj:()=>o});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const o=(e,t,l="")=>(window.getComputedStyle(e).getPropertyValue(t)||l).replace(" ",""),s=(e,t,l=null)=>{const o=new CustomEvent(e,{detail:{payload:l},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(o)}}},t={};function l(o){var s=t[o];if(void 0!==s)return s.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,l),i.exports}l.d=(e,t)=>{for(var o in t)l.o(t,o)&&!l.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o={};l.d(o,{A:()=>n});var s=l(926),i=l(615);
/*
 * HSScrollspy
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
class r extends i.A{constructor(e,t={}){super(e,t),this.isScrollingDown=!1,this.lastScrollTop=0;const l=e.getAttribute("data-hs-scrollspy-options"),o=l?JSON.parse(l):{},s=Object.assign(Object.assign({},o),t);this.ignoreScrollUp=void 0!==s.ignoreScrollUp&&s.ignoreScrollUp,this.links=this.el.querySelectorAll("[href]"),this.sections=[],this.scrollableId=this.el.getAttribute("data-hs-scrollspy-scrollable-parent"),this.scrollable=this.scrollableId?document.querySelector(this.scrollableId):document,this.onLinkClickListener=[],this.init()}scrollableScroll(e){const t=this.scrollable instanceof HTMLElement?this.scrollable.scrollTop:window.scrollY;this.isScrollingDown=t>this.lastScrollTop,this.lastScrollTop=t<=0?0:t,Array.from(this.sections).forEach((t=>{if(!t.getAttribute("id"))return!1;this.update(e,t)}))}init(){this.createCollection(window.$hsScrollspyCollection,this),this.links.forEach((e=>{this.sections.push(this.scrollable.querySelector(e.getAttribute("href")))})),this.onScrollableScrollListener=e=>this.scrollableScroll(e),this.scrollable.addEventListener("scroll",this.onScrollableScrollListener),this.links.forEach((e=>{this.onLinkClickListener.push({el:e,fn:t=>this.linkClick(t,e)}),e.addEventListener("click",this.onLinkClickListener.find((t=>t.el===e)).fn)}))}determineScrollDirection(e){const t=this.el.querySelector("a.active");if(!t)return!0;const l=Array.from(this.links).indexOf(t),o=Array.from(this.links).indexOf(e);return-1===o||o>l}linkClick(e,t){e.preventDefault();const l=t.getAttribute("href");if(!l||"javascript:;"===l)return;(l?document.querySelector(l):null)&&(this.isScrollingDown=this.determineScrollDirection(t),this.scrollTo(t))}update(e,t){const l=parseInt((0,s.gj)(this.el,"--scrollspy-offset","0")),o=parseInt((0,s.gj)(t,"--scrollspy-offset"))||l,i=e.target===document?0:parseInt(String(e.target.getBoundingClientRect().top)),r=parseInt(String(t.getBoundingClientRect().top))-o-i,n=t.offsetHeight;if(this.ignoreScrollUp||this.isScrollingDown?r<=0&&r+n>0:r<=0&&r<n){this.links.forEach((e=>e.classList.remove("active")));const e=this.el.querySelector(`[href="#${t.getAttribute("id")}"]`);if(e){e.classList.add("active");const t=e.closest("[data-hs-scrollspy-group]");if(t){const e=t.querySelector("[href]");e&&e.classList.add("active")}}this.fireEvent("afterScroll",e),(0,s.JD)("afterScroll.hs.scrollspy",e,this.el)}}scrollTo(e){const t=e.getAttribute("href"),l=document.querySelector(t),o=parseInt((0,s.gj)(this.el,"--scrollspy-offset","0")),i=parseInt((0,s.gj)(l,"--scrollspy-offset"))||o,r=this.scrollable===document?0:this.scrollable.offsetTop,n=l.offsetTop-i-r,c=this.scrollable===document?window:this.scrollable,a=()=>{window.history.replaceState(null,null,e.getAttribute("href")),"scrollTo"in c&&c.scrollTo({top:n,left:0,behavior:"smooth"})},h=this.fireEvent("beforeScroll",this.el);(0,s.JD)("beforeScroll.hs.scrollspy",this.el,this.el),h instanceof Promise?h.then((()=>a())):a()}destroy(){this.el.querySelector("[href].active").classList.remove("active"),this.scrollable.removeEventListener("scroll",this.onScrollableScrollListener),this.onLinkClickListener.length&&this.onLinkClickListener.forEach((({el:e,fn:t})=>{e.removeEventListener("click",t)})),window.$hsScrollspyCollection=window.$hsScrollspyCollection.filter((({element:e})=>e.el!==this.el))}static getInstance(e,t=!1){const l=window.$hsScrollspyCollection.find((t=>t.element.el===("string"==typeof e?document.querySelector(e):e)));return l?t?l:l.element.el:null}static autoInit(){window.$hsScrollspyCollection||(window.$hsScrollspyCollection=[]),window.$hsScrollspyCollection&&(window.$hsScrollspyCollection=window.$hsScrollspyCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll("[data-hs-scrollspy]:not(.--prevent-on-load-init)").forEach((e=>{window.$hsScrollspyCollection.find((t=>{var l;return(null===(l=null==t?void 0:t.element)||void 0===l?void 0:l.el)===e}))||new r(e)}))}}window.addEventListener("load",(()=>{r.autoInit()})),"undefined"!=typeof window&&(window.HSScrollspy=r);const n=r;var c=o.A;export{c as default};