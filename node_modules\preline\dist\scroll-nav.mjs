var t={615:(t,e,i)=>{i.d(e,{A:()=>s});class s{constructor(t,e,i){this.el=t,this.options=e,this.events=i,this.el=t,this.options=e,this.events={}}createCollection(t,e){var i;t.push({id:(null===(i=null==e?void 0:e.el)||void 0===i?void 0:i.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}},926:(t,e,i)=>{i.d(e,{sg:()=>s});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const s=(t,e=200)=>{let i;return(...s)=>{clearTimeout(i),i=setTimeout((()=>{t.apply(void 0,s)}),e)}}}},e={};function i(s){var n=e[s];if(void 0!==n)return n.exports;var r=e[s]={exports:{}};return t[s](r,r.exports,i),r.exports}i.d=(t,e)=>{for(var s in e)i.o(e,s)&&!i.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};i.d(s,{A:()=>o});var n=i(926),r=i(615);
/*
 * HSScrollNav
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
class l extends r.A{constructor(t,e){var i,s;super(t,e);const n=t.getAttribute("data-hs-scroll-nav"),r=n?JSON.parse(n):{},l=Object.assign(Object.assign(Object.assign({},{paging:!0,autoCentering:!1}),r),e);this.paging=null===(i=l.paging)||void 0===i||i,this.autoCentering=null!==(s=l.autoCentering)&&void 0!==s&&s,this.body=this.el.querySelector(".hs-scroll-nav-body"),this.items=this.body?Array.from(this.body.querySelectorAll(":scope > *")):[],this.prev=this.el.querySelector(".hs-scroll-nav-prev")||null,this.next=this.el.querySelector(".hs-scroll-nav-next")||null,this.setCurrentState(),this.init()}init(){if(!this.body||!this.items.length)return!1;this.createCollection(window.$hsScrollNavCollection,this),this.setCurrentState(),this.paging?(this.prev&&this.buildPrev(),this.next&&this.buildNext()):(this.prev&&this.buildPrevSingle(),this.next&&this.buildNextSingle()),this.autoCentering&&this.scrollToActiveElement(),this.body.addEventListener("scroll",(0,n.sg)((()=>this.setCurrentState()),200)),window.addEventListener("resize",(0,n.sg)((()=>{this.setCurrentState(),this.autoCentering&&this.scrollToActiveElement()}),200))}setCurrentState(){this.currentState={first:this.getFirstVisibleItem(),last:this.getLastVisibleItem(),center:this.getCenterVisibleItem()},this.prev&&this.setPrevToDisabled(),this.next&&this.setNextToDisabled()}setPrevToDisabled(){this.currentState.first===this.items[0]?(this.prev.setAttribute("disabled","disabled"),this.prev.classList.add("disabled")):(this.prev.removeAttribute("disabled"),this.prev.classList.remove("disabled"))}setNextToDisabled(){this.currentState.last===this.items[this.items.length-1]?(this.next.setAttribute("disabled","disabled"),this.next.classList.add("disabled")):(this.next.removeAttribute("disabled"),this.next.classList.remove("disabled"))}buildPrev(){this.prev&&this.prev.addEventListener("click",(()=>{const t=this.currentState.first;if(!t)return;const e=this.getVisibleItemsCount();let i=t;for(let t=0;t<e&&i.previousElementSibling;t++)i=i.previousElementSibling;this.goTo(i)}))}buildNext(){this.next&&this.next.addEventListener("click",(()=>{const t=this.currentState.last;if(!t)return;const e=this.getVisibleItemsCount();let i=t;for(let t=0;t<e&&i.nextElementSibling;t++)i=i.nextElementSibling;this.goTo(i)}))}buildPrevSingle(){var t;null===(t=this.prev)||void 0===t||t.addEventListener("click",(()=>{const t=this.currentState.first;if(!t)return;const e=t.previousElementSibling;e&&this.goTo(e)}))}buildNextSingle(){var t;null===(t=this.next)||void 0===t||t.addEventListener("click",(()=>{const t=this.currentState.last;if(!t)return;const e=t.nextElementSibling;e&&this.goTo(e)}))}getCenterVisibleItem(){const t=this.body.scrollLeft+this.body.clientWidth/2;let e=null,i=1/0;return this.items.forEach((s=>{const n=s.offsetLeft+s.offsetWidth/2,r=Math.abs(n-t);r<i&&(i=r,e=s)})),e}getFirstVisibleItem(){const t=this.body.getBoundingClientRect();for(let e of this.items){const i=e.getBoundingClientRect();if(i.left>=t.left&&i.right<=t.right)return e}return null}getLastVisibleItem(){const t=this.body.getBoundingClientRect();for(let e=this.items.length-1;e>=0;e--){const i=this.items[e],s=i.getBoundingClientRect();if(s.left<t.right&&s.right>t.left)return i}return null}getVisibleItemsCount(){const t=this.body.clientWidth;let e=0,i=0;for(let s of this.items){if(i+=s.offsetWidth,!(i<=t))break;e++}return e}scrollToActiveElement(){const t=this.body.querySelector(".active");if(!t)return!1;this.centerElement(t)}getCurrentState(){return this.currentState}goTo(t,e){t.scrollIntoView({behavior:"smooth",block:"nearest",inline:"nearest"});new IntersectionObserver(((i,s)=>{i.forEach((i=>{i.target===t&&i.isIntersecting&&("function"==typeof e&&e(),s.disconnect())}))}),{root:this.body,threshold:1}).observe(t)}centerElement(t,e="smooth"){if(!this.body.contains(t))return;const i=t.offsetLeft+t.offsetWidth/2-this.body.clientWidth/2;this.body.scrollTo({left:i,behavior:e})}destroy(){this.paging?(this.prev&&this.prev.removeEventListener("click",this.buildPrev),this.next&&this.next.removeEventListener("click",this.buildNext)):(this.prev&&this.prev.removeEventListener("click",this.buildPrevSingle),this.next&&this.next.removeEventListener("click",this.buildNextSingle)),window.removeEventListener("resize",(0,n.sg)((()=>this.setCurrentState()),200)),window.$hsScrollNavCollection=window.$hsScrollNavCollection.filter((({element:t})=>t.el!==this.el))}static getInstance(t,e){const i=window.$hsScrollNavCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)||e.element.el===("string"==typeof t?document.querySelector(t):t)));return i?e?i:i.element.el:null}static autoInit(){window.$hsScrollNavCollection||(window.$hsScrollNavCollection=[]),window.$hsScrollNavCollection&&(window.$hsRemoveElementCollection=window.$hsRemoveElementCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll("[data-hs-scroll-nav]:not(.--prevent-on-load-init)").forEach((t=>{window.$hsScrollNavCollection.find((e=>{var i;return(null===(i=null==e?void 0:e.element)||void 0===i?void 0:i.el)===t}))||new l(t)}))}}window.addEventListener("load",(()=>{l.autoInit()})),"undefined"!=typeof window&&(window.HSScrollNav=l);const o=l;var h=s.A;export{h as default};