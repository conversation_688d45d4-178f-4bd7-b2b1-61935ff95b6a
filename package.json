{"name": "verictus", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:static": "next build", "serve:static": "cd out && python -m http.server 8080", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@next/mdx": "^14.0.0", "autoprefixer": "^10.4.21", "lucide-react": "^0.294.0", "next": "14.2.15", "preline": "^3.1.0", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}