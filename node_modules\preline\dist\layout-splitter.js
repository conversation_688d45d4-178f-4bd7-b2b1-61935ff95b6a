!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var n in i)("object"==typeof exports?exports:t)[n]=i[n]}}(self,(()=>(()=>{"use strict";var t={292:function(t,e){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(e,"__esModule",{value:!0}),e.menuSearchHistory=e.classToClassList=e.htmlToElement=e.afterTransition=e.dispatch=e.debounce=e.isScrollable=e.isParentOrElementHidden=e.isJson=e.isIpadOS=e.isIOS=e.isDirectChild=e.isFormElement=e.isFocused=e.isEnoughSpace=e.getHighestZIndex=e.getZIndex=e.getClassPropertyAlt=e.getClassProperty=e.stringToBoolean=void 0;e.stringToBoolean=t=>"true"===t;e.getClassProperty=(t,e,i="")=>(window.getComputedStyle(t).getPropertyValue(e)||i).replace(" ","");e.getClassPropertyAlt=(t,e,i="")=>{let n="";return t.classList.forEach((t=>{t.includes(e)&&(n=t)})),n.match(/:(.*)]/)?n.match(/:(.*)]/)[1]:i};const i=t=>window.getComputedStyle(t).getPropertyValue("z-index");e.getZIndex=i;e.getHighestZIndex=t=>{let e=Number.NEGATIVE_INFINITY;return t.forEach((t=>{let n=i(t);"auto"!==n&&(n=parseInt(n,10),n>e&&(e=n))})),e};e.isDirectChild=(t,e)=>{const i=t.children;for(let t=0;t<i.length;t++)if(i[t]===e)return!0;return!1};e.isEnoughSpace=(t,e,i="auto",n=10,o=null)=>{const l=e.getBoundingClientRect(),r=o?o.getBoundingClientRect():null,s=window.innerHeight,a=r?l.top-r.top:l.top,d=(o?r.bottom:s)-l.bottom,c=t.clientHeight+n;return"bottom"===i?d>=c:"top"===i?a>=c:a>=c||d>=c};e.isFocused=t=>document.activeElement===t;e.isFormElement=t=>t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement;e.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);e.isJson=t=>{if("string"!=typeof t)return!1;const e=t.trim()[0],i=t.trim().slice(-1);if("{"===e&&"}"===i||"["===e&&"]"===i)try{return JSON.parse(t),!0}catch(t){return!1}return!1};const n=t=>{if(!t)return!1;return"none"===window.getComputedStyle(t).display||n(t.parentElement)};e.isParentOrElementHidden=n;e.isScrollable=t=>{const e=window.getComputedStyle(t),i=e.overflowY,n=e.overflowX,o=("scroll"===i||"auto"===i)&&t.scrollHeight>t.clientHeight,l=("scroll"===n||"auto"===n)&&t.scrollWidth>t.clientWidth;return o||l};e.debounce=(t,e=200)=>{let i;return(...n)=>{clearTimeout(i),i=setTimeout((()=>{t.apply(this,n)}),e)}};e.dispatch=(t,e,i=null)=>{const n=new CustomEvent(t,{detail:{payload:i},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(n)};e.afterTransition=(t,e)=>{const i=()=>{e(),t.removeEventListener("transitionend",i,!0)},n=window.getComputedStyle(t),o=n.getPropertyValue("transition-duration");"none"!==n.getPropertyValue("transition-property")&&parseFloat(o)>0?t.addEventListener("transitionend",i,!0):e()};e.htmlToElement=t=>{const e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild};e.classToClassList=(t,e,i=" ",n="add")=>{t.split(i).forEach((t=>"add"===n?e.classList.add(t):e.classList.remove(t)))};const o={historyIndex:-1,addHistory(t){this.historyIndex=t},existsInHistory(t){return t>this.historyIndex},clearHistory(){this.historyIndex=-1}};e.menuSearchHistory=o},812:function(t,e,i){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});
/*
 * HSLayoutSplitter
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const o=i(292),l=n(i(961));class r extends l.default{constructor(t,e){var i;super(t,e);const n=t.getAttribute("data-hs-layout-splitter"),o=n?JSON.parse(n):{},l=Object.assign(Object.assign({},o),e);this.horizontalSplitterClasses=(null==l?void 0:l.horizontalSplitterClasses)||null,this.horizontalSplitterTemplate=(null==l?void 0:l.horizontalSplitterTemplate)||"<div></div>",this.verticalSplitterClasses=(null==l?void 0:l.verticalSplitterClasses)||null,this.verticalSplitterTemplate=(null==l?void 0:l.verticalSplitterTemplate)||"<div></div>",this.isSplittersAddedManually=null!==(i=null==l?void 0:l.isSplittersAddedManually)&&void 0!==i&&i,this.horizontalSplitters=[],this.horizontalControls=[],this.verticalSplitters=[],this.verticalControls=[],this.isDragging=!1,this.activeSplitter=null,this.onControlPointerDownListener=[],this.init()}controlPointerDown(t){this.isDragging=!0,this.activeSplitter=t,this.onPointerDownHandler(t)}controlPointerUp(){this.isDragging=!1,this.activeSplitter=null,this.onPointerUpHandler()}init(){this.createCollection(window.$hsLayoutSplitterCollection,this),this.buildSplitters(),r.isListenersInitialized||(document.addEventListener("pointermove",r.onDocumentPointerMove),document.addEventListener("pointerup",r.onDocumentPointerUp),r.isListenersInitialized=!0)}buildSplitters(){this.buildHorizontalSplitters(),this.buildVerticalSplitters()}buildHorizontalSplitters(){const t=this.el.querySelectorAll("[data-hs-layout-splitter-horizontal-group]");t.length&&(t.forEach((t=>{this.horizontalSplitters.push({el:t,items:Array.from(t.querySelectorAll(":scope > [data-hs-layout-splitter-item]"))})})),this.updateHorizontalSplitter())}buildVerticalSplitters(){const t=this.el.querySelectorAll("[data-hs-layout-splitter-vertical-group]");t.length&&(t.forEach((t=>{this.verticalSplitters.push({el:t,items:Array.from(t.querySelectorAll(":scope > [data-hs-layout-splitter-item]"))})})),this.updateVerticalSplitter())}buildControl(t,e,i="horizontal"){let n;if(this.isSplittersAddedManually){if(n=null==e?void 0:e.previousElementSibling,!n)return!1;n.style.display=""}else n=(0,o.htmlToElement)("horizontal"===i?this.horizontalSplitterTemplate:this.verticalSplitterTemplate),(0,o.classToClassList)("horizontal"===i?this.horizontalSplitterClasses:this.verticalSplitterClasses,n),n.classList.add("hs-layout-splitter-control");const l={el:n,direction:i,prev:t,next:e};"horizontal"===i?this.horizontalControls.push(l):this.verticalControls.push(l),this.bindListeners(l),e&&!this.isSplittersAddedManually&&t.insertAdjacentElement("afterend",n)}getSplitterItemParsedParam(t){const e=t.getAttribute("data-hs-layout-splitter-item");return(0,o.isJson)(e)?JSON.parse(e):e}getContainerSize(t,e){return e?t.getBoundingClientRect().width:t.getBoundingClientRect().height}getMaxFlexSize(t,e,i){const n=this.getSplitterItemSingleParam(t,e);return"number"==typeof n?n/100*i:0}updateHorizontalSplitter(){this.horizontalSplitters.forEach((({items:t})=>{t.forEach((t=>{this.updateSingleSplitter(t)})),t.forEach(((e,i)=>{i>=t.length-1?this.buildControl(e,null):this.buildControl(e,t[i+1])}))}))}updateSingleSplitter(t){const e=t.getAttribute("data-hs-layout-splitter-item"),i=(0,o.isJson)(e)?JSON.parse(e):e,n=(0,o.isJson)(e)?i.dynamicSize:e;t.style.flex=`${n} 1 0`}updateVerticalSplitter(){this.verticalSplitters.forEach((({items:t})=>{t.forEach((t=>{this.updateSingleSplitter(t)})),t.forEach(((e,i)=>{i>=t.length-1?this.buildControl(e,null,"vertical"):this.buildControl(e,t[i+1],"vertical")}))}))}updateSplitterItemParam(t,e){const i=this.getSplitterItemParsedParam(t),n=e.toFixed(1),o="object"==typeof i?JSON.stringify(Object.assign(Object.assign({},i),{dynamicSize:+n})):n;t.setAttribute("data-hs-layout-splitter-item",o)}onPointerDownHandler(t){const{el:e,prev:i,next:n}=t;e.classList.add("dragging"),i.classList.add("dragging"),n.classList.add("dragging"),document.body.style.userSelect="none"}onPointerUpHandler(){document.body.style.userSelect=""}onPointerMoveHandler(t,e,i){const{prev:n,next:o}=e,l=e.el.closest("horizontal"===i?"[data-hs-layout-splitter-horizontal-group]":"[data-hs-layout-splitter-vertical-group]"),r="horizontal"===i,s=this.getContainerSize(l,r),a=this.calculateAvailableSize(l,n,o,r,s),d=this.calculateResizedSizes(t,n,a,r),c=this.enforceLimits(d,n,o,s,a);this.applySizes(n,o,c,s)}bindListeners(t){const{el:e}=t;this.onControlPointerDownListener.push({el:e,fn:()=>this.controlPointerDown(t)}),e.addEventListener("pointerdown",this.onControlPointerDownListener.find((t=>t.el===e)).fn)}calculateAvailableSize(t,e,i,n,o){const l=t.querySelectorAll(":scope > [data-hs-layout-splitter-item]");return o-Array.from(l).reduce(((t,o)=>{if(o===e||o===i)return t;const l=o.getBoundingClientRect();return t+("fixed"===window.getComputedStyle(o).position?0:n?l.width:l.height)}),0)}calculateResizedSizes(t,e,i,n){const o=n?e.getBoundingClientRect().left:e.getBoundingClientRect().top;let l=Math.max(0,Math.min((n?t.clientX:t.clientY)-o,i));return{previousSize:l,nextSize:i-l}}enforceLimits(t,e,i,n,l){const r=this.getMaxFlexSize(e,"minSize",n),s=this.getMaxFlexSize(i,"minSize",n),a=this.getMaxFlexSize(e,"preLimitSize",n),d=this.getMaxFlexSize(i,"preLimitSize",n);let{previousSize:c,nextSize:u}=t;u<s?(u=s,c=l-u):c<r&&(c=r,u=l-c);const p={prev:e,next:i,previousSize:c.toFixed(),previousFlexSize:c/n*100,previousPreLimitSize:a,previousPreLimitFlexSize:a/n*100,previousMinSize:r,previousMinFlexSize:r/n*100,nextSize:u.toFixed(),nextFlexSize:u/n*100,nextPreLimitSize:d,nextPreLimitFlexSize:d/n*100,nextMinSize:s,nextMinFlexSize:s/n*100,static:{prev:{minSize:this.getSplitterItemSingleParam(e,"minSize"),preLimitSize:this.getSplitterItemSingleParam(e,"preLimitSize")},next:{minSize:this.getSplitterItemSingleParam(i,"minSize"),preLimitSize:this.getSplitterItemSingleParam(i,"preLimitSize")}}};return u<s?(this.fireEvent("onNextLimit",p),(0,o.dispatch)("onNextLimit.hs.layoutSplitter",this.el,p)):c<r&&(this.fireEvent("onPrevLimit",p),(0,o.dispatch)("onPrevLimit.hs.layoutSplitter",this.el,p)),c<=a&&(this.fireEvent("onPrevPreLimit",p),(0,o.dispatch)("onPrevPreLimit.hs.layoutSplitter",this.el,p)),u<=d&&(this.fireEvent("onNextPreLimit",p),(0,o.dispatch)("onNextPreLimit.hs.layoutSplitter",this.el,p)),this.fireEvent("drag",p),(0,o.dispatch)("drag.hs.layoutSplitter",this.el,p),{previousSize:c,nextSize:u}}applySizes(t,e,i,n){const{previousSize:o,nextSize:l}=i,r=o/n*100;this.updateSplitterItemParam(t,r),t.style.flex=`${r.toFixed(1)} 1 0`;const s=l/n*100;this.updateSplitterItemParam(e,s),e.style.flex=`${s.toFixed(1)} 1 0`}getSplitterItemSingleParam(t,e){try{return this.getSplitterItemParsedParam(t)[e]}catch(t){return console.log("There is no parameter with this name in the object."),!1}}getData(t){var e,i;const n=t.closest("[data-hs-layout-splitter-horizontal-group], [data-hs-layout-splitter-vertical-group]");if(!n)throw new Error("Element is not inside a valid layout splitter container.");const o=n.matches("[data-hs-layout-splitter-horizontal-group]"),l=this.getContainerSize(n,o),r=this.getSplitterItemSingleParam(t,"dynamicSize")||0,s=this.getMaxFlexSize(t,"minSize",l),a=this.getMaxFlexSize(t,"preLimitSize",l),d=s/l*100,c=a/l*100;return{el:t,dynamicSize:+(r/100*l).toFixed(),dynamicFlexSize:r,minSize:+s.toFixed(),minFlexSize:d,preLimitSize:+a.toFixed(),preLimitFlexSize:c,static:{minSize:null!==(e=this.getSplitterItemSingleParam(t,"minSize"))&&void 0!==e?e:null,preLimitSize:null!==(i=this.getSplitterItemSingleParam(t,"preLimitSize"))&&void 0!==i?i:null}}}setSplitterItemSize(t,e){this.updateSplitterItemParam(t,e),t.style.flex=`${e.toFixed(1)} 1 0`}updateFlexValues(t){let e=0;const i=window.innerWidth;if(t.forEach((({id:t,breakpoints:n})=>{const o=document.getElementById(t);if(o){const t=(t=>{const e=Object.keys(t).map(Number).sort(((t,e)=>t-e));for(let n=e.length-1;n>=0;n--)if(i>=e[n])return t[e[n]];return 0})(n);this.updateSplitterItemParam(o,t),o.style.flex=`${t.toFixed(1)} 1 0`,e+=t}})),100!==e){const i=100/e;t.forEach((({id:t})=>{const e=document.getElementById(t);if(e){const t=parseFloat(e.style.flex.split(" ")[0])*i;this.updateSplitterItemParam(e,t),e.style.flex=`${t.toFixed(1)} 1 0`}}))}}destroy(){this.onControlPointerDownListener&&(this.onControlPointerDownListener.forEach((({el:t,fn:e})=>{t.removeEventListener("pointerdown",e)})),this.onControlPointerDownListener=null),this.horizontalSplitters.forEach((({items:t})=>{t.forEach((t=>{t.style.flex=""}))})),this.verticalSplitters.forEach((({items:t})=>{t.forEach((t=>{t.style.flex=""}))})),this.horizontalControls.forEach((({el:t})=>{this.isSplittersAddedManually?t.style.display="none":t.remove()})),this.verticalControls.forEach((({el:t})=>{this.isSplittersAddedManually?t.style.display="none":t.remove()})),this.horizontalControls=[],this.verticalControls=[],window.$hsLayoutSplitterCollection=window.$hsLayoutSplitterCollection.filter((({element:t})=>t.el!==this.el)),0===window.$hsLayoutSplitterCollection.length&&r.isListenersInitialized&&(document.removeEventListener("pointermove",r.onDocumentPointerMove),document.removeEventListener("pointerup",r.onDocumentPointerUp),r.isListenersInitialized=!1)}static findInCollection(t){return window.$hsLayoutSplitterCollection.find((e=>t instanceof r?e.element.el===t.el:"string"==typeof t?e.element.el===document.querySelector(t):e.element.el===t))||null}static autoInit(){window.$hsLayoutSplitterCollection||(window.$hsLayoutSplitterCollection=[],window.addEventListener("pointerup",(()=>{if(!window.$hsLayoutSplitterCollection)return!1;const t=document.querySelector(".hs-layout-splitter-control.dragging"),e=document.querySelectorAll("[data-hs-layout-splitter-item].dragging");if(!t)return!1;const i=r.getInstance(t.closest("[data-hs-layout-splitter]"),!0);t.classList.remove("dragging"),e.forEach((t=>t.classList.remove("dragging"))),i.element.isDragging=!1}))),window.$hsLayoutSplitterCollection&&(window.$hsLayoutSplitterCollection=window.$hsLayoutSplitterCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll("[data-hs-layout-splitter]:not(.--prevent-on-load-init)").forEach((t=>{window.$hsLayoutSplitterCollection.find((e=>{var i;return(null===(i=null==e?void 0:e.element)||void 0===i?void 0:i.el)===t}))||new r(t)}))}static getInstance(t,e){const i=window.$hsLayoutSplitterCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)));return i?e?i:i.element.el:null}static on(t,e,i){const n=r.findInCollection(e);n&&(n.element.events[t]=i)}}r.isListenersInitialized=!1,r.onDocumentPointerMove=t=>{const e=document.querySelector(".hs-layout-splitter-control.dragging");if(!e)return;const i=r.getInstance(e.closest("[data-hs-layout-splitter]"),!0);if(!i||!i.element.isDragging)return;const n=i.element.activeSplitter;n&&("vertical"===n.direction?i.element.onPointerMoveHandler(t,n,"vertical"):i.element.onPointerMoveHandler(t,n,"horizontal"))},r.onDocumentPointerUp=()=>{const t=document.querySelector(".hs-layout-splitter-control.dragging");if(!t)return;const e=r.getInstance(t.closest("[data-hs-layout-splitter]"),!0);e&&e.element.controlPointerUp()},window.addEventListener("load",(()=>{r.autoInit()})),"undefined"!=typeof window&&(window.HSLayoutSplitter=r),e.default=r},961:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(t,e,i){this.el=t,this.options=e,this.events=i,this.el=t,this.options=e,this.events={}}createCollection(t,e){var i;t.push({id:(null===(i=null==e?void 0:e.el)||void 0===i?void 0:i.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}}},e={};var i=function i(n){var o=e[n];if(void 0!==o)return o.exports;var l=e[n]={exports:{}};return t[n].call(l.exports,l,l.exports,i),l.exports}(812);return i})()));