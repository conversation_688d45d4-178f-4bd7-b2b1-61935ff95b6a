{"version": 3, "sources": ["../../src/server/load-components.ts"], "names": ["BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE", "join", "requirePage", "interopDefault", "getTracer", "LoadComponentsSpan", "evalManifest", "loadManifest", "wait", "setReferenceManifestsSingleton", "createServerModuleMap", "loadManifestWithRetries", "manifestPath", "attempts", "err", "evalManifestWithRetries", "loadClientReferenceManifest", "entryName", "context", "__RSC_MANIFEST", "undefined", "loadComponentsImpl", "distDir", "page", "isAppPath", "DocumentMod", "AppMod", "Promise", "all", "resolve", "then", "hasClientManifest", "endsWith", "buildManifest", "reactLoadableManifest", "clientReferenceManifest", "serverActionsManifest", "replace", "catch", "serverModuleMap", "pageName", "ComponentMod", "Component", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "loadComponents", "wrap"], "mappings": "AAgBA,SACEA,cAAc,EACdC,uBAAuB,EACvBC,yBAAyB,EACzBC,yBAAyB,EACzBC,0BAA0B,QACrB,0BAAyB;AAChC,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,WAAW,QAAQ,YAAW;AACvC,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,YAAY,EAAEC,YAAY,QAAQ,kBAAiB;AAC5D,SAASC,IAAI,QAAQ,cAAa;AAClC,SAASC,8BAA8B,QAAQ,gCAA+B;AAC9E,SAASC,qBAAqB,QAAQ,4BAA2B;AAyCjE;;CAEC,GACD,OAAO,eAAeC,wBACpBC,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAON,aAAgBK;QACzB,EAAE,OAAOE,KAAK;YACZD;YACA,IAAIA,YAAY,GAAG,MAAMC;YAEzB,MAAMN,KAAK;QACb;IACF;AACF;AAEA;;CAEC,GACD,OAAO,eAAeO,wBACpBH,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOP,aAAgBM;QACzB,EAAE,OAAOE,KAAK;YACZD;YACA,IAAIA,YAAY,GAAG,MAAMC;YAEzB,MAAMN,KAAK;QACb;IACF;AACF;AAEA,eAAeQ,4BACbJ,YAAoB,EACpBK,SAAiB;IAEjB,IAAI;QACF,MAAMC,UAAU,MAAMH,wBAEnBH;QACH,OAAOM,QAAQC,cAAc,CAACF,UAAU;IAC1C,EAAE,OAAOH,KAAK;QACZ,OAAOM;IACT;AACF;AAEA,eAAeC,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EAKV;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACF,WAAW;QACb,CAACC,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzCD,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAM5B,YAAY,cAAcoB,SAAS;YAChEK,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAM5B,YAAY,SAASoB,SAAS;SAC5D;IACH;IAEA,6DAA6D;IAC7D,MAAMS,oBACJP,aAAcD,CAAAA,KAAKS,QAAQ,CAAC,YAAYT,SAASvB,0BAAyB;IAE5E,gCAAgC;IAChC,MAAM,CACJiC,eACAC,uBACAC,yBACAC,sBACD,GAAG,MAAMT,QAAQC,GAAG,CAAC;QACpBjB,wBAAuCV,KAAKqB,SAAS1B;QACrDe,wBACEV,KAAKqB,SAASzB;QAEhBkC,oBACIf,4BACEf,KACEqB,SACA,UACA,OACAC,KAAKc,OAAO,CAAC,QAAQ,OAAO,MAAMvC,4BAA4B,QAEhEyB,KAAKc,OAAO,CAAC,QAAQ,QAEvBjB;QACJI,YACIb,wBACEV,KAAKqB,SAAS,UAAUvB,4BAA4B,UACpDuC,KAAK,CAAC,IAAM,QACd;KACL;IAED,iFAAiF;IACjF,4EAA4E;IAC5E,uCAAuC;IACvC,IAAIF,yBAAyBD,yBAAyB;QACpD1B,+BAA+B;YAC7B0B;YACAC;YACAG,iBAAiB7B,sBAAsB;gBACrC0B;gBACAI,UAAUjB;YACZ;QACF;IACF;IAEA,MAAMkB,eAAe,MAAMd,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAChD5B,YAAYqB,MAAMD,SAASE;IAG7B,MAAMkB,YAAYvC,eAAesC;IACjC,MAAME,WAAWxC,eAAesB;IAChC,MAAMmB,MAAMzC,eAAeuB;IAE3B,MAAM,EAAEmB,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvEP;IAEF,OAAO;QACLG;QACAD;QACAD;QACAT;QACAC;QACAe,YAAYR,aAAaS,MAAM,IAAI,CAAC;QACpCT;QACAI;QACAC;QACAC;QACAZ;QACAC;QACAZ;QACAD;QACAyB;IACF;AACF;AAEA,OAAO,MAAMG,iBAAiB/C,YAAYgD,IAAI,CAC5C/C,mBAAmB8C,cAAc,EACjC9B,oBACD"}