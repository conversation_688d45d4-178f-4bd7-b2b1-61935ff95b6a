var t={189:(t,e,i)=>{i.d(e,{fp:()=>o,lP:()=>s});const s={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"},o=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter","Space","Tab"]},615:(t,e,i)=>{i.d(e,{A:()=>s});class s{constructor(t,e,i){this.el=t,this.options=e,this.events=i,this.el=t,this.options=e,this.events={}}createCollection(t,e){var i;t.push({id:(null===(i=null==e?void 0:e.el)||void 0===i?void 0:i.id)||t.length+1,element:e})}fireEvent(t,e=null){if(this.events.hasOwnProperty(t))return this.events[t](e)}on(t,e){this.events[t]=e}}},926:(t,e,i)=>{i.d(e,{JD:()=>n,PR:()=>s,ar:()=>o,en:()=>d,fc:()=>r,sg:()=>l,yd:()=>a});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
const s=(t,e,i="auto",s=10,o=null)=>{const l=e.getBoundingClientRect(),n=o?o.getBoundingClientRect():null,a=window.innerHeight,r=n?l.top-n.top:l.top,d=(o?n.bottom:a)-l.bottom,h=t.clientHeight+s;return"bottom"===i?d>=h:"top"===i?r>=h:r>=h||d>=h},o=t=>document.activeElement===t,l=(t,e=200)=>{let i;return(...s)=>{clearTimeout(i),i=setTimeout((()=>{t.apply(void 0,s)}),e)}},n=(t,e,i=null)=>{const s=new CustomEvent(t,{detail:{payload:i},bubbles:!0,cancelable:!0,composed:!1});e.dispatchEvent(s)},a=(t,e)=>{const i=()=>{e(),t.removeEventListener("transitionend",i,!0)},s=window.getComputedStyle(t),o=s.getPropertyValue("transition-duration");"none"!==s.getPropertyValue("transition-property")&&parseFloat(o)>0?t.addEventListener("transitionend",i,!0):e()},r=t=>{const e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild},d=(t,e,i=" ",s="add")=>{t.split(i).forEach((t=>"add"===s?e.classList.add(t):e.classList.remove(t)))}}},e={};function i(s){var o=e[s];if(void 0!==o)return o.exports;var l=e[s]={exports:{}};return t[s](l,l.exports,i),l.exports}i.d=(t,e)=>{for(var s in e)i.o(e,s)&&!i.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};i.d(s,{A:()=>d});var o=i(926),l=i(615),n=i(189),a=function(t,e,i,s){return new(i||(i=Promise))((function(o,l){function n(t){try{r(s.next(t))}catch(t){l(t)}}function a(t){try{r(s.throw(t))}catch(t){l(t)}}function r(t){var e;t.done?o(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(n,a)}r((s=s.apply(t,e||[])).next())}))};class r extends l.A{constructor(t,e){var i,s,o,l,n;super(t,e),this.optionId=0;const a=t.getAttribute("data-hs-select"),r=a?JSON.parse(a):{},d=Object.assign(Object.assign({},r),e);this.value=(null==d?void 0:d.value)||this.el.value||null,this.placeholder=(null==d?void 0:d.placeholder)||"Select...",this.hasSearch=(null==d?void 0:d.hasSearch)||!1,this.minSearchLength=null!==(i=null==d?void 0:d.minSearchLength)&&void 0!==i?i:0,this.preventSearchFocus=(null==d?void 0:d.preventSearchFocus)||!1,this.mode=(null==d?void 0:d.mode)||"default",this.viewport=void 0!==(null==d?void 0:d.viewport)?document.querySelector(null==d?void 0:d.viewport):null,this.isOpened=Boolean(null==d?void 0:d.isOpened)||!1,this.isMultiple=this.el.hasAttribute("multiple")||!1,this.isDisabled=this.el.hasAttribute("disabled")||!1,this.selectedItems=[],this.apiUrl=(null==d?void 0:d.apiUrl)||null,this.apiQuery=(null==d?void 0:d.apiQuery)||null,this.apiOptions=(null==d?void 0:d.apiOptions)||null,this.apiSearchQueryKey=(null==d?void 0:d.apiSearchQueryKey)||null,this.apiDataPart=(null==d?void 0:d.apiDataPart)||null,this.apiLoadMore=!0===(null==d?void 0:d.apiLoadMore)?{perPage:10,scrollThreshold:100}:"object"==typeof(null==d?void 0:d.apiLoadMore)&&null!==(null==d?void 0:d.apiLoadMore)&&{perPage:d.apiLoadMore.perPage||10,scrollThreshold:d.apiLoadMore.scrollThreshold||100},this.apiFieldsMap=(null==d?void 0:d.apiFieldsMap)||null,this.apiIconTag=(null==d?void 0:d.apiIconTag)||null,this.apiSelectedValues=(null==d?void 0:d.apiSelectedValues)||null,this.currentPage=0,this.isLoading=!1,this.hasMore=!0,this.wrapperClasses=(null==d?void 0:d.wrapperClasses)||null,this.toggleTag=(null==d?void 0:d.toggleTag)||null,this.toggleClasses=(null==d?void 0:d.toggleClasses)||null,this.toggleCountText=void 0===typeof(null==d?void 0:d.toggleCountText)?null:d.toggleCountText,this.toggleCountTextPlacement=(null==d?void 0:d.toggleCountTextPlacement)||"postfix",this.toggleCountTextMinItems=(null==d?void 0:d.toggleCountTextMinItems)||1,this.toggleCountTextMode=(null==d?void 0:d.toggleCountTextMode)||"countAfterLimit",this.toggleSeparators={items:(null===(s=null==d?void 0:d.toggleSeparators)||void 0===s?void 0:s.items)||", ",betweenItemsAndCounter:(null===(o=null==d?void 0:d.toggleSeparators)||void 0===o?void 0:o.betweenItemsAndCounter)||"and"},this.tagsItemTemplate=(null==d?void 0:d.tagsItemTemplate)||null,this.tagsItemClasses=(null==d?void 0:d.tagsItemClasses)||null,this.tagsInputId=(null==d?void 0:d.tagsInputId)||null,this.tagsInputClasses=(null==d?void 0:d.tagsInputClasses)||null,this.dropdownTag=(null==d?void 0:d.dropdownTag)||null,this.dropdownClasses=(null==d?void 0:d.dropdownClasses)||null,this.dropdownDirectionClasses=(null==d?void 0:d.dropdownDirectionClasses)||null,this.dropdownSpace=(null==d?void 0:d.dropdownSpace)||10,this.dropdownPlacement=(null==d?void 0:d.dropdownPlacement)||null,this.dropdownVerticalFixedPlacement=(null==d?void 0:d.dropdownVerticalFixedPlacement)||null,this.dropdownScope=(null==d?void 0:d.dropdownScope)||"parent",this.dropdownAutoPlacement=(null==d?void 0:d.dropdownAutoPlacement)||!1,this.searchTemplate=(null==d?void 0:d.searchTemplate)||null,this.searchWrapperTemplate=(null==d?void 0:d.searchWrapperTemplate)||null,this.searchWrapperClasses=(null==d?void 0:d.searchWrapperClasses)||"bg-white p-2 sticky top-0",this.searchId=(null==d?void 0:d.searchId)||null,this.searchLimit=(null==d?void 0:d.searchLimit)||1/0,this.isSearchDirectMatch=void 0===(null==d?void 0:d.isSearchDirectMatch)||(null==d?void 0:d.isSearchDirectMatch),this.searchClasses=(null==d?void 0:d.searchClasses)||"block w-[calc(100%-32px)] text-sm border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 py-2 px-3 my-2 mx-4",this.searchPlaceholder=(null==d?void 0:d.searchPlaceholder)||"Search...",this.searchNoResultTemplate=(null==d?void 0:d.searchNoResultTemplate)||"<span></span>",this.searchNoResultText=(null==d?void 0:d.searchNoResultText)||"No results found",this.searchNoResultClasses=(null==d?void 0:d.searchNoResultClasses)||"px-4 text-sm text-gray-800 dark:text-neutral-200",this.optionAllowEmptyOption=void 0!==(null==d?void 0:d.optionAllowEmptyOption)&&(null==d?void 0:d.optionAllowEmptyOption),this.optionTemplate=(null==d?void 0:d.optionTemplate)||null,this.optionTag=(null==d?void 0:d.optionTag)||null,this.optionClasses=(null==d?void 0:d.optionClasses)||null,this.extraMarkup=(null==d?void 0:d.extraMarkup)||null,this.descriptionClasses=(null==d?void 0:d.descriptionClasses)||null,this.iconClasses=(null==d?void 0:d.iconClasses)||null,this.isAddTagOnEnter=null===(l=null==d?void 0:d.isAddTagOnEnter)||void 0===l||l,this.isSelectedOptionOnTop=null===(n=null==d?void 0:d.isSelectedOptionOnTop)||void 0===n||n,this.animationInProcess=!1,this.selectOptions=[],this.remoteOptions=[],this.tagsInputHelper=null,this.init()}wrapperClick(t){t.target.closest("[data-hs-select-dropdown]")||t.target.closest("[data-tag-value]")||this.tagsInput.focus()}toggleClick(){if(this.isDisabled)return!1;this.toggleFn()}tagsInputFocus(){this.isOpened||this.open()}tagsInputInput(){this.calculateInputWidth()}tagsInputInputSecond(t){this.apiUrl||this.searchOptions(t.target.value)}tagsInputKeydown(t){if("Enter"===t.key&&this.isAddTagOnEnter){const e=t.target.value;if(this.selectOptions.find((t=>t.val===e)))return!1;this.addSelectOption(e,e),this.buildOption(e,e),this.buildOriginalOption(e,e),this.dropdown.querySelector(`[data-value="${e}"]`).click(),this.resetTagsInputField()}}searchInput(t){const e=t.target.value;this.apiUrl?this.remoteSearch(e):this.searchOptions(e)}setValue(t){if(this.value=t,this.clearSelections(),Array.isArray(t))if("tags"===this.mode){this.unselectMultipleItems(),this.selectMultipleItems(),this.selectedItems=[];this.wrapper.querySelectorAll("[data-tag-value]").forEach((t=>t.remove())),this.setTagsItems(),this.reassignTagsInputPlaceholder(this.value.length?"":this.placeholder)}else this.toggleTextWrapper.innerHTML=this.value.length?this.stringFromValue():this.placeholder,this.unselectMultipleItems(),this.selectMultipleItems();else this.setToggleTitle(),this.toggle.querySelector("[data-icon]")&&this.setToggleIcon(),this.toggle.querySelector("[data-title]")&&this.setToggleTitle(),this.selectSingleItem()}init(){this.createCollection(window.$hsSelectCollection,this),this.build()}build(){if(this.el.style.display="none",this.el.children&&Array.from(this.el.children).filter((t=>this.optionAllowEmptyOption||!this.optionAllowEmptyOption&&t.value&&""!==t.value)).forEach((t=>{const e=t.getAttribute("data-hs-select-option");this.selectOptions=[...this.selectOptions,{title:t.textContent,val:t.value,disabled:t.disabled,options:"undefined"!==e?JSON.parse(e):null}]})),this.optionAllowEmptyOption&&!this.value&&(this.value=""),this.isMultiple){const t=Array.from(this.el.children).filter((t=>t.selected));if(t){const e=[];t.forEach((t=>{e.push(t.value)})),this.value=e}}this.buildWrapper(),"tags"===this.mode?this.buildTags():this.buildToggle(),this.buildDropdown(),this.extraMarkup&&this.buildExtraMarkup()}buildWrapper(){this.wrapper=document.createElement("div"),this.wrapper.classList.add("hs-select","relative"),"tags"===this.mode&&(this.onWrapperClickListener=t=>this.wrapperClick(t),this.wrapper.addEventListener("click",this.onWrapperClickListener)),this.wrapperClasses&&(0,o.en)(this.wrapperClasses,this.wrapper),this.el.before(this.wrapper),this.wrapper.append(this.el)}buildExtraMarkup(){const t=t=>{const e=(0,o.fc)(t);return this.wrapper.append(e),e},e=t=>{t.classList.contains("--prevent-click")||t.addEventListener("click",(t=>{t.stopPropagation(),this.isDisabled||this.toggleFn()}))};if(Array.isArray(this.extraMarkup))this.extraMarkup.forEach((i=>{const s=t(i);e(s)}));else{const i=t(this.extraMarkup);e(i)}}buildToggle(){var t,e;let i,s;this.toggleTextWrapper=document.createElement("span"),this.toggleTextWrapper.classList.add("truncate"),this.toggle=(0,o.fc)(this.toggleTag||"<div></div>"),i=this.toggle.querySelector("[data-icon]"),s=this.toggle.querySelector("[data-title]"),!this.isMultiple&&i&&this.setToggleIcon(),!this.isMultiple&&s&&this.setToggleTitle(),this.isMultiple?this.toggleTextWrapper.innerHTML=this.value.length?this.stringFromValue():this.placeholder:this.toggleTextWrapper.innerHTML=(null===(t=this.getItemByValue(this.value))||void 0===t?void 0:t.title)||this.placeholder,s||this.toggle.append(this.toggleTextWrapper),this.toggleClasses&&(0,o.en)(this.toggleClasses,this.toggle),this.isDisabled&&this.toggle.classList.add("disabled"),this.wrapper&&this.wrapper.append(this.toggle),(null===(e=this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.isOpened?this.toggle.ariaExpanded="true":this.toggle.ariaExpanded="false"),this.onToggleClickListener=()=>this.toggleClick(),this.toggle.addEventListener("click",this.onToggleClickListener)}setToggleIcon(){var t;const e=this.getItemByValue(this.value),i=this.toggle.querySelector("[data-icon]");if(i){i.innerHTML="";const s=(0,o.fc)(this.apiUrl&&this.apiIconTag?this.apiIconTag||"":(null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.icon)||"");this.value&&this.apiUrl&&this.apiIconTag&&e[this.apiFieldsMap.icon]&&(s.src=e[this.apiFieldsMap.icon]||""),i.append(s),(null==s?void 0:s.src)?i.classList.remove("hidden"):i.classList.add("hidden")}}setToggleTitle(){const t=this.toggle.querySelector("[data-title]");let e=this.placeholder;if(this.optionAllowEmptyOption&&""===this.value){const t=this.selectOptions.find((t=>""===t.val));e=(null==t?void 0:t.title)||this.placeholder}else if(this.value)if(this.apiUrl){const t=this.remoteOptions.find((t=>`${t[this.apiFieldsMap.val]}`===this.value||`${t[this.apiFieldsMap.title]}`===this.value));t&&(e=t[this.apiFieldsMap.title])}else{const t=this.selectOptions.find((t=>t.val===this.value));t&&(e=t.title)}t?(t.innerHTML=e,t.classList.add("truncate"),this.toggle.append(t)):this.toggleTextWrapper.innerHTML=e}buildTags(){this.isDisabled&&this.wrapper.classList.add("disabled"),this.buildTagsInput(),this.setTagsItems()}reassignTagsInputPlaceholder(t){this.tagsInput.placeholder=t,this.tagsInputHelper.innerHTML=t,this.calculateInputWidth()}buildTagsItem(t){var e,i,s,l,n;const a=this.getItemByValue(t);let r,d,h,p;const c=document.createElement("div");if(c.setAttribute("data-tag-value",t),this.tagsItemClasses&&(0,o.en)(this.tagsItemClasses,c),this.tagsItemTemplate&&(r=(0,o.fc)(this.tagsItemTemplate),c.append(r)),(null===(e=null==a?void 0:a.options)||void 0===e?void 0:e.icon)||this.apiIconTag){const t=(0,o.fc)(this.apiUrl&&this.apiIconTag?this.apiIconTag:null===(i=null==a?void 0:a.options)||void 0===i?void 0:i.icon);this.apiUrl&&this.apiIconTag&&a[this.apiFieldsMap.icon]&&(t.src=a[this.apiFieldsMap.icon]||""),p=r?r.querySelector("[data-icon]"):document.createElement("span"),p.append(t),r||c.append(p)}!r||!r.querySelector("[data-icon]")||(null===(s=null==a?void 0:a.options)||void 0===s?void 0:s.icon)||this.apiUrl||this.apiIconTag||a[null===(l=this.apiFieldsMap)||void 0===l?void 0:l.icon]||r.querySelector("[data-icon]").classList.add("hidden"),d=r?r.querySelector("[data-title]"):document.createElement("span"),this.apiUrl&&(null===(n=this.apiFieldsMap)||void 0===n?void 0:n.title)&&a[this.apiFieldsMap.title]?d.textContent=a[this.apiFieldsMap.title]:d.textContent=a.title||"",r||c.append(d),r?h=r.querySelector("[data-remove]"):(h=document.createElement("span"),h.textContent="X",c.append(h)),h.addEventListener("click",(()=>{this.value=this.value.filter((e=>e!==t)),this.selectedItems=this.selectedItems.filter((e=>e!==t)),this.value.length||this.reassignTagsInputPlaceholder(this.placeholder),this.unselectMultipleItems(),this.selectMultipleItems(),c.remove(),this.triggerChangeEventForNativeSelect()})),this.wrapper.append(c)}getItemByValue(t){return this.apiUrl?this.remoteOptions.find((e=>`${e[this.apiFieldsMap.val]}`===t||e[this.apiFieldsMap.title]===t)):this.selectOptions.find((e=>e.val===t))}setTagsItems(){this.value&&this.value.forEach((t=>{this.selectedItems.includes(t)||this.buildTagsItem(t),this.selectedItems=this.selectedItems.includes(t)?this.selectedItems:[...this.selectedItems,t]})),this.isOpened&&this.floatingUIInstance&&this.floatingUIInstance.update()}buildTagsInput(){this.tagsInput=document.createElement("input"),this.tagsInputId&&(this.tagsInput.id=this.tagsInputId),this.tagsInputClasses&&(0,o.en)(this.tagsInputClasses,this.tagsInput),this.onTagsInputFocusListener=()=>this.tagsInputFocus(),this.onTagsInputInputListener=()=>this.tagsInputInput(),this.onTagsInputInputSecondListener=(0,o.sg)((t=>this.tagsInputInputSecond(t))),this.onTagsInputKeydownListener=t=>this.tagsInputKeydown(t),this.tagsInput.addEventListener("focus",this.onTagsInputFocusListener),this.tagsInput.addEventListener("input",this.onTagsInputInputListener),this.tagsInput.addEventListener("input",this.onTagsInputInputSecondListener),this.tagsInput.addEventListener("keydown",this.onTagsInputKeydownListener),this.wrapper.append(this.tagsInput),setTimeout((()=>{this.adjustInputWidth(),this.reassignTagsInputPlaceholder(this.value.length?"":this.placeholder)}))}buildDropdown(){this.dropdown=(0,o.fc)(this.dropdownTag||"<div></div>"),this.dropdown.setAttribute("data-hs-select-dropdown",""),"parent"===this.dropdownScope&&(this.dropdown.classList.add("absolute"),this.dropdownVerticalFixedPlacement||this.dropdown.classList.add("top-full")),this.dropdown.role="listbox",this.dropdown.tabIndex=-1,this.dropdown.ariaOrientation="vertical",this.isOpened||this.dropdown.classList.add("hidden"),this.dropdownClasses&&(0,o.en)(this.dropdownClasses,this.dropdown),this.wrapper&&this.wrapper.append(this.dropdown),this.dropdown&&this.hasSearch&&this.buildSearch(),this.selectOptions&&this.selectOptions.forEach(((t,e)=>this.buildOption(t.title,t.val,t.disabled,t.selected,t.options,`${e}`))),this.apiUrl&&this.optionsFromRemoteData(),"window"===this.dropdownScope&&this.buildFloatingUI(),this.dropdown&&this.apiLoadMore&&this.setupInfiniteScroll()}setupInfiniteScroll(){this.dropdown.addEventListener("scroll",this.handleScroll.bind(this))}handleScroll(){return a(this,void 0,void 0,(function*(){if(!this.dropdown||this.isLoading||!this.hasMore||!this.apiLoadMore)return;const{scrollTop:t,scrollHeight:e,clientHeight:i}=this.dropdown;e-t-i<("object"==typeof this.apiLoadMore?this.apiLoadMore.scrollThreshold:100)&&(yield this.loadMore())}))}loadMore(){return a(this,void 0,void 0,(function*(){var t,e,i,s;if(this.apiUrl&&!this.isLoading&&this.hasMore&&this.apiLoadMore){this.isLoading=!0;try{const o=new URL(this.apiUrl),l=(null===(t=this.apiFieldsMap)||void 0===t?void 0:t.page)||(null===(e=this.apiFieldsMap)||void 0===e?void 0:e.offset)||"page",n=!!(null===(i=this.apiFieldsMap)||void 0===i?void 0:i.offset),a="object"==typeof this.apiLoadMore?this.apiLoadMore.perPage:10;if(n){const t=this.currentPage*a;o.searchParams.set(l,t.toString()),this.currentPage++}else this.currentPage++,o.searchParams.set(l,this.currentPage.toString());o.searchParams.set((null===(s=this.apiFieldsMap)||void 0===s?void 0:s.limit)||"limit",a.toString());const r=yield fetch(o.toString(),this.apiOptions||{}),d=yield r.json(),h=this.apiDataPart?d[this.apiDataPart]:d.results,p=d.count||0,c=this.currentPage*a;h&&h.length>0?(this.remoteOptions=[...this.remoteOptions||[],...h],this.buildOptionsFromRemoteData(h),this.hasMore=c<p):this.hasMore=!1}catch(t){this.hasMore=!1,console.error("Error loading more options:",t)}finally{this.isLoading=!1}}}))}buildFloatingUI(){if("undefined"!=typeof FloatingUIDOM&&FloatingUIDOM.computePosition){document.body.appendChild(this.dropdown);const t="tags"===this.mode?this.wrapper:this.toggle,e=[FloatingUIDOM.offset([0,5])];this.dropdownAutoPlacement&&"function"==typeof FloatingUIDOM.flip&&e.push(FloatingUIDOM.flip({fallbackPlacements:["bottom-start","bottom-end","top-start","top-end"]}));const i={placement:n.lP[this.dropdownPlacement]||"bottom",strategy:"fixed",middleware:e},s=()=>{FloatingUIDOM.computePosition(t,this.dropdown,i).then((({x:t,y:e,placement:i})=>{Object.assign(this.dropdown.style,{position:"fixed",left:`${t}px`,top:`${e}px`,["margin"+("bottom"===i?"Top":"top"===i?"Bottom":"right"===i?"Left":"Right")]:`${this.dropdownSpace}px`}),this.dropdown.setAttribute("data-placement",i)}))};s();const o=FloatingUIDOM.autoUpdate(t,this.dropdown,s);this.floatingUIInstance={update:s,destroy:o}}else console.error("FloatingUIDOM not found! Please enable it on the page.")}updateDropdownWidth(){const t="tags"===this.mode?this.wrapper:this.toggle;this.dropdown.style.width=`${t.clientWidth}px`}buildSearch(){let t;this.searchWrapper=(0,o.fc)(this.searchWrapperTemplate||"<div></div>"),this.searchWrapperClasses&&(0,o.en)(this.searchWrapperClasses,this.searchWrapper),t=this.searchWrapper.querySelector("[data-input]");const e=(0,o.fc)(this.searchTemplate||'<input type="text">');this.search="INPUT"===e.tagName?e:e.querySelector(":scope input"),this.search.placeholder=this.searchPlaceholder,this.searchClasses&&(0,o.en)(this.searchClasses,this.search),this.searchId&&(this.search.id=this.searchId),this.onSearchInputListener=(0,o.sg)((t=>this.searchInput(t))),this.search.addEventListener("input",this.onSearchInputListener),t?t.append(e):this.searchWrapper.append(e),this.dropdown.append(this.searchWrapper)}buildOption(t,e,i=!1,s=!1,l,n="1",a){var r;let d=null,h=null,p=null,c=null;const u=(0,o.fc)(this.optionTag||"<div></div>");if(u.setAttribute("data-value",e),u.setAttribute("data-title-value",t),u.setAttribute("tabIndex",n),u.classList.add("cursor-pointer"),u.setAttribute("data-id",a||`${this.optionId}`),a||this.optionId++,i&&u.classList.add("disabled"),s&&(this.isMultiple?this.value=[...this.value,e]:this.value=e),this.optionTemplate&&(d=(0,o.fc)(this.optionTemplate),u.append(d)),d?(h=d.querySelector("[data-title]"),h.textContent=t||""):u.textContent=t||"",l){if(l.icon){const e=(0,o.fc)(null!==(r=this.apiIconTag)&&void 0!==r?r:l.icon);if(e.classList.add("max-w-full"),this.apiUrl&&(e.setAttribute("alt",t),e.setAttribute("src",l.icon)),d)p=d.querySelector("[data-icon]"),p.append(e);else{const t=(0,o.fc)("<div></div>");this.iconClasses&&(0,o.en)(this.iconClasses,t),t.append(e),u.append(t)}}if(l.description)if(d)c=d.querySelector("[data-description]"),c&&c.append(l.description);else{const t=(0,o.fc)("<div></div>");t.textContent=l.description,this.descriptionClasses&&(0,o.en)(this.descriptionClasses,t),u.append(t)}}d&&d.querySelector("[data-icon]")&&!l&&!(null==l?void 0:l.icon)&&d.querySelector("[data-icon]").classList.add("hidden"),this.value&&(this.isMultiple?this.value.includes(e):this.value===e)&&u.classList.add("selected"),i||u.addEventListener("click",(()=>this.onSelectOption(e))),this.optionClasses&&(0,o.en)(this.optionClasses,u),this.dropdown&&this.dropdown.append(u),s&&this.setNewValue()}buildOptionFromRemoteData(t,e,i=!1,s=!1,o="1",l,n){o?this.buildOption(t,e,i,s,n,o,l):alert("ID parameter is required for generating remote options! Please check your API endpoint have it.")}buildOptionsFromRemoteData(t){t.forEach(((t,e)=>{let i=null,s="",o="";const l={id:"",val:"",title:"",icon:null,description:null,rest:{}};Object.keys(t).forEach((e=>{var n;t[this.apiFieldsMap.id]&&(i=t[this.apiFieldsMap.id]),t[this.apiFieldsMap.val]&&(o=`${t[this.apiFieldsMap.val]}`),t[this.apiFieldsMap.title]&&(s=t[this.apiFieldsMap.title],t[this.apiFieldsMap.val]||(o=s)),t[this.apiFieldsMap.icon]&&(l.icon=t[this.apiFieldsMap.icon]),t[null===(n=this.apiFieldsMap)||void 0===n?void 0:n.description]&&(l.description=t[this.apiFieldsMap.description]),l.rest[e]=t[e]}));if(!this.dropdown.querySelector(`[data-value="${o}"]`)){const t=!!this.apiSelectedValues&&(Array.isArray(this.apiSelectedValues)?this.apiSelectedValues.includes(o):this.apiSelectedValues===o);this.buildOriginalOption(s,o,i,!1,t,l),this.buildOptionFromRemoteData(s,o,!1,t,`${e}`,i,l),t&&(this.isMultiple?(this.value||(this.value=[]),Array.isArray(this.value)&&(this.value=[...this.value,o])):this.value=o)}})),this.sortElements(this.el,"option"),this.sortElements(this.dropdown,"[data-value]")}optionsFromRemoteData(){return a(this,arguments,void 0,(function*(t=""){const e=yield this.apiRequest(t);this.remoteOptions=e,e.length?this.buildOptionsFromRemoteData(this.remoteOptions):console.log("There is no data were responded!")}))}apiRequest(){return a(this,arguments,void 0,(function*(t=""){var e,i,s,o;try{let l=this.apiUrl;const n=this.apiSearchQueryKey?`${this.apiSearchQueryKey}=${t.toLowerCase()}`:null,a=this.apiQuery||"",r=this.apiOptions||{},d=new URLSearchParams(a),h=d.toString();if(this.apiLoadMore){const t=(null===(e=this.apiFieldsMap)||void 0===e?void 0:e.page)||(null===(i=this.apiFieldsMap)||void 0===i?void 0:i.offset)||"page",n=!!(null===(s=this.apiFieldsMap)||void 0===s?void 0:s.offset),a=(null===(o=this.apiFieldsMap)||void 0===o?void 0:o.limit)||"limit",r="object"==typeof this.apiLoadMore?this.apiLoadMore.perPage:10;d.delete(t),d.delete(a),l+=n?`?${t}=0`:`?${t}=1`,l+=`&${a}=${r}`}else(n||h)&&(l+=`?${n||h}`);n&&h?l+=`&${h}`:!n||h||this.apiLoadMore||(l+=`?${n}`);const p=yield fetch(l,r),c=yield p.json();return this.apiDataPart?c[this.apiDataPart]:c}catch(t){console.error(t)}}))}sortElements(t,e){const i=Array.from(t.querySelectorAll(e));this.isSelectedOptionOnTop&&i.sort(((t,e)=>{const i=t.classList.contains("selected")||t.hasAttribute("selected"),s=e.classList.contains("selected")||e.hasAttribute("selected");return i&&!s?-1:!i&&s?1:0})),i.forEach((e=>t.appendChild(e)))}remoteSearch(t){return a(this,void 0,void 0,(function*(){if(t.length<=this.minSearchLength){const t=yield this.apiRequest("");return this.remoteOptions=t,Array.from(this.dropdown.querySelectorAll("[data-value]")).forEach((t=>t.remove())),Array.from(this.el.querySelectorAll("option[value]")).forEach((t=>{t.remove()})),t.length?this.buildOptionsFromRemoteData(t):console.log("No data responded!"),!1}const e=yield this.apiRequest(t);this.remoteOptions=e;let i=e.map((t=>`${t.id}`)),s=null;const o=this.dropdown.querySelectorAll("[data-value]");this.el.querySelectorAll("[data-hs-select-option]").forEach((t=>{var e;const s=t.getAttribute("data-id");i.includes(s)||(null===(e=this.value)||void 0===e?void 0:e.includes(t.value))||this.destroyOriginalOption(t.value)})),o.forEach((t=>{var e;const s=t.getAttribute("data-id");i.includes(s)||(null===(e=this.value)||void 0===e?void 0:e.includes(t.getAttribute("data-value")))?i=i.filter((t=>t!==s)):this.destroyOption(t.getAttribute("data-value"))})),s=e.filter((t=>i.includes(`${t.id}`))),s.length?this.buildOptionsFromRemoteData(s):console.log("No data responded!")}))}destroyOption(t){const e=this.dropdown.querySelector(`[data-value="${t}"]`);if(!e)return!1;e.remove()}buildOriginalOption(t,e,i,s,l,n){const a=(0,o.fc)("<option></option>");a.setAttribute("value",e),s&&a.setAttribute("disabled","disabled"),l&&a.setAttribute("selected","selected"),i&&a.setAttribute("data-id",i),a.setAttribute("data-hs-select-option",JSON.stringify(n)),a.innerText=t,this.el.append(a)}destroyOriginalOption(t){const e=this.el.querySelector(`[value="${t}"]`);if(!e)return!1;e.remove()}buildTagsInputHelper(){this.tagsInputHelper=document.createElement("span"),this.tagsInputHelper.style.fontSize=window.getComputedStyle(this.tagsInput).fontSize,this.tagsInputHelper.style.fontFamily=window.getComputedStyle(this.tagsInput).fontFamily,this.tagsInputHelper.style.fontWeight=window.getComputedStyle(this.tagsInput).fontWeight,this.tagsInputHelper.style.letterSpacing=window.getComputedStyle(this.tagsInput).letterSpacing,this.tagsInputHelper.style.visibility="hidden",this.tagsInputHelper.style.whiteSpace="pre",this.tagsInputHelper.style.position="absolute",this.wrapper.appendChild(this.tagsInputHelper)}calculateInputWidth(){this.tagsInputHelper.textContent=this.tagsInput.value||this.tagsInput.placeholder;const t=parseInt(window.getComputedStyle(this.tagsInput).paddingLeft)+parseInt(window.getComputedStyle(this.tagsInput).paddingRight),e=parseInt(window.getComputedStyle(this.tagsInput).borderLeftWidth)+parseInt(window.getComputedStyle(this.tagsInput).borderRightWidth),i=this.tagsInputHelper.offsetWidth+t+e,s=this.wrapper.offsetWidth-(parseInt(window.getComputedStyle(this.wrapper).paddingLeft)+parseInt(window.getComputedStyle(this.wrapper).paddingRight));this.tagsInput.style.width=`${Math.min(i,s)+2}px`}adjustInputWidth(){this.buildTagsInputHelper(),this.calculateInputWidth()}onSelectOption(t){if(this.clearSelections(),this.isMultiple?(this.value=this.value.includes(t)?Array.from(this.value).filter((e=>e!==t)):[...Array.from(this.value),t],this.selectMultipleItems(),this.setNewValue()):(this.value=t,this.selectSingleItem(),this.setNewValue()),this.fireEvent("change",this.value),"tags"===this.mode){const t=this.selectedItems.filter((t=>!this.value.includes(t)));t.length&&t.forEach((t=>{this.selectedItems=this.selectedItems.filter((e=>e!==t)),this.wrapper.querySelector(`[data-tag-value="${t}"]`).remove()})),this.resetTagsInputField()}this.isMultiple||(this.toggle.querySelector("[data-icon]")&&this.setToggleIcon(),this.toggle.querySelector("[data-title]")&&this.setToggleTitle(),this.close(!0)),this.value.length||"tags"!==this.mode||this.reassignTagsInputPlaceholder(this.placeholder),this.isOpened&&"tags"===this.mode&&this.tagsInput&&this.tagsInput.focus(),this.triggerChangeEventForNativeSelect()}triggerChangeEventForNativeSelect(){const t=new Event("change",{bubbles:!0});this.el.dispatchEvent(t),(0,o.JD)("change.hs.select",this.el,this.value)}addSelectOption(t,e,i,s,o){this.selectOptions=[...this.selectOptions,{title:t,val:e,disabled:i,selected:s,options:o}]}removeSelectOption(t,e=!1){if(!!!this.selectOptions.some((e=>e.val===t)))return!1;this.selectOptions=this.selectOptions.filter((e=>e.val!==t)),this.value=e?this.value.filter((e=>e!==t)):t}resetTagsInputField(){this.tagsInput.value="",this.reassignTagsInputPlaceholder(""),this.searchOptions("")}clearSelections(){Array.from(this.dropdown.children).forEach((t=>{t.classList.contains("selected")&&t.classList.remove("selected")})),Array.from(this.el.children).forEach((t=>{t.selected&&(t.selected=!1)}))}setNewValue(){if("tags"===this.mode)this.setTagsItems();else if(this.optionAllowEmptyOption&&""===this.value){const t=this.selectOptions.find((t=>""===t.val));this.toggleTextWrapper.innerHTML=(null==t?void 0:t.title)||this.placeholder}else if(this.value)if(this.apiUrl){const t=this.dropdown.querySelector(`[data-value="${this.value}"]`);if(t)this.toggleTextWrapper.innerHTML=t.getAttribute("data-title-value")||this.placeholder;else{const t=this.remoteOptions.find((t=>(t[this.apiFieldsMap.val]?`${t[this.apiFieldsMap.val]}`:t[this.apiFieldsMap.title])===this.value));this.toggleTextWrapper.innerHTML=t?`${t[this.apiFieldsMap.title]}`:this.stringFromValue()}}else this.toggleTextWrapper.innerHTML=this.stringFromValue();else this.toggleTextWrapper.innerHTML=this.placeholder}stringFromValueBasic(t){var e;const i=[];let s="";if(t.forEach((t=>{this.isMultiple?this.value.includes(t.val)&&i.push(t.title):this.value===t.val&&i.push(t.title)})),void 0!==this.toggleCountText&&null!==this.toggleCountText&&i.length>=this.toggleCountTextMinItems)if("nItemsAndCount"===this.toggleCountTextMode){const t=i.slice(0,this.toggleCountTextMinItems-1),o=[t.join(this.toggleSeparators.items)],l=""+(i.length-t.length);if((null===(e=null==this?void 0:this.toggleSeparators)||void 0===e?void 0:e.betweenItemsAndCounter)&&o.push(this.toggleSeparators.betweenItemsAndCounter),this.toggleCountText)switch(this.toggleCountTextPlacement){case"postfix-no-space":o.push(`${l}${this.toggleCountText}`);break;case"prefix-no-space":o.push(`${this.toggleCountText}${l}`);break;case"prefix":o.push(`${this.toggleCountText} ${l}`);break;default:o.push(`${l} ${this.toggleCountText}`)}s=o.join(" ")}else s=`${i.length} ${this.toggleCountText}`;else s=i.join(this.toggleSeparators.items);return s}stringFromValueRemoteData(){const t=this.dropdown.querySelectorAll("[data-title-value]"),e=[];let i="";if(t.forEach((t=>{const i=t.getAttribute("data-value"),s=t.getAttribute("data-title-value");this.isMultiple?this.value.includes(i)&&e.push(s):this.value===i&&e.push(s)})),this.toggleCountText&&""!==this.toggleCountText&&e.length>=this.toggleCountTextMinItems)if("nItemsAndCount"===this.toggleCountTextMode){const t=e.slice(0,this.toggleCountTextMinItems-1);i=`${t.join(this.toggleSeparators.items)} ${this.toggleSeparators.betweenItemsAndCounter} ${e.length-t.length} ${this.toggleCountText}`}else i=`${e.length} ${this.toggleCountText}`;else i=e.join(this.toggleSeparators.items);return i}stringFromValue(){return this.apiUrl?this.stringFromValueRemoteData():this.stringFromValueBasic(this.selectOptions)}selectSingleItem(){Array.from(this.el.children).find((t=>this.value===t.value)).selected=!0;const t=Array.from(this.dropdown.children).find((t=>this.value===t.getAttribute("data-value")));t&&t.classList.add("selected")}selectMultipleItems(){Array.from(this.dropdown.children).filter((t=>this.value.includes(t.getAttribute("data-value")))).forEach((t=>t.classList.add("selected"))),Array.from(this.el.children).filter((t=>this.value.includes(t.value))).forEach((t=>t.selected=!0))}unselectMultipleItems(){Array.from(this.dropdown.children).forEach((t=>t.classList.remove("selected"))),Array.from(this.el.children).forEach((t=>t.selected=!1))}searchOptions(t){if(t.length<=this.minSearchLength){this.searchNoResult&&(this.searchNoResult.remove(),this.searchNoResult=null);return this.dropdown.querySelectorAll("[data-value]").forEach((t=>{t.classList.remove("hidden")})),!1}this.searchNoResult&&(this.searchNoResult.remove(),this.searchNoResult=null),this.searchNoResult=(0,o.fc)(this.searchNoResultTemplate),this.searchNoResult.innerText=this.searchNoResultText,(0,o.en)(this.searchNoResultClasses,this.searchNoResult);const e=this.dropdown.querySelectorAll("[data-value]");let i,s=!1;this.searchLimit&&(i=0),e.forEach((e=>{const o=e.getAttribute("data-title-value").toLocaleLowerCase();let l;if(this.isSearchDirectMatch)l=!o.includes(t.toLowerCase())||this.searchLimit&&i>=this.searchLimit;else{const e=t?t.split("").map((t=>/\w/.test(t)?`${t}[\\W_]*`:"\\W*")).join(""):"";l=!new RegExp(e,"i").test(o.trim())||this.searchLimit&&i>=this.searchLimit}l?e.classList.add("hidden"):(e.classList.remove("hidden"),s=!0,this.searchLimit&&i++)})),s||this.dropdown.append(this.searchNoResult)}eraseToggleIcon(){const t=this.toggle.querySelector("[data-icon]");t&&(t.innerHTML=null,t.classList.add("hidden"))}eraseToggleTitle(){const t=this.toggle.querySelector("[data-title]");t?t.innerHTML=this.placeholder:this.toggleTextWrapper.innerHTML=this.placeholder}toggleFn(){this.isOpened?this.close():this.open()}destroy(){this.wrapper&&this.wrapper.removeEventListener("click",this.onWrapperClickListener),this.toggle&&this.toggle.removeEventListener("click",this.onToggleClickListener),this.tagsInput&&(this.tagsInput.removeEventListener("focus",this.onTagsInputFocusListener),this.tagsInput.removeEventListener("input",this.onTagsInputInputListener),this.tagsInput.removeEventListener("input",this.onTagsInputInputSecondListener),this.tagsInput.removeEventListener("keydown",this.onTagsInputKeydownListener)),this.search&&this.search.removeEventListener("input",this.onSearchInputListener);const t=this.el.parentElement.parentElement;this.el.classList.add("hidden"),this.el.style.display="",t.prepend(this.el),t.querySelector(".hs-select").remove(),this.wrapper=null,window.$hsSelectCollection=window.$hsSelectCollection.filter((({element:t})=>t.el!==this.el))}open(){var t;const e=(null===(t=null===window||void 0===window?void 0:window.$hsSelectCollection)||void 0===t?void 0:t.find((t=>t.element.isOpened)))||null;if(e&&e.element.close(),this.animationInProcess)return!1;this.animationInProcess=!0,"window"===this.dropdownScope&&this.dropdown.classList.add("invisible"),this.dropdown.classList.remove("hidden"),"window"!==this.dropdownScope&&this.recalculateDirection(),setTimeout((()=>{var t;(null===(t=null==this?void 0:this.toggle)||void 0===t?void 0:t.ariaExpanded)&&(this.toggle.ariaExpanded="true"),this.wrapper.classList.add("active"),this.dropdown.classList.add("opened"),this.dropdown.classList.contains("w-full")&&"window"===this.dropdownScope&&this.updateDropdownWidth(),this.floatingUIInstance&&"window"===this.dropdownScope&&(this.floatingUIInstance.update(),this.dropdown.classList.remove("invisible")),this.hasSearch&&!this.preventSearchFocus&&this.search.focus(),this.animationInProcess=!1})),this.isOpened=!0}close(t=!1){var e,i,s,l;if(this.animationInProcess)return!1;this.animationInProcess=!0,(null===(e=null==this?void 0:this.toggle)||void 0===e?void 0:e.ariaExpanded)&&(this.toggle.ariaExpanded="false"),this.wrapper.classList.remove("active"),this.dropdown.classList.remove("opened","bottom-full","top-full"),(null===(i=this.dropdownDirectionClasses)||void 0===i?void 0:i.bottom)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.bottom),(null===(s=this.dropdownDirectionClasses)||void 0===s?void 0:s.top)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop="",this.dropdown.style.marginBottom="",(0,o.yd)(this.dropdown,(()=>{this.dropdown.classList.add("hidden"),this.hasSearch&&(this.search.value="",this.search.dispatchEvent(new Event("input",{bubbles:!0})),this.search.blur()),t&&this.toggle.focus(),this.animationInProcess=!1})),null===(l=this.dropdown.querySelector(".hs-select-option-highlighted"))||void 0===l||l.classList.remove("hs-select-option-highlighted"),this.isOpened=!1}addOption(t){let e=`${this.selectOptions.length}`;const i=t=>{const{title:i,val:s,disabled:o,selected:l,options:n}=t;!!this.selectOptions.some((t=>t.val===s))||(this.addSelectOption(i,s,o,l,n),this.buildOption(i,s,o,l,n,e),this.buildOriginalOption(i,s,null,o,l,n),l&&!this.isMultiple&&this.onSelectOption(s))};Array.isArray(t)?t.forEach((t=>{i(t)})):i(t)}removeOption(t){const e=(t,e=!1)=>{!!this.selectOptions.some((e=>e.val===t))&&(this.removeSelectOption(t,e),this.destroyOption(t),this.destroyOriginalOption(t),this.value===t&&(this.value=null,this.eraseToggleTitle(),this.eraseToggleIcon()))};Array.isArray(t)?t.forEach((t=>{e(t,this.isMultiple)})):e(t,this.isMultiple),this.setNewValue()}recalculateDirection(){var t,e,i,s;if((null==this?void 0:this.dropdownVerticalFixedPlacement)&&(this.dropdown.classList.contains("bottom-full")||this.dropdown.classList.contains("top-full")))return!1;"top"===(null==this?void 0:this.dropdownVerticalFixedPlacement)?(this.dropdown.classList.add("bottom-full"),this.dropdown.style.marginBottom=`${this.dropdownSpace}px`):"bottom"===(null==this?void 0:this.dropdownVerticalFixedPlacement)?(this.dropdown.classList.add("top-full"),this.dropdown.style.marginTop=`${this.dropdownSpace}px`):(0,o.PR)(this.dropdown,this.toggle||this.tagsInput,"bottom",this.dropdownSpace,this.viewport)?(this.dropdown.classList.remove("bottom-full"),(null===(t=this.dropdownDirectionClasses)||void 0===t?void 0:t.bottom)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.bottom),this.dropdown.style.marginBottom="",this.dropdown.classList.add("top-full"),(null===(e=this.dropdownDirectionClasses)||void 0===e?void 0:e.top)&&this.dropdown.classList.add(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop=`${this.dropdownSpace}px`):(this.dropdown.classList.remove("top-full"),(null===(i=this.dropdownDirectionClasses)||void 0===i?void 0:i.top)&&this.dropdown.classList.remove(this.dropdownDirectionClasses.top),this.dropdown.style.marginTop="",this.dropdown.classList.add("bottom-full"),(null===(s=this.dropdownDirectionClasses)||void 0===s?void 0:s.bottom)&&this.dropdown.classList.add(this.dropdownDirectionClasses.bottom),this.dropdown.style.marginBottom=`${this.dropdownSpace}px`)}static findInCollection(t){return window.$hsSelectCollection.find((e=>t instanceof r?e.element.el===t.el:"string"==typeof t?e.element.el===document.querySelector(t):e.element.el===t))||null}static getInstance(t,e){const i=window.$hsSelectCollection.find((e=>e.element.el===("string"==typeof t?document.querySelector(t):t)));return i?e?i:i.element:null}static autoInit(){window.$hsSelectCollection||(window.$hsSelectCollection=[],window.addEventListener("click",(t=>{const e=t.target;r.closeCurrentlyOpened(e)})),document.addEventListener("keydown",(t=>r.accessibility(t)))),window.$hsSelectCollection&&(window.$hsSelectCollection=window.$hsSelectCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll("[data-hs-select]:not(.--prevent-on-load-init)").forEach((t=>{if(!window.$hsSelectCollection.find((e=>{var i;return(null===(i=null==e?void 0:e.element)||void 0===i?void 0:i.el)===t}))){const e=t.getAttribute("data-hs-select"),i=e?JSON.parse(e):{};new r(t,i)}}))}static open(t){const e=r.findInCollection(t);e&&!e.element.isOpened&&e.element.open()}static close(t){const e=r.findInCollection(t);e&&e.element.isOpened&&e.element.close()}static closeCurrentlyOpened(t=null){if(!t.closest(".hs-select.active")&&!t.closest("[data-hs-select-dropdown].opened")){const t=window.$hsSelectCollection.filter((t=>t.element.isOpened))||null;t&&t.forEach((t=>{t.element.close()}))}}static accessibility(t){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e&&n.fp.includes(t.code)&&!t.metaKey)switch(t.code){case"Escape":t.preventDefault(),this.onEscape();break;case"ArrowUp":t.preventDefault(),t.stopImmediatePropagation(),this.onArrow();break;case"ArrowDown":t.preventDefault(),t.stopImmediatePropagation(),this.onArrow(!1);break;case"Tab":t.preventDefault(),t.stopImmediatePropagation(),this.onTab(t.shiftKey);break;case"Home":t.preventDefault(),t.stopImmediatePropagation(),this.onStartEnd();break;case"End":t.preventDefault(),t.stopImmediatePropagation(),this.onStartEnd(!1);break;case"Enter":t.preventDefault(),this.onEnter(t);break;case"Space":if((0,o.ar)(e.element.search))break;t.preventDefault(),this.onEnter(t)}}static onEscape(){const t=window.$hsSelectCollection.find((t=>t.element.isOpened));t&&t.element.close()}static onArrow(t=!0){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e){const i=e.element.dropdown;if(!i)return!1;const s=(t?Array.from(i.querySelectorAll(":scope > *:not(.hidden)")).reverse():Array.from(i.querySelectorAll(":scope > *:not(.hidden)"))).filter((t=>!t.classList.contains("disabled"))),o=i.querySelector(".hs-select-option-highlighted")||i.querySelector(".selected");o||s[0].classList.add("hs-select-option-highlighted");let l=s.findIndex((t=>t===o));l+1<s.length&&l++,s[l].focus(),o&&o.classList.remove("hs-select-option-highlighted"),s[l].classList.add("hs-select-option-highlighted")}}static onTab(t=!0){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e){const i=e.element.dropdown;if(!i)return!1;const s=(t?Array.from(i.querySelectorAll(":scope >  *:not(.hidden)")).reverse():Array.from(i.querySelectorAll(":scope >  *:not(.hidden)"))).filter((t=>!t.classList.contains("disabled"))),o=i.querySelector(".hs-select-option-highlighted")||i.querySelector(".selected");o||s[0].classList.add("hs-select-option-highlighted");let l=s.findIndex((t=>t===o));if(!(l+1<s.length))return o&&o.classList.remove("hs-select-option-highlighted"),e.element.close(),e.element.toggle.focus(),!1;l++,s[l].focus(),o&&o.classList.remove("hs-select-option-highlighted"),s[l].classList.add("hs-select-option-highlighted")}}static onStartEnd(t=!0){const e=window.$hsSelectCollection.find((t=>t.element.isOpened));if(e){const i=e.element.dropdown;if(!i)return!1;const s=(t?Array.from(i.querySelectorAll(":scope >  *:not(.hidden)")):Array.from(i.querySelectorAll(":scope >  *:not(.hidden)")).reverse()).filter((t=>!t.classList.contains("disabled"))),o=i.querySelector(".hs-select-option-highlighted");s.length&&(s[0].focus(),o&&o.classList.remove("hs-select-option-highlighted"),s[0].classList.add("hs-select-option-highlighted"))}}static onEnter(t){const e=t.target.previousSibling;if(window.$hsSelectCollection.find((t=>t.element.el===e))){const t=window.$hsSelectCollection.find((t=>t.element.isOpened)),i=window.$hsSelectCollection.find((t=>t.element.el===e));t.element.close(),t!==i&&i.element.open()}else{const e=window.$hsSelectCollection.find((t=>t.element.isOpened));e&&e.element.onSelectOption(t.target.dataset.value||"")}}}window.addEventListener("load",(()=>{r.autoInit()})),document.addEventListener("scroll",(()=>{if(!window.$hsSelectCollection)return!1;const t=window.$hsSelectCollection.find((t=>t.element.isOpened));t&&t.element.recalculateDirection()})),"undefined"!=typeof window&&(window.HSSelect=r);const d=r;var h=s.A;export{h as default};