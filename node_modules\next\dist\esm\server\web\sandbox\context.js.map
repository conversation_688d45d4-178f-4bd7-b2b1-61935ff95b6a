{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["AsyncLocalStorage", "COMPILER_NAMES", "EDGE_UNSUPPORTED_NODE_APIS", "EdgeRuntime", "readFileSync", "promises", "fs", "validateURL", "pick", "fetchInlineAsset", "runInContext", "BufferImplementation", "EventsImplementation", "AssertImplementation", "UtilImplementation", "AsyncHooksImplementation", "intervalsManager", "timeouts<PERSON><PERSON><PERSON>", "getServerError", "decorateServerError", "process", "env", "NODE_ENV", "middleware", "require", "error", "_", "__", "moduleContexts", "Map", "pendingModuleCaches", "clearAllModuleContexts", "removeAll", "clear", "clearModuleContext", "path", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "injectedEnvironments", "pairs", "Object", "keys", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "Error", "edgeServer", "createProcessPolyfill", "processPolyfill", "overriddenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "entries", "requestStore", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "codeGeneration", "strings", "extend", "id", "TypeError", "__next_log_error__", "err", "onError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "assets", "distDir", "headers", "Headers", "store", "getStore", "prevs", "split", "concat", "moduleName", "join", "response", "url", "String", "catch", "message", "stack", "__Request", "Request", "constructor", "next", "__redirect", "Response", "redirect", "bind", "args", "assign", "performance", "setInterval", "clearInterval", "interval", "remove", "setTimeout", "clearTimeout", "timeout", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "getModuleContext", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "filename"], "mappings": "AAMA,SAASA,iBAAiB,QAAQ,cAAa;AAC/C,SACEC,cAAc,EACdC,0BAA0B,QACrB,gCAA+B;AACtC,SAASC,WAAW,QAAQ,kCAAiC;AAC7D,SAASC,YAAY,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACjD,SAASC,WAAW,QAAQ,WAAU;AACtC,SAASC,IAAI,QAAQ,oBAAmB;AACxC,SAASC,gBAAgB,QAAQ,wBAAuB;AACxD,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,wBAAwB,YAAW;AAC1C,OAAOC,8BAA8B,mBAAkB;AACvD,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,sBAAqB;AAQvE,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,aAAaC,QAAQ;IAC3BN,iBAAiBK,WAAWL,cAAc;IAC1CC,sBACEK,QAAQ,oCAAoCL,mBAAmB;AACnE,OAAO;IACLD,iBAAiB,CAACO,OAAcC,IAAcD;IAC9CN,sBAAsB,CAACO,GAAUC,MAAgB;AACnD;AAEA;;;;CAIC,GACD,MAAMC,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAEhC;;CAEC,GACD,OAAO,eAAeE;IACpBf,iBAAiBgB,SAAS;IAC1Bf,gBAAgBe,SAAS;IACzBJ,eAAeK,KAAK;IACpBH,oBAAoBG,KAAK;AAC3B;AAEA;;;;;;;CAOC,GACD,OAAO,eAAeC,mBAAmBC,IAAY;IACnDnB,iBAAiBgB,SAAS;IAC1Bf,gBAAgBe,SAAS;IAEzB,MAAMI,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACN,OAAO;YAC1BI,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIV,eAAgB;QACzCQ,cAAcC,KAAKC,OAAOV;IAC5B;IACA,KAAK,MAAM,CAACS,KAAKC,MAAM,IAAIR,oBAAqB;QAC9CM,cAAcC,KAAK,MAAMC,OAAOR;IAClC;AACF;AAEA,eAAea,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,SAAS,MAAMC,YAAYC,OAAO,CACtC,MAAM9C,GAAG+C,QAAQ,CAACJ,QAAQK,QAAQ;QAEpCT,OAAO,CAACI,QAAQM,IAAI,CAAC,GAAGL;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASW,8BACPC,oBAA4C;IAE5C,MAAMC,QAAQC,OAAOC,IAAI,CAACxC,QAAQC,GAAG,EAAE2B,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKjB,QAAQC,GAAG,CAACgB,IAAI;SAAC;IAC3E,MAAMhB,MAAMsC,OAAOE,WAAW,CAACH;IAC/B,KAAK,MAAMrB,OAAOsB,OAAOC,IAAI,CAACH,sBAAuB;QACnDpC,GAAG,CAACgB,IAAI,GAAGoB,oBAAoB,CAACpB,IAAI;IACtC;IACAhB,IAAIyC,YAAY,GAAG;IACnB,OAAOzC;AACT;AAEA,SAAS0C,yBAAyBR,IAAY;IAC5C,MAAM9B,QACJ,IAAIuC,MAAM,CAAC,uBAAuB,EAAET,KAAK;8DACiB,CAAC;IAC7DpC,oBAAoBM,OAAOxB,eAAegE,UAAU;IACpD,MAAMxC;AACR;AAEA,SAASyC,sBAAsB7C,GAA2B;IACxD,MAAM8C,kBAAkB;QAAE9C,KAAKmC,8BAA8BnC;IAAK;IAClE,MAAM+C,kBAAuC,CAAC;IAE9C,KAAK,MAAM/B,OAAOsB,OAAOC,IAAI,CAACxC,SAAU;QACtC,IAAIiB,QAAQ,OAAO;QACnBsB,OAAOU,cAAc,CAACF,iBAAiB9B,KAAK;YAC1CiC;gBACE,IAAIF,eAAe,CAAC/B,IAAI,KAAKkC,WAAW;oBACtC,OAAOH,eAAe,CAAC/B,IAAI;gBAC7B;gBACA,IAAI,OAAO,AAACjB,OAAe,CAACiB,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAM0B,yBAAyB,CAAC,QAAQ,EAAE1B,IAAI,CAAC;gBACxD;gBACA,OAAOkC;YACT;YACAC,KAAIC,KAAK;gBACPL,eAAe,CAAC/B,IAAI,GAAGoC;YACzB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQpC,OAA+B,EAAEgB,IAAY;IAC5DI,OAAOU,cAAc,CAAC9B,SAASgB,MAAM;QACnCe;YACE,OAAO;gBACLP,yBAAyBR;YAC3B;QACF;QACAmB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACtD;QACN,IAAIA,iBAAiBqD,kBAAkB;YACrC3D,oBAAoBM,OAAOxB,eAAegE,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/C3D,oBAAoB8D,SAASC,MAAM,EAAEjF,eAAegE,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAe5E,KAAKG,sBAAsB;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeH,KAAKI,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBJ,KAAKO,0BAA0B;YACjD;YACA;SACD;QACD,eAAeP,KAAKK,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaL,KAAKM,oBAAoB;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAIe,IAAI8B,OAAO0B,OAAO,CAACD;AAChC,CAAA;AAEA,OAAO,MAAME,eAAe,IAAItF,oBAE5B;AAEJ;;;CAGC,GACD,eAAeuF,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAM,EAAEE,iBAAiB,EAAE,GAAGJ;IAC9B,MAAM5C,OAAO,MAAMD,SAASiD,kBAAkBhD,IAAI,IAAI,EAAE;IACxD,MAAMiC,UAAU,IAAI1E,YAAY;QAC9B0F,gBACEzE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB;YAAEwE,SAAS;YAAMlD,MAAM;QAAK,IAC5B2B;QACNwB,QAAQ,CAACxD;YACPA,QAAQnB,OAAO,GAAG8C,sBAAsB0B,kBAAkBvE,GAAG;YAE7DsC,OAAOU,cAAc,CAAC9B,SAAS,WAAW;gBACxCmC,YAAY;gBACZD,OAAO,CAACuB;oBACN,MAAMvB,QAAQU,gBAAgBb,GAAG,CAAC0B;oBAClC,IAAI,CAACvB,OAAO;wBACV,MAAMwB,UAAU,8BAA8BD;oBAChD;oBACA,OAAOvB;gBACT;YACF;YAEA,IAAIrD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzCiB,QAAQ2D,kBAAkB,GAAG,SAAUC,GAAY;oBACjDX,QAAQY,OAAO,CAACD;gBAClB;YACF;YAEA5D,QAAQ8D,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAMjE,MAAMiE,GAAGC,QAAQ;gBACvB,IAAI,CAACd,YAAYhD,GAAG,CAACJ,MAAM;oBACzB,MAAMmE,UAAUtF,eACd,IAAI8C,MACF,CAAC;yEAC0D,CAAC,GAE9D/D,eAAegE,UAAU;oBAE3BuC,QAAQjD,IAAI,GAAG;oBACfS,MAAMyC,iBAAiB,CAACD,SAASH;oBACjCZ,YAAYiB,GAAG,CAACrE;oBAChBmD,QAAQmB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEA/D,QAAQqE,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAMjE,MAAMiE,GAAGC,QAAQ;gBACvB,IAAI,CAACZ,mBAAmBlD,GAAG,CAACJ,MAAM;oBAChC,MAAMmE,UAAUtF,eACd,IAAI8C,MAAM,CAAC;yEACgD,CAAC,GAC5D/D,eAAegE,UAAU;oBAE3BuC,QAAQjD,IAAI,GAAG;oBACfS,MAAMyC,iBAAiB,CAACD,SAASI;oBACjCjB,mBAAmBe,GAAG,CAACrE;oBACvBmD,QAAQmB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEF/D,QAAQsE,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAM3E,MAAMiE,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAACpB,mBAAmBlD,GAAG,CAACJ,MAAM;oBAC1D,MAAMmE,UAAUtF,eACd,IAAI8C,MAAM,CAAC;yEACgD,CAAC,GAC5D/D,eAAegE,UAAU;oBAE3BuC,QAAQjD,IAAI,GAAG;oBACfS,MAAMyC,iBAAiB,CAACD,SAASK;oBACjClB,mBAAmBe,GAAG,CAACrE;oBACvBmD,QAAQmB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAU1E,QAAQ2E,KAAK;YAC7B3E,QAAQ2E,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;oBA2BnCA;gBA1BF,MAAMC,eAAe,IAAIrD,MAAM;gBAC/B,MAAMsD,gBAAgB,MAAM7G,iBAAiB;oBAC3C0G;oBACAI,QAAQ/B,QAAQI,iBAAiB,CAAC2B,MAAM;oBACxCC,SAAShC,QAAQgC,OAAO;oBACxBjF;gBACF;gBACA,IAAI+E,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKK,OAAO,GAAG,IAAIC,QAAQN,KAAKK,OAAO,IAAI,CAAC;gBAE5C,sEAAsE;gBACtE,MAAME,QAAQrC,aAAasC,QAAQ;gBACnC,IACED,CAAAA,yBAAAA,MAAOF,OAAO,CAAChF,GAAG,CAAC,+BACnB,CAAC2E,KAAKK,OAAO,CAAChF,GAAG,CAAC,4BAClB;oBACA2E,KAAKK,OAAO,CAACjD,GAAG,CACd,2BACAmD,MAAMF,OAAO,CAACnD,GAAG,CAAC,8BAA8B;gBAEpD;gBAEA,MAAMuD,QACJT,EAAAA,oBAAAA,KAAKK,OAAO,CAACnD,GAAG,CAAC,CAAC,uBAAuB,CAAC,sBAA1C8C,kBAA6CU,KAAK,CAAC,SAAQ,EAAE;gBAC/D,MAAMrD,QAAQoD,MAAME,MAAM,CAACvC,QAAQwC,UAAU,EAAEC,IAAI,CAAC;gBACpDb,KAAKK,OAAO,CAACjD,GAAG,CAAC,2BAA2BC;gBAE5C,IAAI,CAAC2C,KAAKK,OAAO,CAAChF,GAAG,CAAC,eAAe;oBACnC2E,KAAKK,OAAO,CAACjD,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAM0D,WACJ,OAAOf,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMgB,GAAG,EAAE;oBACjB,GAAG3H,KAAK2G,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPK,SAAS;wBACP,GAAG9D,OAAOE,WAAW,CAACsD,MAAMM,OAAO,CAAC;wBACpC,GAAG9D,OAAOE,WAAW,CAACuD,KAAKK,OAAO,CAAC;oBACrC;gBACF,KACAR,QAAQmB,OAAOjB,QAAQC;gBAE7B,OAAO,MAAMc,SAASG,KAAK,CAAC,CAAClC;oBAC3BkB,aAAaiB,OAAO,GAAGnC,IAAImC,OAAO;oBAClCnC,IAAIoC,KAAK,GAAGlB,aAAakB,KAAK;oBAC9B,MAAMpC;gBACR;YACF;YAEA,MAAMqC,YAAYjG,QAAQkG,OAAO;YACjClG,QAAQkG,OAAO,GAAG,cAAcD;gBAE9BE,YAAYvB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMe,MACJ,OAAOhB,UAAU,YAAY,SAASA,QAClCA,MAAMgB,GAAG,GACTC,OAAOjB;oBACb5G,YAAY4H;oBACZ,KAAK,CAACA,KAAKf;oBACX,IAAI,CAACuB,IAAI,GAAGvB,wBAAAA,KAAMuB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAarG,QAAQsG,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACxG,QAAQsG,QAAQ;YAClEtG,QAAQsG,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BzI,YAAYyI,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAMzF,QAAQrD,2BAA4B;gBAC7CyE,QAAQpC,SAASgB;YACnB;YAEAI,OAAOsF,MAAM,CAAC1G,SAASK;YAEvBL,QAAQ2G,WAAW,GAAGA;YAEtB3G,QAAQvC,iBAAiB,GAAGA;YAE5B,+DAA+D;YAC/DuC,QAAQ4G,WAAW,GAAG,CAAC,GAAGH,OACxBhI,iBAAiB0F,GAAG,CAACsC;YAEvB,+DAA+D;YAC/DzG,QAAQ6G,aAAa,GAAG,CAACC,WACvBrI,iBAAiBsI,MAAM,CAACD;YAE1B,+DAA+D;YAC/D9G,QAAQgH,UAAU,GAAG,CAAC,GAAGP,OACvB/H,gBAAgByF,GAAG,CAACsC;YAEtB,+DAA+D;YAC/DzG,QAAQiH,YAAY,GAAG,CAACC,UACtBxI,gBAAgBqI,MAAM,CAACG;YAEzB,OAAOlH;QACT;IACF;IAEA,MAAMmH,yBAAyB9E,0BAA0BC;IACzDA,QAAQtC,OAAO,CAACoH,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6B5E,8BAA8BH;IACjEA,QAAQtC,OAAO,CAACoH,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACL/E;QACArC,OAAO,IAAIX;QACX4D,aAAa,IAAIC;IACnB;AACF;AAWA,SAASmE,uBAAuBrE,OAA6B;IAC3D,IAAIsE,wBAAwBhI,oBAAoBwC,GAAG,CAACkB,QAAQwC,UAAU;IACtE,IAAI,CAAC8B,uBAAuB;QAC1BA,wBAAwBvE,oBAAoBC;QAC5C1D,oBAAoB0C,GAAG,CAACgB,QAAQwC,UAAU,EAAE8B;IAC9C;IACA,OAAOA;AACT;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,iBAAiBvE,OAA6B;IAMlE,IAAIwE;IAIJ,IAAIxE,QAAQyE,QAAQ,EAAE;QACpBD,oBACEpI,eAAe0C,GAAG,CAACkB,QAAQwC,UAAU,KACpC,MAAM6B,uBAAuBrE;IAClC;IAEA,IAAI,CAACwE,mBAAmB;QACtBA,oBAAoB,MAAMzE,oBAAoBC;QAC9C5D,eAAe4C,GAAG,CAACgB,QAAQwC,UAAU,EAAEgC;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAc1H,KAAK,CAACC,GAAG,CAAC2H,WAAW;YACtC,MAAMC,UAAUjK,aAAagK,UAAU;YACvC,IAAI;gBACF1J,aAAa2J,SAASH,cAAcrF,OAAO,CAACtC,OAAO,EAAE;oBACnD+H,UAAUF;gBACZ;gBACAF,cAAc1H,KAAK,CAACgC,GAAG,CAAC4F,UAAUC;YACpC,EAAE,OAAO5I,OAAO;gBACd,IAAI+D,QAAQyE,QAAQ,EAAE;oBACpBC,iCAAAA,cAAe1H,KAAK,CAACE,MAAM,CAAC0H;gBAC9B;gBACA,MAAM3I;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAGyI,aAAa;QAAEC;IAAkB;AAC/C"}