!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("ApexCharts"));else if("function"==typeof define&&define.amd)define(["ApexCharts"],t);else{var s="object"==typeof exports?t(require("ApexCharts")):t(e.ApexCharts);for(var a in s)("object"==typeof exports?exports:e)[a]=s[a]}}(self,(e=>(()=>{"use strict";var t={24:function(e,t,s){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.buildChart=function(e,t,s,a){const l=document.querySelector(e);let n=null;if(!l)return!1;const o=l.closest('[role="tabpanel"]');let i=null;Array.from(document.querySelector("html").classList).forEach((e=>{["dark","light","default"].includes(e)&&(i=e)}));const d=(e=i||localStorage.getItem("hs_theme"))=>window._.merge(t(e),"dark"===e?a:s);if(l){let e=!0;n=new r.default(l,d()),n.render(),setTimeout((()=>{e=!1}),100);const t=t=>{e||n.updateOptions(d(t.detail))};window.addEventListener("on-hs-appearance-change",t),o&&o.addEventListener("on-hs-appearance-change",t)}return n},t.buildTooltip=function(e,t){const{title:s,mode:a,valuePrefix:r="$",isValueDivided:l=!0,valuePostfix:n="",hasTextLabel:o=!1,invertGroup:i=!1,labelDivider:d="",wrapperClasses:p="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:c="",seriesClasses:x="text-xs",seriesExtClasses:u="",titleClasses:$="font-semibold !text-sm !bg-white !border-gray-200 text-gray-800 rounded-t-lg dark:!bg-neutral-800 dark:!border-neutral-700 dark:text-neutral-200",titleExtClasses:v="",markerClasses:h="!w-2.5 !h-2.5 !me-1.5",markerExtClasses:g="!rounded-xs",valueClasses:f="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:m="",labelClasses:b="text-gray-500 dark:text-neutral-400",labelExtClasses:y="",thousandsShortName:k="k"}=t,{dataPointIndex:w}=e,{colors:C}=e.ctx.opts,E=e.ctx.opts.series;let j="";return E.forEach(((t,s)=>{const a=e.series[s][w]||("object"!=typeof E[s].data[w]?E[s].data[w]:e.series[s][w]),p=E[s].name,c=i?{left:`${o?p:""}${d}`,right:`${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}`}:{left:`${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}`,right:`${o?p:""}${d}`},$=`<span class="apexcharts-tooltip-text-y-label ${b} ${y}">${c.left}</span>`;j+=`<div class="apexcharts-tooltip-series-group !flex ${o?"!justify-between":""} order-${s+1} ${x} ${u}">\n      <span class="flex items-center">\n        <span class="apexcharts-tooltip-marker ${h} ${g}" style="background: ${C[s]}"></span>\n        <div class="apexcharts-tooltip-text">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            <span class="apexcharts-tooltip-text-y-value ${f} ${m}">${c.right}</span>\n          </div>\n        </div>\n      </span>\n      ${$}\n    </div>`})),`<div class="${"dark"===a?"dark ":""}${p} ${c}">\n    <div class="apexcharts-tooltip-title ${$} ${v}">${s}</div>\n    ${j}\n  </div>`},t.buildHeatmapTooltip=function(e,t){const{mode:s,valuePrefix:a="$",valuePostfix:r="",divider:l="",wrapperClasses:n="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:o="",markerClasses:i="!w-2.5 !h-2.5 !me-1.5",markerStyles:d="",markerExtClasses:p="!rounded-xs",valueClasses:c="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:x=""}=t,{dataPointIndex:u,seriesIndex:$,series:v}=e,{name:h}=e.ctx.opts.series[$],g=`${a}${v[$][u]}${r}`;return`<div class="${"dark"===s?"dark ":""}${n} ${o}">\n    <div class="apexcharts-tooltip-series-group !flex">\n\t\t\t<span class="apexcharts-tooltip-marker ${i} ${p}" style="${d}"></span>\n      <span class="flex items-center">\n        <div class="apexcharts-tooltip-text">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            <span class="apexcharts-tooltip-text-y-value ${c} ${x}">${h}${l}</span>\n          </div>\n        </div>\n      </span>\n\t\t\t<span class="apexcharts-tooltip-text-y-value ${c} ${x}">${g}</span>\n    </div>\n  </div>`},t.buildTooltipCompareTwo=function(e,t){const{title:s,mode:a,valuePrefix:r="$",isValueDivided:l=!0,valuePostfix:n="",hasCategory:o=!0,hasTextLabel:i=!1,labelDivider:d="",wrapperClasses:p="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:c="",seriesClasses:x="!justify-between w-full text-xs",seriesExtClasses:u="",titleClasses:$="flex justify-between font-semibold !text-sm !bg-white !border-gray-200 text-gray-800 rounded-t-lg dark:!bg-neutral-800 dark:!border-neutral-700 dark:text-neutral-200",titleExtClasses:v="",markerClasses:h="!w-2.5 !h-2.5 !me-1.5",markerExtClasses:g="!rounded-xs",valueClasses:f="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:m="",labelClasses:b="text-gray-500 dark:text-neutral-400 ms-2",labelExtClasses:y="",thousandsShortName:k="k"}=t,{dataPointIndex:w}=e,{categories:C}=e.ctx.opts.xaxis,{colors:E}=e.ctx.opts,j=e.ctx.opts.series;let S="";const A=j[0].data[w],P=j[1].data[w],T=C[w].split(" "),L=o?`${T[0]}${T[1]?" ":""}${T[1]?T[1].slice(0,3):""}`:"",q=A>P,B=A/P==1,D=B?0:A/P*100,_=q?'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/><polyline points="16 7 22 7 22 13"/></svg>':'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 17 13.5 8.5 8.5 13.5 2 7" /><polyline points="16 17 22 17 22 11" /></svg>';return j.forEach(((t,s)=>{const a=e.series[s][w]||("object"!=typeof j[s].data[w]?j[s].data[w]:e.series[s][w]),o=j[s].name,p=j[s].altValue||null,c=`<span class="apexcharts-tooltip-text-y-label ${b} ${y}">${L} ${o||""}</span>`,$=p||`<span class="apexcharts-tooltip-text-y-value ${f} ${m}">${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}${d}</span>`;S+=`<div class="apexcharts-tooltip-series-group ${x} !flex order-${s+1} ${u}">\n      <span class="flex items-center">\n        <span class="apexcharts-tooltip-marker ${h} ${g}" style="background: ${E[s]}"></span>\n        <div class="apexcharts-tooltip-text">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            ${$}\n          </div>\n        </div>\n      </span>\n      ${i?c:""}\n    </div>`})),`<div class="${"dark"===a?"dark ":""}${p} ${c}">\n    <div class="apexcharts-tooltip-title ${$} ${v}">\n      <span>${s}</span>\n      <span class="flex items-center gap-x-1 ${B?"":q?"text-green-600":"text-red-600"} ms-2">\n        ${B?"":_}\n        <span class="inline-block text-sm">\n          ${D.toFixed(1)}%\n        </span>\n      </span>\n    </div>\n    ${S}\n  </div>`},t.buildTooltipCompareTwoAlt=function(e,t){const{title:s,mode:a,valuePrefix:r="$",isValueDivided:l=!0,valuePostfix:n="",hasCategory:o=!0,hasTextLabel:i=!1,labelDivider:d="",wrapperClasses:p="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:c="",seriesClasses:x="!justify-between w-full text-xs",seriesExtClasses:u="",titleClasses:$="flex justify-between font-semibold !text-sm !bg-white !border-gray-200 text-gray-800 rounded-t-lg dark:!bg-neutral-800 dark:!border-neutral-700 dark:text-neutral-200",titleExtClasses:v="",markerClasses:h="!w-2.5 !h-2.5 !me-1.5",markerExtClasses:g="!rounded-xs",valueClasses:f="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:m="",labelClasses:b="text-gray-500 dark:text-neutral-400 ms-2",labelExtClasses:y="",thousandsShortName:k="k"}=t,{dataPointIndex:w}=e,{categories:C}=e.ctx.opts.xaxis,{colors:E}=e.ctx.opts,j=e.ctx.opts.series;let S="";const A=j[0].data[w],P=j[1].data[w],T=C[w].split(" "),L=o?`${T[0]}${T[1]?" ":""}${T[1]?T[1].slice(0,3):""}`:"",q=A>P,B=A/P==1,D=B?0:A/P*100,_=q?'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/><polyline points="16 7 22 7 22 13"/></svg>':'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 17 13.5 8.5 8.5 13.5 2 7" /><polyline points="16 17 22 17 22 11" /></svg>';return j.forEach(((t,s)=>{const a=e.series[s][w]||("object"!=typeof j[s].data[w]?j[s].data[w]:e.series[s][w]),o=j[s].name,p=`<span class="apexcharts-tooltip-text-y-label ${b} ${y}">${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}</span>`;S+=`<div class="apexcharts-tooltip-series-group !flex ${x} order-${s+1} ${u}">\n      <span class="flex items-center">\n        <span class="apexcharts-tooltip-marker ${h} ${g}" style="background: ${E[s]}"></span>\n        <div class="apexcharts-tooltip-text text-xs">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            <span class="apexcharts-tooltip-text-y-value ${f} ${m}">${L} ${o||""}${d}</span>\n          </div>\n        </div>\n      </span>\n      ${i?p:""}\n    </div>`})),`<div class="${"dark"===a?"dark ":""}${p} ${c}">\n    <div class="apexcharts-tooltip-title ${$} ${v}">\n      <span>${s}</span>\n      <span class="flex items-center gap-x-1 ${B?"":q?"text-green-600":"text-red-600"} ms-2">\n        ${B?"":_}\n        <span class="inline-block text-sm">\n          ${D.toFixed(1)}%\n        </span>\n      </span>\n    </div>\n    ${S}\n  </div>`},t.buildTooltipForDonut=function({series:e,seriesIndex:t,w:s},a){const{globals:r}=s,{colors:l}=r;return`<div class="apexcharts-tooltip-series-group" style="background-color: ${l[t]}; display: block;">\n    <div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;">\n      <div class="apexcharts-tooltip-y-group" style="color: ${a[t]}">\n        <span class="apexcharts-tooltip-text-y-label">${r.labels[t]}: </span>\n        <span class="apexcharts-tooltip-text-y-value">${e[t]}</span>\n      </div>\n    </div>\n  </div>`},t.fullBarHoverEffect=function(e,{shadowClasses:t="fill-gray-200"}={}){var s;const a=e.el.querySelector(".apexcharts-grid"),r=e.el.querySelector("svg");if(!a||!r)return;const l=(null===(s=e.w.config.xaxis)||void 0===s?void 0:s.categories)||[];if(0===l.length)return;let n=null,o=!1,i=!1;function d(){n&&o&&!i&&(i=!0,n.classList.remove("opacity-100"),null==n||n.remove(),n=null,o=!1,i=!1)}r.addEventListener("mousemove",(s=>{const r=a.getBoundingClientRect();if(s.clientX<r.left||s.clientX>r.right||s.clientY<r.top||s.clientY>r.bottom)return void d();const p=(s.clientX-r.left)/r.width,c=Math.floor(p*l.length);c<0||c>=l.length?d():function(s){var a;const r=e.el.querySelector(".apexcharts-bar-series");if(!r)return;const l=r.querySelectorAll("path")[s];if(!l)return;const d=l.getBBox(),p=d.x,c=d.y,x=d.width;c<=0||(n||(n=document.createElementNS("http://www.w3.org/2000/svg","rect"),n.setAttribute("y","0"),n.setAttribute("class",t),null===(a=l.parentNode)||void 0===a||a.insertBefore(n,l)),n.setAttribute("x",p.toString()),n.setAttribute("width",x.toString()),n.setAttribute("height",c.toString()),requestAnimationFrame((()=>{null==n||n.classList.add("opacity-100")})),o=!0,i=!1)}(c)})),r.addEventListener("mouseleave",d)};const r=a(s(157))},157:t=>{t.exports=e}},s={};var a=function e(a){var r=s[a];if(void 0!==r)return r.exports;var l=s[a]={exports:{}};return t[a].call(l.exports,l,l.exports,e),l.exports}(24);return a})()));