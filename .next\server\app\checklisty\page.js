(()=>{var e={};e.id=929,e.ids=[929],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4994:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>l}),s(3944),s(7609),s(996);var a=s(170),i=s(5002),r=s(3876),n=s.n(r),c=s(6299),o={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);s.d(t,o);let l=["",{children:["checklisty",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3944)),"D:\\Projekty\\verictus\\src\\app\\checklisty\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,7609)),"D:\\Projekty\\verictus\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,996,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Projekty\\verictus\\src\\app\\checklisty\\page.tsx"],x="/checklisty/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/checklisty/page",pathname:"/checklisty",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5589:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4080,23))},9385:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},7907:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]])},9099:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},4386:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},6404:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},5753:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},5322:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},9017:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5485:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(1511).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},3944:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y,metadata:()=>h});var a=s(2051),i=s(2349),r=s(5753),n=s(5322),c=s(4386),o=s(9017),l=s(6404),d=s(7907),x=s(9099),p=s(5485),m=s(9385);let h={title:"Checklisty - VERICTUS | Praktyczne listy kontrolne",description:"Gotowe checklisty i listy kontrolne do pobrania. Sprawdzone narzędzia pomocne w trudnych sytuacjach.",keywords:"checklisty, listy kontrolne, procedury, kroki do wykonania, praktyczne narzędzia"},y=()=>{let e=[{id:1,title:"Lista kontrolna po pożarze mieszkania",description:"Szczeg\xf3łowa lista krok\xf3w do wykonania po pożarze - od zabezpieczenia miejsca po kontakt z ubezpieczycielem.",category:"Procedury",items:25,downloadCount:"1.8k",difficulty:"Podstawowy",icon:r.Z,color:"text-red-600 bg-red-50 border-red-200",href:"/checklisty/po-pozarze-mieszkania.pdf",featured:!0},{id:2,title:"Pierwsze kroki po stracie bliskiej osoby",description:"Kompletna lista formalności i działań do podjęcia w pierwszych dniach po utracie.",category:"Wsparcie emocjonalne",items:18,downloadCount:"2.3k",difficulty:"Podstawowy",icon:n.Z,color:"text-pink-600 bg-pink-50 border-pink-200",href:"/checklisty/pierwsze-kroki-po-stracie.pdf",featured:!0},{id:3,title:"Działania po powodzi - lista pilnych zadań",description:"Priorytetowa lista działań do wykonania w pierwszych godzinach po powodzi.",category:"Procedury",items:20,downloadCount:"1.2k",difficulty:"Średniozaawansowany",icon:c.Z,color:"text-blue-600 bg-blue-50 border-blue-200",href:"/checklisty/dzialania-po-powodzi.pdf",featured:!1},{id:4,title:"Bezpieczna dezynfekcja - procedury krok po kroku",description:"Lista kontrolna dla bezpiecznego przeprowadzenia dezynfekcji pomieszczeń.",category:"Bezpieczeństwo",items:15,downloadCount:"890",difficulty:"Ekspertowy",icon:o.Z,color:"text-green-600 bg-green-50 border-green-200",href:"/checklisty/bezpieczna-dezynfekcja.pdf",featured:!1},{id:5,title:"Dokumentacja szk\xf3d - co fotografować",description:"Szczeg\xf3łowa lista element\xf3w do udokumentowania dla cel\xf3w ubezpieczeniowych.",category:"Procedury",items:12,downloadCount:"1.5k",difficulty:"Podstawowy",icon:l.Z,color:"text-purple-600 bg-purple-50 border-purple-200",href:"/checklisty/dokumentacja-szkod.pdf",featured:!1},{id:6,title:"Kontakty awaryjne - numery i instytucje",description:"Kompletna lista kontakt\xf3w do służb ratunkowych, instytucji i organizacji pomocowych.",category:"Praktyczne porady",items:30,downloadCount:"2.1k",difficulty:"Podstawowy",icon:d.Z,color:"text-indigo-600 bg-indigo-50 border-indigo-200",href:"/checklisty/kontakty-awaryjne.pdf",featured:!1}],t=e.filter(e=>e.featured),s=e.filter(e=>!e.featured);return(0,a.jsxs)("div",{className:"min-h-screen bg-base-50",children:[a.jsx("section",{className:"bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-20",children:a.jsx("div",{className:"container-max",children:(0,a.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-x-3 bg-primary-100 text-primary-800 px-6 py-3 rounded-full font-semibold shadow-soft mb-8",children:[a.jsx(d.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Praktyczne narzędzia"})]}),(0,a.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-secondary-900 mb-6 leading-tight",children:[a.jsx("span",{className:"gradient-text",children:"Checklisty"})," i listy kontrolne",a.jsx("br",{}),"do pobrania"]}),a.jsx("p",{className:"text-xl text-secondary-600 leading-relaxed mb-10",children:"Gotowe do użycia listy kontrolne, kt\xf3re pomogą Ci nie zapomnieć o ważnych krokach w trudnych sytuacjach. Wszystkie materiały są darmowe i dostępne do pobrania."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-primary-600 mb-2",children:"25+"}),a.jsx("div",{className:"text-secondary-600",children:"Checklisty"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-primary-600 mb-2",children:"10k+"}),a.jsx("div",{className:"text-secondary-600",children:"Pobrań"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-primary-600 mb-2",children:"100%"}),a.jsx("div",{className:"text-secondary-600",children:"Darmowe"})]})]})]})})}),a.jsx("section",{className:"py-12 border-b border-secondary-200",children:a.jsx("div",{className:"container-max",children:a.jsx("div",{className:"flex flex-wrap gap-4 justify-center",children:[{name:"Wszystkie",count:25},{name:"Procedury",count:12},{name:"Wsparcie emocjonalne",count:5},{name:"Bezpieczeństwo",count:4},{name:"Praktyczne porady",count:4}].map((e,t)=>(0,a.jsxs)("button",{className:`px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${0===t?"bg-primary-600 text-white shadow-medium":"bg-white text-secondary-700 border-2 border-secondary-200 hover:border-primary-300 hover:bg-primary-50"}`,children:[e.name," (",e.count,")"]},t))})})}),a.jsx("section",{className:"section-padding",children:(0,a.jsxs)("div",{className:"container-max",children:[a.jsx("h2",{className:"text-3xl font-bold text-secondary-900 mb-12 text-center",children:"Najpopularniejsze checklisty"}),a.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16",children:t.map(e=>{let t=e.icon;return a.jsx("div",{className:"group card-elevated hover:scale-105 transition-all duration-300",children:(0,a.jsxs)("div",{className:"flex items-start gap-x-6",children:[a.jsx("div",{className:`w-16 h-16 rounded-2xl flex items-center justify-center ${e.color} border-2`,children:a.jsx(t,{className:"w-8 h-8"})}),(0,a.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-xl font-bold text-secondary-900 group-hover:text-primary-600 transition-colors",children:e.title}),a.jsx("p",{className:"text-secondary-600 leading-relaxed",children:e.description})]}),(0,a.jsxs)("div",{className:"flex items-center gap-x-6 text-sm text-secondary-500",children:[(0,a.jsxs)("div",{className:"flex items-center gap-x-1",children:[a.jsx(d.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[e.items," punkt\xf3w"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-x-1",children:[a.jsx(x.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[e.downloadCount," pobrań"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-x-1",children:[a.jsx(p.Z,{className:"w-4 h-4"}),a.jsx("span",{children:e.difficulty})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,a.jsxs)("a",{href:e.href,className:"inline-flex items-center gap-x-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-bold transition-all duration-200 hover:shadow-medium",children:[a.jsx(x.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Pobierz PDF"})]}),(0,a.jsxs)(i.default,{href:`/checklisty/${e.id}`,className:"inline-flex items-center gap-x-2 text-primary-600 hover:text-primary-700 font-bold group-hover:translate-x-1 transition-all duration-200",children:[a.jsx("span",{children:"Zobacz szczeg\xf3ły"}),a.jsx(m.Z,{className:"w-5 h-5"})]})]})]})]})},e.id)})})]})}),a.jsx("section",{className:"pb-20",children:(0,a.jsxs)("div",{className:"container-max",children:[a.jsx("h2",{className:"text-3xl font-bold text-secondary-900 mb-12 text-center",children:"Wszystkie checklisty"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:s.map(e=>{let t=e.icon;return a.jsx("div",{className:"group card hover:scale-105 transition-all duration-300",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-x-4",children:[a.jsx("div",{className:`w-12 h-12 rounded-xl flex items-center justify-center ${e.color} border`,children:a.jsx(t,{className:"w-6 h-6"})}),a.jsx("div",{className:"expert-badge",children:e.category})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("h3",{className:"text-lg font-bold text-secondary-900 group-hover:text-primary-600 transition-colors",children:e.title}),a.jsx("p",{className:"text-secondary-600 text-sm leading-relaxed",children:e.description})]}),(0,a.jsxs)("div",{className:"flex items-center gap-x-4 text-xs text-secondary-500",children:[(0,a.jsxs)("div",{className:"flex items-center gap-x-1",children:[a.jsx(d.Z,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[e.items," punkt\xf3w"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-x-1",children:[a.jsx(x.Z,{className:"w-3 h-3"}),a.jsx("span",{children:e.downloadCount})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-x-3",children:[(0,a.jsxs)("a",{href:e.href,className:"flex-1 inline-flex items-center justify-center gap-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl font-semibold text-sm transition-all duration-200",children:[a.jsx(x.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Pobierz"})]}),(0,a.jsxs)(i.default,{href:`/checklisty/${e.id}`,className:"inline-flex items-center gap-x-1 text-primary-600 hover:text-primary-700 font-semibold text-sm group-hover:translate-x-1 transition-all duration-200",children:[a.jsx("span",{children:"Szczeg\xf3ły"}),a.jsx(m.Z,{className:"w-4 h-4"})]})]})]})},e.id)})})]})})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[542,689],()=>s(4994));module.exports=a})();