{"name": "@mdx-js/react", "version": "3.1.0", "description": "React context for MDX", "license": "MIT", "keywords": ["jsx", "markdown", "mdx", "react", "remark"], "homepage": "https://mdxjs.com", "repository": {"type": "git", "url": "https://github.com/mdx-js/mdx", "directory": "packages/react/"}, "bugs": "https://github.com/mdx-js/mdx/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://johno.com)", "contributors": ["<PERSON> <<EMAIL>> (https://johno.com)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <matija.ma<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stg.me)", "<PERSON> <<EMAIL>>"], "type": "module", "sideEffects": false, "exports": "./index.js", "files": ["lib/", "index.d.ts.map", "index.d.ts", "index.js"], "dependencies": {"@types/mdx": "^2.0.0"}, "peerDependencies": {"@types/react": ">=16", "react": ">=16"}, "devDependencies": {}, "scripts": {"test": "npm run test-coverage", "test-api": "node --conditions development --loader=../../script/jsx-loader.js test/index.jsx", "test-coverage": "c8 --100 --reporter lcov npm run test-api"}, "xo": {"overrides": [{"extends": "xo-react", "files": ["**/*.jsx"], "rules": {"react/jsx-no-bind": "off", "react/react-in-jsx-scope": "off"}}], "prettier": true, "rules": {"n/file-extension-in-import": "off"}}}