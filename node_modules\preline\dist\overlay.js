!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var o=t();for(var i in o)("object"==typeof exports?exports:e)[i]=o[i]}}(self,(()=>(()=>{"use strict";var e={223:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BREAKPOINTS=t.COMBO_BOX_ACCESSIBILITY_KEY_SET=t.SELECT_ACCESSIBILITY_KEY_SET=t.TABS_ACCESSIBILITY_KEY_SET=t.OVERLAY_ACCESSIBILITY_KEY_SET=t.DROPDOWN_ACCESSIBILITY_KEY_SET=t.POSITIONS=void 0,t.POSITIONS={auto:"auto","auto-start":"auto-start","auto-end":"auto-end",top:"top","top-left":"top-start","top-right":"top-end",bottom:"bottom","bottom-left":"bottom-start","bottom-right":"bottom-end",right:"right","right-start":"right-start","right-end":"right-end",left:"left","left-start":"left-start","left-end":"left-end"},t.DROPDOWN_ACCESSIBILITY_KEY_SET=["Escape","ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Home","End","Enter"],t.OVERLAY_ACCESSIBILITY_KEY_SET=["Escape","Tab"],t.TABS_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End"],t.SELECT_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter","Space","Tab"],t.COMBO_BOX_ACCESSIBILITY_KEY_SET=["ArrowUp","ArrowLeft","ArrowDown","ArrowRight","Home","End","Escape","Enter"],t.BREAKPOINTS={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536}},292:function(e,t){
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
Object.defineProperty(t,"__esModule",{value:!0}),t.menuSearchHistory=t.classToClassList=t.htmlToElement=t.afterTransition=t.dispatch=t.debounce=t.isScrollable=t.isParentOrElementHidden=t.isJson=t.isIpadOS=t.isIOS=t.isDirectChild=t.isFormElement=t.isFocused=t.isEnoughSpace=t.getHighestZIndex=t.getZIndex=t.getClassPropertyAlt=t.getClassProperty=t.stringToBoolean=void 0;t.stringToBoolean=e=>"true"===e;t.getClassProperty=(e,t,o="")=>(window.getComputedStyle(e).getPropertyValue(t)||o).replace(" ","");t.getClassPropertyAlt=(e,t,o="")=>{let i="";return e.classList.forEach((e=>{e.includes(t)&&(i=e)})),i.match(/:(.*)]/)?i.match(/:(.*)]/)[1]:o};const o=e=>window.getComputedStyle(e).getPropertyValue("z-index");t.getZIndex=o;t.getHighestZIndex=e=>{let t=Number.NEGATIVE_INFINITY;return e.forEach((e=>{let i=o(e);"auto"!==i&&(i=parseInt(i,10),i>t&&(t=i))})),t};t.isDirectChild=(e,t)=>{const o=e.children;for(let e=0;e<o.length;e++)if(o[e]===t)return!0;return!1};t.isEnoughSpace=(e,t,o="auto",i=10,n=null)=>{const s=t.getBoundingClientRect(),l=n?n.getBoundingClientRect():null,r=window.innerHeight,a=l?s.top-l.top:s.top,d=(n?l.bottom:r)-s.bottom,c=e.clientHeight+i;return"bottom"===o?d>=c:"top"===o?a>=c:a>=c||d>=c};t.isFocused=e=>document.activeElement===e;t.isFormElement=e=>e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement;t.isIOS=()=>!!/iPad|iPhone|iPod/.test(navigator.platform)||navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isIpadOS=()=>navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/MacIntel/.test(navigator.platform);t.isJson=e=>{if("string"!=typeof e)return!1;const t=e.trim()[0],o=e.trim().slice(-1);if("{"===t&&"}"===o||"["===t&&"]"===o)try{return JSON.parse(e),!0}catch(e){return!1}return!1};const i=e=>{if(!e)return!1;return"none"===window.getComputedStyle(e).display||i(e.parentElement)};t.isParentOrElementHidden=i;t.isScrollable=e=>{const t=window.getComputedStyle(e),o=t.overflowY,i=t.overflowX,n=("scroll"===o||"auto"===o)&&e.scrollHeight>e.clientHeight,s=("scroll"===i||"auto"===i)&&e.scrollWidth>e.clientWidth;return n||s};t.debounce=(e,t=200)=>{let o;return(...i)=>{clearTimeout(o),o=setTimeout((()=>{e.apply(this,i)}),t)}};t.dispatch=(e,t,o=null)=>{const i=new CustomEvent(e,{detail:{payload:o},bubbles:!0,cancelable:!0,composed:!1});t.dispatchEvent(i)};t.afterTransition=(e,t)=>{const o=()=>{t(),e.removeEventListener("transitionend",o,!0)},i=window.getComputedStyle(e),n=i.getPropertyValue("transition-duration");"none"!==i.getPropertyValue("transition-property")&&parseFloat(n)>0?e.addEventListener("transitionend",o,!0):t()};t.htmlToElement=e=>{const t=document.createElement("template");return e=e.trim(),t.innerHTML=e,t.content.firstChild};t.classToClassList=(e,t,o=" ",i="add")=>{e.split(o).forEach((e=>"add"===i?t.classList.add(e):t.classList.remove(e)))};const n={historyIndex:-1,addHistory(e){this.historyIndex=e},existsInHistory(e){return e>this.historyIndex},clearHistory(){this.historyIndex=-1}};t.menuSearchHistory=n},850:function(e,t,o){
/*
 * HSOverlay
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const n=o(292),s=o(223),l=i(o(961));class r extends l.default{constructor(e,t,o){var i,l,r,a,d,c;super(e,t,o),this.initialZIndex=0,this.toggleButtons=Array.from(document.querySelectorAll(`[data-hs-overlay="#${this.el.id}"]`));const h=this.collectToggleParameters(this.toggleButtons),u=e.getAttribute("data-hs-overlay-options"),p=u?JSON.parse(u):{},y=Object.assign(Object.assign(Object.assign({},p),h),t);this.hiddenClass=(null==y?void 0:y.hiddenClass)||"hidden",this.emulateScrollbarSpace=(null==y?void 0:y.emulateScrollbarSpace)||!1,this.isClosePrev=null===(i=null==y?void 0:y.isClosePrev)||void 0===i||i,this.backdropClasses=null!==(l=null==y?void 0:y.backdropClasses)&&void 0!==l?l:"hs-overlay-backdrop transition duration fixed inset-0 bg-gray-900/50 dark:bg-neutral-900/80",this.backdropParent="string"==typeof y.backdropParent?document.querySelector(y.backdropParent):document.body,this.backdropExtraClasses=null!==(r=null==y?void 0:y.backdropExtraClasses)&&void 0!==r?r:"",this.moveOverlayToBody=(null==y?void 0:y.moveOverlayToBody)||null,this.openNextOverlay=!1,this.autoHide=null,this.initContainer=(null===(a=this.el)||void 0===a?void 0:a.parentElement)||null,this.isCloseWhenClickInside=(0,n.stringToBoolean)((0,n.getClassProperty)(this.el,"--close-when-click-inside","false")||"false"),this.isTabAccessibilityLimited=(0,n.stringToBoolean)((0,n.getClassProperty)(this.el,"--tab-accessibility-limited","true")||"true"),this.isLayoutAffect=(0,n.stringToBoolean)((0,n.getClassProperty)(this.el,"--is-layout-affect","false")||"false"),this.hasAutofocus=(0,n.stringToBoolean)((0,n.getClassProperty)(this.el,"--has-autofocus","true")||"true"),this.hasDynamicZIndex=(0,n.stringToBoolean)((0,n.getClassProperty)(this.el,"--has-dynamic-z-index","false")||"false"),this.hasAbilityToCloseOnBackdropClick=(0,n.stringToBoolean)(this.el.getAttribute("data-hs-overlay-keyboard")||"true");const m=(0,n.getClassProperty)(this.el,"--auto-close"),v=(0,n.getClassProperty)(this.el,"--auto-close-equality-type"),f=(0,n.getClassProperty)(this.el,"--opened");this.autoClose=!isNaN(+m)&&isFinite(+m)?+m:s.BREAKPOINTS[m]||null,this.autoCloseEqualityType=null!==(d=v)&&void 0!==d?d:null,this.openedBreakpoint=(!isNaN(+f)&&isFinite(+f)?+f:s.BREAKPOINTS[f])||null,this.animationTarget=(null===(c=null==this?void 0:this.el)||void 0===c?void 0:c.querySelector(".hs-overlay-animation-target"))||this.el,this.initialZIndex=parseInt(getComputedStyle(this.el).zIndex,10),this.onElementClickListener=[],this.init()}elementClick(){const e=()=>{const e={el:this.el,isOpened:!!this.el.classList.contains("open")};this.fireEvent("toggleClicked",e),(0,n.dispatch)("toggleClicked.hs.overlay",this.el,e)};this.el.classList.contains("opened")?this.close(!1,e):this.open(e)}overlayClick(e){e.target.id&&`#${e.target.id}`===this.el.id&&this.isCloseWhenClickInside&&this.hasAbilityToCloseOnBackdropClick&&this.close()}backdropClick(){this.close()}init(){if(this.createCollection(window.$hsOverlayCollection,this),this.isLayoutAffect&&this.openedBreakpoint){const e=r.getInstance(this.el,!0);r.setOpened(this.openedBreakpoint,e)}this.onOverlayClickListener=e=>this.overlayClick(e),this.el.addEventListener("click",this.onOverlayClickListener),this.toggleButtons.length&&this.buildToggleButtons()}getElementsByZIndex(){return window.$hsOverlayCollection.filter((e=>e.element.initialZIndex===this.initialZIndex))}buildToggleButtons(){this.toggleButtons.forEach((e=>{this.el.classList.contains("opened")?e.ariaExpanded="true":e.ariaExpanded="false",this.onElementClickListener.push({el:e,fn:()=>this.elementClick()}),e.addEventListener("click",this.onElementClickListener.find((t=>t.el===e)).fn)}))}hideAuto(){const e=parseInt((0,n.getClassProperty)(this.el,"--auto-hide","0"));e&&(this.autoHide=setTimeout((()=>{this.close()}),e))}checkTimer(){this.autoHide&&(clearTimeout(this.autoHide),this.autoHide=null)}buildBackdrop(){const e=this.el.classList.value.split(" "),t=parseInt(window.getComputedStyle(this.el).getPropertyValue("z-index")),o=this.el.getAttribute("data-hs-overlay-backdrop-container")||!1;this.backdrop=document.createElement("div");let i=`${this.backdropClasses} ${this.backdropExtraClasses}`;const s="static"!==(0,n.getClassProperty)(this.el,"--overlay-backdrop","true"),l="false"===(0,n.getClassProperty)(this.el,"--overlay-backdrop","true");this.backdrop.id=`${this.el.id}-backdrop`,"style"in this.backdrop&&(this.backdrop.style.zIndex=""+(t-1));for(const t of e)(t.startsWith("hs-overlay-backdrop-open:")||t.includes(":hs-overlay-backdrop-open:"))&&(i+=` ${t}`);l||(o&&(this.backdrop=document.querySelector(o).cloneNode(!0),this.backdrop.classList.remove("hidden"),i=`${this.backdrop.classList.toString()}`,this.backdrop.classList.value=""),s&&(this.onBackdropClickListener=()=>this.backdropClick(),this.backdrop.addEventListener("click",this.onBackdropClickListener,!0)),this.backdrop.setAttribute("data-hs-overlay-backdrop-template",""),this.backdropParent.appendChild(this.backdrop),setTimeout((()=>{this.backdrop.classList.value=i})))}destroyBackdrop(){const e=document.querySelector(`#${this.el.id}-backdrop`);e&&(this.openNextOverlay&&(e.style.transitionDuration=1.8*parseFloat(window.getComputedStyle(e).transitionDuration.replace(/[^\d.-]/g,""))+"s"),e.classList.add("opacity-0"),(0,n.afterTransition)(e,(()=>{e.remove()})))}focusElement(){const e=this.el.querySelector("[autofocus]");if(!e)return!1;e.focus()}getScrollbarSize(){let e=document.createElement("div");e.style.overflow="scroll",e.style.width="100px",e.style.height="100px",document.body.appendChild(e);let t=e.offsetWidth-e.clientWidth;return document.body.removeChild(e),t}collectToggleParameters(e){let t={};return e.forEach((e=>{const o=e.getAttribute("data-hs-overlay-options"),i=o?JSON.parse(o):{};t=Object.assign(Object.assign({},t),i)})),t}isElementVisible(){const e=window.getComputedStyle(this.el);if("none"===e.display||"hidden"===e.visibility||"0"===e.opacity)return!1;const t=this.el.getBoundingClientRect();if(0===t.width||0===t.height)return!1;let o=this.el.parentElement;for(;o;){const e=window.getComputedStyle(o);if("none"===e.display||"hidden"===e.visibility||"0"===e.opacity)return!1;o=o.parentElement}return!0}open(e=null){this.hasDynamicZIndex&&(r.currentZIndex<this.initialZIndex&&(r.currentZIndex=this.initialZIndex),r.currentZIndex++,this.el.style.zIndex=`${r.currentZIndex}`);const t=document.querySelectorAll(".hs-overlay.open"),o=window.$hsOverlayCollection.find((e=>Array.from(t).includes(e.element.el)&&!e.element.isLayoutAffect)),i=document.querySelectorAll(`[data-hs-overlay="#${this.el.id}"]`),s="true"!==(0,n.getClassProperty)(this.el,"--body-scroll","false");if(this.isClosePrev&&o)return this.openNextOverlay=!0,o.element.close().then((()=>{this.open(),this.openNextOverlay=!1}));s&&(document.body.style.overflow="hidden",this.emulateScrollbarSpace&&(document.body.style.paddingRight=`${this.getScrollbarSize()}px`)),this.buildBackdrop(),this.checkTimer(),this.hideAuto(),i.forEach((e=>{e.ariaExpanded&&(e.ariaExpanded="true")})),this.el.classList.remove(this.hiddenClass),this.el.setAttribute("aria-overlay","true"),this.el.setAttribute("tabindex","-1"),setTimeout((()=>{if(this.el.classList.contains("opened"))return!1;this.el.classList.add("open","opened"),this.isLayoutAffect&&document.body.classList.add("hs-overlay-body-open"),this.fireEvent("open",this.el),(0,n.dispatch)("open.hs.overlay",this.el,this.el),this.hasAutofocus&&this.focusElement(),"function"==typeof e&&e(),this.isElementVisible()&&r.openedItemsQty++}),50)}close(e=!1,t=null){this.isElementVisible()&&(r.openedItemsQty=r.openedItemsQty<=0?0:r.openedItemsQty-1),0===r.openedItemsQty&&this.isLayoutAffect&&document.body.classList.remove("hs-overlay-body-open");const o=e=>{if(this.el.classList.contains("open"))return!1;document.querySelectorAll(`[data-hs-overlay="#${this.el.id}"]`).forEach((e=>{e.ariaExpanded&&(e.ariaExpanded="false")})),this.el.classList.add(this.hiddenClass),this.hasDynamicZIndex&&(this.el.style.zIndex=""),this.destroyBackdrop(),this.fireEvent("close",this.el),(0,n.dispatch)("close.hs.overlay",this.el,this.el),document.querySelector(".hs-overlay.opened")||(document.body.style.overflow="",this.emulateScrollbarSpace&&(document.body.style.paddingRight="")),e(this.el),"function"==typeof t&&t(),0===r.openedItemsQty&&(document.body.classList.remove("hs-overlay-body-open"),this.hasDynamicZIndex&&(r.currentZIndex=0))};return new Promise((t=>{this.el.classList.remove("open","opened"),this.el.removeAttribute("aria-overlay"),this.el.removeAttribute("tabindex"),e?o(t):(0,n.afterTransition)(this.animationTarget,(()=>o(t)))}))}destroy(){this.el.classList.remove("open","opened",this.hiddenClass),this.isLayoutAffect&&document.body.classList.remove("hs-overlay-body-open"),this.el.removeEventListener("click",this.onOverlayClickListener),this.onElementClickListener.length&&(this.onElementClickListener.forEach((({el:e,fn:t})=>{e.removeEventListener("click",t)})),this.onElementClickListener=null),this.backdrop&&this.backdrop.removeEventListener("click",this.onBackdropClickListener),this.backdrop&&(this.backdrop.remove(),this.backdrop=null),window.$hsOverlayCollection=window.$hsOverlayCollection.filter((({element:e})=>e.el!==this.el))}static findInCollection(e){return window.$hsOverlayCollection.find((t=>e instanceof r?t.element.el===e.el:"string"==typeof e?t.element.el===document.querySelector(e):t.element.el===e))||null}static getInstance(e,t){const o="string"==typeof e?document.querySelector(e):e,i=(null==o?void 0:o.getAttribute("data-hs-overlay"))?o.getAttribute("data-hs-overlay"):e,n=window.$hsOverlayCollection.find((e=>e.element.el===("string"==typeof i?document.querySelector(i):i)||e.element.el===("string"==typeof i?document.querySelector(i):i)));return n?t?n:n.element.el:null}static autoInit(){window.$hsOverlayCollection||(window.$hsOverlayCollection=[],document.addEventListener("keydown",(e=>r.accessibility(e)))),window.$hsOverlayCollection&&(window.$hsOverlayCollection=window.$hsOverlayCollection.filter((({element:e})=>document.contains(e.el)))),document.querySelectorAll(".hs-overlay:not(.--prevent-on-load-init)").forEach((e=>{window.$hsOverlayCollection.find((t=>{var o;return(null===(o=null==t?void 0:t.element)||void 0===o?void 0:o.el)===e}))||new r(e)}))}static open(e){const t=r.findInCollection(e);t&&t.element.el.classList.contains(t.element.hiddenClass)&&t.element.open()}static close(e){const t=r.findInCollection(e);t&&!t.element.el.classList.contains(t.element.hiddenClass)&&t.element.close()}static setOpened(e,t){document.body.clientWidth>=e?(document.body.classList.add("hs-overlay-body-open"),t.element.open()):t.element.close(!0)}static accessibility(e){var t,o;const i=document.querySelectorAll(".hs-overlay.open"),s=(0,n.getHighestZIndex)(Array.from(i)),l=window.$hsOverlayCollection.filter((e=>e.element.el.classList.contains("open"))).find((e=>window.getComputedStyle(e.element.el).getPropertyValue("z-index")===`${s}`)),r=null===(o=null===(t=null==l?void 0:l.element)||void 0===t?void 0:t.el)||void 0===o?void 0:o.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),a=[];(null==r?void 0:r.length)&&r.forEach((e=>{(0,n.isParentOrElementHidden)(e)||a.push(e)}));const d=l&&!e.metaKey;if(d&&!l.element.isTabAccessibilityLimited&&"Tab"===e.code)return!1;d&&a.length&&"Tab"===e.code&&(e.preventDefault(),this.onTab(l)),d&&"Escape"===e.code&&(e.preventDefault(),this.onEscape(l))}static onEscape(e){e&&e.element.hasAbilityToCloseOnBackdropClick&&e.element.close()}static onTab(e){const t=e.element.el,o=Array.from(t.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'));if(0===o.length)return!1;const i=t.querySelector(":focus");if(i){let e=!1;for(const t of o){if(e)return void t.focus();t===i&&(e=!0)}o[0].focus()}else o[0].focus()}static on(e,t,o){const i=r.findInCollection(t);i&&(i.element.events[e]=o)}}r.openedItemsQty=0,r.currentZIndex=0;const a=()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.moveOverlayToBody)))return!1;window.$hsOverlayCollection.filter((e=>e.element.moveOverlayToBody)).forEach((e=>{const t=e.element.moveOverlayToBody,o=e.element.initContainer,i=document.querySelector("body"),s=e.element.el;if(!o&&s)return!1;document.body.clientWidth<=t&&!(0,n.isDirectChild)(i,s)?i.appendChild(s):document.body.clientWidth>t&&!o.contains(s)&&o.appendChild(s)}))};window.addEventListener("load",(()=>{r.autoInit(),a()})),window.addEventListener("resize",(()=>{(()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.autoClose)))return!1;window.$hsOverlayCollection.filter((e=>e.element.autoClose)).forEach((e=>{const{autoCloseEqualityType:t,autoClose:o}=e.element;("less-than"===t?document.body.clientWidth<=o:document.body.clientWidth>=o)?e.element.close(!0):e.element.isLayoutAffect&&document.body.classList.add("hs-overlay-body-open")}))})(),a(),(()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.autoClose)))return!1;window.$hsOverlayCollection.filter((e=>e.element.autoClose)).forEach((e=>{const{autoCloseEqualityType:t,autoClose:o}=e.element;("less-than"===t?document.body.clientWidth<=o:document.body.clientWidth>=o)&&e.element.close(!0)}))})(),(()=>{if(!window.$hsOverlayCollection.length||!window.$hsOverlayCollection.find((e=>e.element.el.classList.contains("opened"))))return!1;window.$hsOverlayCollection.filter((e=>e.element.el.classList.contains("opened"))).forEach((e=>{const t=parseInt(window.getComputedStyle(e.element.el).getPropertyValue("z-index")),o=document.querySelector(`#${e.element.el.id}-backdrop`);return!!o&&(t!==parseInt(window.getComputedStyle(o).getPropertyValue("z-index"))+1&&("style"in o&&(o.style.zIndex=""+(t-1)),void document.body.classList.add("hs-overlay-body-open")))}))})()})),"undefined"!=typeof window&&(window.HSOverlay=r),t.default=r},961:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t,o){this.el=e,this.options=t,this.events=o,this.el=e,this.options=t,this.events={}}createCollection(e,t){var o;e.push({id:(null===(o=null==t?void 0:t.el)||void 0===o?void 0:o.id)||e.length+1,element:t})}fireEvent(e,t=null){if(this.events.hasOwnProperty(e))return this.events[e](t)}on(e,t){this.events[e]=t}}}},t={};var o=function o(i){var n=t[i];if(void 0!==n)return n.exports;var s=t[i]={exports:{}};return e[i].call(s.exports,s,s.exports,o),s.exports}(850);return o})()));