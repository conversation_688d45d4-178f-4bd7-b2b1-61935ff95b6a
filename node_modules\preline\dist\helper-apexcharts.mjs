import*as e from"ApexCharts";var t={d:(e,s)=>{for(var a in s)t.o(s,a)&&!t.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:s[a]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const s=(e=>{var s={};return t.d(s,e),s})({default:()=>e.default});
/*
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */
function a(e,t){const{title:s,mode:a,valuePrefix:r="$",isValueDivided:l=!0,valuePostfix:n="",hasTextLabel:o=!1,invertGroup:i=!1,labelDivider:d="",wrapperClasses:p="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:c="",seriesClasses:x="text-xs",seriesExtClasses:u="",titleClasses:$="font-semibold !text-sm !bg-white !border-gray-200 text-gray-800 rounded-t-lg dark:!bg-neutral-800 dark:!border-neutral-700 dark:text-neutral-200",titleExtClasses:g="",markerClasses:h="!w-2.5 !h-2.5 !me-1.5",markerExtClasses:v="!rounded-xs",valueClasses:m="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:b="",labelClasses:f="text-gray-500 dark:text-neutral-400",labelExtClasses:y="",thousandsShortName:k="k"}=t,{dataPointIndex:w}=e,{colors:C}=e.ctx.opts,E=e.ctx.opts.series;let S="";return E.forEach(((t,s)=>{const a=e.series[s][w]||("object"!=typeof E[s].data[w]?E[s].data[w]:e.series[s][w]),p=E[s].name,c=i?{left:`${o?p:""}${d}`,right:`${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}`}:{left:`${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}`,right:`${o?p:""}${d}`},$=`<span class="apexcharts-tooltip-text-y-label ${f} ${y}">${c.left}</span>`;S+=`<div class="apexcharts-tooltip-series-group !flex ${o?"!justify-between":""} order-${s+1} ${x} ${u}">\n      <span class="flex items-center">\n        <span class="apexcharts-tooltip-marker ${h} ${v}" style="background: ${C[s]}"></span>\n        <div class="apexcharts-tooltip-text">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            <span class="apexcharts-tooltip-text-y-value ${m} ${b}">${c.right}</span>\n          </div>\n        </div>\n      </span>\n      ${$}\n    </div>`})),`<div class="${"dark"===a?"dark ":""}${p} ${c}">\n    <div class="apexcharts-tooltip-title ${$} ${g}">${s}</div>\n    ${S}\n  </div>`}function r(e,t){const{mode:s,valuePrefix:a="$",valuePostfix:r="",divider:l="",wrapperClasses:n="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:o="",markerClasses:i="!w-2.5 !h-2.5 !me-1.5",markerStyles:d="",markerExtClasses:p="!rounded-xs",valueClasses:c="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:x=""}=t,{dataPointIndex:u,seriesIndex:$,series:g}=e,{name:h}=e.ctx.opts.series[$];return`<div class="${"dark"===s?"dark ":""}${n} ${o}">\n    <div class="apexcharts-tooltip-series-group !flex">\n\t\t\t<span class="apexcharts-tooltip-marker ${i} ${p}" style="${d}"></span>\n      <span class="flex items-center">\n        <div class="apexcharts-tooltip-text">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            <span class="apexcharts-tooltip-text-y-value ${c} ${x}">${h}${l}</span>\n          </div>\n        </div>\n      </span>\n\t\t\t<span class="apexcharts-tooltip-text-y-value ${c} ${x}">${`${a}${g[$][u]}${r}`}</span>\n    </div>\n  </div>`}function l(e,t){const{title:s,mode:a,valuePrefix:r="$",isValueDivided:l=!0,valuePostfix:n="",hasCategory:o=!0,hasTextLabel:i=!1,labelDivider:d="",wrapperClasses:p="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:c="",seriesClasses:x="!justify-between w-full text-xs",seriesExtClasses:u="",titleClasses:$="flex justify-between font-semibold !text-sm !bg-white !border-gray-200 text-gray-800 rounded-t-lg dark:!bg-neutral-800 dark:!border-neutral-700 dark:text-neutral-200",titleExtClasses:g="",markerClasses:h="!w-2.5 !h-2.5 !me-1.5",markerExtClasses:v="!rounded-xs",valueClasses:m="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:b="",labelClasses:f="text-gray-500 dark:text-neutral-400 ms-2",labelExtClasses:y="",thousandsShortName:k="k"}=t,{dataPointIndex:w}=e,{categories:C}=e.ctx.opts.xaxis,{colors:E}=e.ctx.opts,S=e.ctx.opts.series;let j="";const P=S[0].data[w],A=S[1].data[w],L=C[w].split(" "),B=o?`${L[0]}${L[1]?" ":""}${L[1]?L[1].slice(0,3):""}`:"",q=P>A,I=P/A==1,D=I?0:P/A*100,z=q?'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/><polyline points="16 7 22 7 22 13"/></svg>':'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 17 13.5 8.5 8.5 13.5 2 7" /><polyline points="16 17 22 17 22 11" /></svg>';return S.forEach(((t,s)=>{const a=e.series[s][w]||("object"!=typeof S[s].data[w]?S[s].data[w]:e.series[s][w]),o=S[s].name,p=S[s].altValue||null,c=`<span class="apexcharts-tooltip-text-y-label ${f} ${y}">${B} ${o||""}</span>`,$=p||`<span class="apexcharts-tooltip-text-y-value ${m} ${b}">${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}${d}</span>`;j+=`<div class="apexcharts-tooltip-series-group ${x} !flex order-${s+1} ${u}">\n      <span class="flex items-center">\n        <span class="apexcharts-tooltip-marker ${h} ${v}" style="background: ${E[s]}"></span>\n        <div class="apexcharts-tooltip-text">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            ${$}\n          </div>\n        </div>\n      </span>\n      ${i?c:""}\n    </div>`})),`<div class="${"dark"===a?"dark ":""}${p} ${c}">\n    <div class="apexcharts-tooltip-title ${$} ${g}">\n      <span>${s}</span>\n      <span class="flex items-center gap-x-1 ${I?"":q?"text-green-600":"text-red-600"} ms-2">\n        ${I?"":z}\n        <span class="inline-block text-sm">\n          ${D.toFixed(1)}%\n        </span>\n      </span>\n    </div>\n    ${j}\n  </div>`}function n(e,t){const{title:s,mode:a,valuePrefix:r="$",isValueDivided:l=!0,valuePostfix:n="",hasCategory:o=!0,hasTextLabel:i=!1,labelDivider:d="",wrapperClasses:p="ms-0.5 mb-2 bg-white border border-gray-200 text-gray-800 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700",wrapperExtClasses:c="",seriesClasses:x="!justify-between w-full text-xs",seriesExtClasses:u="",titleClasses:$="flex justify-between font-semibold !text-sm !bg-white !border-gray-200 text-gray-800 rounded-t-lg dark:!bg-neutral-800 dark:!border-neutral-700 dark:text-neutral-200",titleExtClasses:g="",markerClasses:h="!w-2.5 !h-2.5 !me-1.5",markerExtClasses:v="!rounded-xs",valueClasses:m="!font-medium text-gray-500 !ms-auto dark:text-neutral-400",valueExtClasses:b="",labelClasses:f="text-gray-500 dark:text-neutral-400 ms-2",labelExtClasses:y="",thousandsShortName:k="k"}=t,{dataPointIndex:w}=e,{categories:C}=e.ctx.opts.xaxis,{colors:E}=e.ctx.opts,S=e.ctx.opts.series;let j="";const P=S[0].data[w],A=S[1].data[w],L=C[w].split(" "),B=o?`${L[0]}${L[1]?" ":""}${L[1]?L[1].slice(0,3):""}`:"",q=P>A,I=P/A==1,D=I?0:P/A*100,z=q?'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/><polyline points="16 7 22 7 22 13"/></svg>':'<svg class="inline-block size-4 self-center" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 17 13.5 8.5 8.5 13.5 2 7" /><polyline points="16 17 22 17 22 11" /></svg>';return S.forEach(((t,s)=>{const a=e.series[s][w]||("object"!=typeof S[s].data[w]?S[s].data[w]:e.series[s][w]),o=S[s].name,p=`<span class="apexcharts-tooltip-text-y-label ${f} ${y}">${r}${a>=1e3&&l?`${a/1e3}${k}`:a}${n}</span>`;j+=`<div class="apexcharts-tooltip-series-group !flex ${x} order-${s+1} ${u}">\n      <span class="flex items-center">\n        <span class="apexcharts-tooltip-marker ${h} ${v}" style="background: ${E[s]}"></span>\n        <div class="apexcharts-tooltip-text text-xs">\n          <div class="apexcharts-tooltip-y-group !py-0.5">\n            <span class="apexcharts-tooltip-text-y-value ${m} ${b}">${B} ${o||""}${d}</span>\n          </div>\n        </div>\n      </span>\n      ${i?p:""}\n    </div>`})),`<div class="${"dark"===a?"dark ":""}${p} ${c}">\n    <div class="apexcharts-tooltip-title ${$} ${g}">\n      <span>${s}</span>\n      <span class="flex items-center gap-x-1 ${I?"":q?"text-green-600":"text-red-600"} ms-2">\n        ${I?"":z}\n        <span class="inline-block text-sm">\n          ${D.toFixed(1)}%\n        </span>\n      </span>\n    </div>\n    ${j}\n  </div>`}function o({series:e,seriesIndex:t,w:s},a){const{globals:r}=s,{colors:l}=r;return`<div class="apexcharts-tooltip-series-group" style="background-color: ${l[t]}; display: block;">\n    <div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;">\n      <div class="apexcharts-tooltip-y-group" style="color: ${a[t]}">\n        <span class="apexcharts-tooltip-text-y-label">${r.labels[t]}: </span>\n        <span class="apexcharts-tooltip-text-y-value">${e[t]}</span>\n      </div>\n    </div>\n  </div>`}function i(e,t,a,r){const l=document.querySelector(e);let n=null;if(!l)return!1;const o=l.closest('[role="tabpanel"]');let i=null;Array.from(document.querySelector("html").classList).forEach((e=>{["dark","light","default"].includes(e)&&(i=e)}));const d=(e=i||localStorage.getItem("hs_theme"))=>window._.merge(t(e),"dark"===e?r:a);if(l){let e=!0;n=new s.default(l,d()),n.render(),setTimeout((()=>{e=!1}),100);const t=t=>{e||n.updateOptions(d(t.detail))};window.addEventListener("on-hs-appearance-change",t),o&&o.addEventListener("on-hs-appearance-change",t)}return n}function d(e,{shadowClasses:t="fill-gray-200"}={}){var s;const a=e.el.querySelector(".apexcharts-grid"),r=e.el.querySelector("svg");if(!a||!r)return;const l=(null===(s=e.w.config.xaxis)||void 0===s?void 0:s.categories)||[];if(0===l.length)return;let n=null,o=!1,i=!1;function d(){n&&o&&!i&&(i=!0,n.classList.remove("opacity-100"),null==n||n.remove(),n=null,o=!1,i=!1)}r.addEventListener("mousemove",(s=>{const r=a.getBoundingClientRect();if(s.clientX<r.left||s.clientX>r.right||s.clientY<r.top||s.clientY>r.bottom)return void d();const p=(s.clientX-r.left)/r.width,c=Math.floor(p*l.length);c<0||c>=l.length?d():function(s){var a;const r=e.el.querySelector(".apexcharts-bar-series");if(!r)return;const l=r.querySelectorAll("path")[s];if(!l)return;const d=l.getBBox(),p=d.x,c=d.y,x=d.width;c<=0||(n||(n=document.createElementNS("http://www.w3.org/2000/svg","rect"),n.setAttribute("y","0"),n.setAttribute("class",t),null===(a=l.parentNode)||void 0===a||a.insertBefore(n,l)),n.setAttribute("x",p.toString()),n.setAttribute("width",x.toString()),n.setAttribute("height",c.toString()),requestAnimationFrame((()=>{null==n||n.classList.add("opacity-100")})),o=!0,i=!1)}(c)})),r.addEventListener("mouseleave",d)}export{i as buildChart,r as buildHeatmapTooltip,a as buildTooltip,l as buildTooltipCompareTwo,n as buildTooltipCompareTwoAlt,o as buildTooltipForDonut,d as fullBarHoverEffect};