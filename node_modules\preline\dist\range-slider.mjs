var t={615:(t,o,i)=>{i.d(o,{A:()=>e});class e{constructor(t,o,i){this.el=t,this.options=o,this.events=i,this.el=t,this.options=o,this.events={}}createCollection(t,o){var i;t.push({id:(null===(i=null==o?void 0:o.el)||void 0===i?void 0:i.id)||t.length+1,element:o})}fireEvent(t,o=null){if(this.events.hasOwnProperty(t))return this.events[t](o)}on(t,o){this.events[t]=o}}}},o={};function i(e){var n=o[e];if(void 0!==n)return n.exports;var s=o[e]={exports:{}};return t[e](s,s.exports,i),s.exports}i.d=(t,o)=>{for(var e in o)i.o(o,e)&&!i.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:o[e]})},i.o=(t,o)=>Object.prototype.hasOwnProperty.call(t,o);var e={};i.d(e,{A:()=>r});var n=i(615);
/*
 * HSRangeSlider
 * @version: 3.1.0
 * @author: Preline Labs Ltd.
 * @license: Licensed under MIT and Preline UI Fair Use License (https://preline.co/docs/license.html)
 * Copyright 2024 Preline Labs Ltd.
 */class s extends n.A{constructor(t,o,i){var e;super(t,o,i);const n=t.getAttribute("data-hs-range-slider"),s=n?JSON.parse(n):{};this.concatOptions=Object.assign(Object.assign(Object.assign({},s),o),{cssClasses:Object.assign(Object.assign({},noUiSlider.cssClasses),this.processClasses(s.cssClasses))}),this.wrapper=this.concatOptions.wrapper||t.closest(".hs-range-slider-wrapper")||null,this.currentValue=this.concatOptions.currentValue?Array.from(this.concatOptions.currentValue):Array.from((null===(e=this.wrapper)||void 0===e?void 0:e.querySelectorAll(".hs-range-slider-current-value"))||[]),this.icons=this.concatOptions.icons||{},this.init()}get formattedValue(){const t=this.el.noUiSlider.get();if(Array.isArray(t)&&this.format){const o=[];return t.forEach((t=>{o.push(this.format.to(t))})),o}return this.format?this.format.to(t):t}processClasses(t){const o={};return Object.keys(t).forEach((i=>{i&&(o[i]=`${noUiSlider.cssClasses[i]} ${t[i]}`)})),o}init(){var t,o,i,e,n,s,r,l,a,d,c,h,u;this.createCollection(window.$hsRangeSliderCollection,this),("object"==typeof(null===(t=this.concatOptions)||void 0===t?void 0:t.formatter)?"thousandsSeparatorAndDecimalPoints"===(null===(i=null===(o=this.concatOptions)||void 0===o?void 0:o.formatter)||void 0===i?void 0:i.type):"thousandsSeparatorAndDecimalPoints"===(null===(e=this.concatOptions)||void 0===e?void 0:e.formatter))?this.thousandsSeparatorAndDecimalPointsFormatter():("object"==typeof(null===(n=this.concatOptions)||void 0===n?void 0:n.formatter)?"integer"===(null===(r=null===(s=this.concatOptions)||void 0===s?void 0:s.formatter)||void 0===r?void 0:r.type):"integer"===(null===(l=this.concatOptions)||void 0===l?void 0:l.formatter))?this.integerFormatter():"object"==typeof(null===(a=this.concatOptions)||void 0===a?void 0:a.formatter)&&((null===(c=null===(d=this.concatOptions)||void 0===d?void 0:d.formatter)||void 0===c?void 0:c.prefix)||(null===(u=null===(h=this.concatOptions)||void 0===h?void 0:h.formatter)||void 0===u?void 0:u.postfix))&&this.prefixOrPostfixFormatter(),noUiSlider.create(this.el,this.concatOptions),this.currentValue&&this.currentValue.length>0&&this.el.noUiSlider.on("update",(t=>{this.updateCurrentValue(t)})),this.concatOptions.disabled&&this.setDisabled(),this.icons.handle&&this.buildHandleIcon()}formatValue(t){var o,i,e,n,s,r,l,a,d;let c="";return"object"==typeof(null===(o=this.concatOptions)||void 0===o?void 0:o.formatter)?((null===(e=null===(i=this.concatOptions)||void 0===i?void 0:i.formatter)||void 0===e?void 0:e.prefix)&&(c+=null===(s=null===(n=this.concatOptions)||void 0===n?void 0:n.formatter)||void 0===s?void 0:s.prefix),c+=t,(null===(l=null===(r=this.concatOptions)||void 0===r?void 0:r.formatter)||void 0===l?void 0:l.postfix)&&(c+=null===(d=null===(a=this.concatOptions)||void 0===a?void 0:a.formatter)||void 0===d?void 0:d.postfix)):c+=t,c}integerFormatter(){var t;this.format={to:t=>this.formatValue(Math.round(t)),from:t=>Math.round(+t)},(null===(t=this.concatOptions)||void 0===t?void 0:t.tooltips)&&(this.concatOptions.tooltips=this.format)}prefixOrPostfixFormatter(){var t;this.format={to:t=>this.formatValue(t),from:t=>+t},(null===(t=this.concatOptions)||void 0===t?void 0:t.tooltips)&&(this.concatOptions.tooltips=this.format)}thousandsSeparatorAndDecimalPointsFormatter(){var t;this.format={to:t=>this.formatValue(new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(t)),from:t=>parseFloat(t.replace(/,/g,""))},(null===(t=this.concatOptions)||void 0===t?void 0:t.tooltips)&&(this.concatOptions.tooltips=this.format)}setDisabled(){this.el.setAttribute("disabled","disabled"),this.el.classList.add("disabled")}buildHandleIcon(){if(!this.icons.handle)return!1;const t=this.el.querySelector(".noUi-handle");if(!t)return!1;t.innerHTML=this.icons.handle}updateCurrentValue(t){this.currentValue&&0!==this.currentValue.length&&t.forEach(((t,o)=>{var i;const e=null===(i=this.currentValue)||void 0===i?void 0:i[o];if(!e)return;const n=this.format?this.format.to(t).toString():t.toString();e instanceof HTMLInputElement?e.value=n:e.textContent=n}))}destroy(){this.el.noUiSlider.destroy(),this.format=null,window.$hsRangeSliderCollection=window.$hsRangeSliderCollection.filter((({element:t})=>t.el!==this.el))}static getInstance(t,o=!1){const i=window.$hsRangeSliderCollection.find((o=>o.element.el===("string"==typeof t?document.querySelector(t):t)));return i?o?i:i.element.el:null}static autoInit(){window.$hsRangeSliderCollection||(window.$hsRangeSliderCollection=[]),window.$hsRangeSliderCollection&&(window.$hsRangeSliderCollection=window.$hsRangeSliderCollection.filter((({element:t})=>document.contains(t.el)))),document.querySelectorAll("[data-hs-range-slider]:not(.--prevent-on-load-init)").forEach((t=>{window.$hsRangeSliderCollection.find((o=>{var i;return(null===(i=null==o?void 0:o.element)||void 0===i?void 0:i.el)===t}))||new s(t)}))}}window.addEventListener("load",(()=>{s.autoInit()})),"undefined"!=typeof window&&(window.HSRangeSlider=s);const r=s;var l=e.A;export{l as default};