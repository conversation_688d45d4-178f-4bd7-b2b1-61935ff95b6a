import type { Metadata } from 'next'
import { Open_Sans } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

const openSans = Open_Sans({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800'],
  variable: '--font-open-sans',
})

export const metadata: Metadata = {
  title: 'VERICTUS - Edukacja i wsparcie po trudnych zdarzeniach',
  description: 'VERICTUS to edukacyjne zaplecze treściowe oferujące poradniki, artykuły, checklisty i case studies dla osób dotkniętych trudnymi zdarzeniami. Praktyczna wiedza i wsparcie.',
  keywords: 'edukacja po tragedii, poradniki po zgonach, wsparcie po pożarach, pomoc po powodzi, radzenie sobie z traumą, praktyczne porady, checklisty, case studies',
  authors: [{ name: 'VERICTUS' }],
  creator: 'VERICTUS',
  publisher: 'VERICTUS',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'pl_PL',
    url: 'https://verictus.pl',
    title: 'VERICTUS - Edukacja i wsparcie po trudnych zdarzeniach',
    description: 'Edukacyjne zaplecze treściowe z poradnikami i praktyczną wiedzą dla osób dotkniętych trudnymi zdarzeniami.',
    siteName: 'VERICTUS',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'VERICTUS - Edukacja i wsparcie po trudnych zdarzeniach',
    description: 'Edukacyjne zaplecze treściowe z poradnikami i praktyczną wiedzą.',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: '#0A2144',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pl" className={`${openSans.variable} smooth-scroll`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <script src="./node_modules/preline/dist/preline.js" async></script>
      </head>
      <body className={`${openSans.className} antialiased bg-base-50`}>
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
