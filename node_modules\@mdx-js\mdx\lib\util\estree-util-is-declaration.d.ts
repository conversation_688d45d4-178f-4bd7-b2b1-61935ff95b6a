/**
 * Check if `node` is a declaration.
 *
 * @param {Readonly<MaybeNamedClassDeclaration | MaybeNamedFunctionDeclaration | Node>} node
 *   Node to check.
 * @returns {node is Declaration | MaybeNamedClassDeclaration | MaybeNamedFunctionDeclaration}
 *   Whether `node` is a declaration.
 */
export function isDeclaration(node: Readonly<MaybeNamedClassDeclaration | MaybeNamedFunctionDeclaration | Node>): node is Declaration | MaybeNamedClassDeclaration | MaybeNamedFunctionDeclaration;
import type { MaybeNamedClassDeclaration } from 'estree-jsx';
import type { MaybeNamedFunctionDeclaration } from 'estree-jsx';
import type { Node } from 'estree-jsx';
import type { Declaration } from 'estree-jsx';
//# sourceMappingURL=estree-util-is-declaration.d.ts.map